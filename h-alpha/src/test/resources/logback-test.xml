<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </pattern>
        </layout>
    </appender>

    <logger name="org.springframework.boot" level="WARN"/>
    <logger name="org.springframework.cloud" level="WARN"/>
    <logger name="org.springframework.cloud.kubernetes" level="ERROR"/>
    <logger name="org.hibernate" level="WARN"/>

    <logger name="com.freedom" level="DEBUG"/>
    <logger name="com.yaowu" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="CONSOLE_APPENDER"/>
    </root>

</configuration>
