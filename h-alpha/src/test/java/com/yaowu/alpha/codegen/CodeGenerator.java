package com.yaowu.alpha.codegen;


import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class CodeGenerator {

    /**
     * 全局配置
     *
     * @param author       作者
     * @param outputDir    输出路径
     * @param fileOverride 是否覆盖已有文件
     * @return GlobalConfig
     */
    public static GlobalConfig globalConfig(String author, String outputDir, boolean fileOverride) {
        GlobalConfig.Builder config = new GlobalConfig.Builder();
        // 生成文件的输出目录
        config.outputDir(StringUtils.hasText(outputDir) ? outputDir : (System.getProperty("user.dir") + "/src/main/java"));
        // 开发人员
        config.author(StringUtils.hasText(author) ? author : "system");
        // 开启 swagger2 模式
        config.enableSwagger();
        // 时间类型对应策略
        config.dateType(DateType.TIME_PACK);
        return config.build();
    }

    /**
     * 数据源配置
     *
     * @param url      数据库地址
     * @param database 数据库
     * @param username 用户名
     * @param password 密码
     * @return DataSourceConfig
     */
    public static DataSourceConfig dataSourceConfig(String url, String database, String driverName, String username, String password) {
        String dbUrl = "jdbc:mysql://" + url + "/" + database + "?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8";
        DataSourceConfig.Builder config = new DataSourceConfig.Builder(dbUrl, username, password);
        return config.build();
    }

    /**
     * 包路径配置
     *
     * @param parent               指定父包名
     * @param subControllerPackage controller子包名
     * @param moduleName           模块名
     * @return PackageConfig
     */
    public static PackageConfig packageConfig(String parent, String subControllerPackage, String moduleName) {
        PackageConfig.Builder config = new PackageConfig.Builder();
        // 指定父包名
        config.parent(parent);
        // 指定实体包名
        config.entity("model.entity." + moduleName);
        // 指定控制器包名
        config.controller("controller." + (StringUtils.hasText(subControllerPackage) ? subControllerPackage : moduleName));
        // 指定service接口包名
        config.service("domain." + moduleName + ".service.batis.service");
        // service实现类包名
        config.serviceImpl("domain." + moduleName + ".service.batis.service.impl");
        // 指定mapper接口包名
        config.mapper("domain." + moduleName + ".service.batis.mapper");
        // 指定xml包名
        config.xml("domain." + moduleName + ".service.batis.mapper.xml");

        return config.build();
    }

    /**
     * 策略配置
     *
     * @param tablePrefix 表前缀
     * @param tableName   表名
     * @return 策略配置
     */
    public static StrategyConfig strategyConfig(String[] tablePrefix, String[] tableName) {
        StrategyConfig.Builder config = new StrategyConfig.Builder();
        config.enableSchema();
        // 表前缀
        config.addTablePrefix(tablePrefix);
        config.addInclude(tableName);
        return config.build();
    }


    public static TemplateConfig templateConfig() {
        return new TemplateConfig.Builder().build();
    }

    // 数据库ip及端号
    private final static String url = "rm-uf6io467mc774dvy6ho.mysql.rds.aliyuncs.com:3306";
    // 数据库名
    private final static String database = "h_alpha";
    // 数据库驱动
    private final static String driverName = "com.mysql.cj.jdbc.Driver";
    // 数据库用户名
    private final static String username = "g-dev";
    // 数据库密码
    private final static String password = "D6nIFWn48UP40SKy";
    // 作者
    private final static String author = "zhaozhiwei";
    // 生成文件输出路径, 可以为空，为空则输出到项目路径里 /src/main/java
    private final static String outputDir = "";
    // 是否覆盖已有文件
    private final static boolean fileOverride = true;
    // 父包名
    private final static String parentPackage = "com.yaowu.alpha";
    // 自定义controller子包名
    private final static String subControllerPackage = "v1.proxy";
    // 模块名，可以为空
    private final static String moduleName = "proxy";
    // 表前缀, 可指定多个
    private final static String[] tablePrefix = {"b_"};
    // 生成代码的表名，可指定多个
    private final static String[] tableNameArr = {
            "b_nurture_customer","b_nurture_customer_flow_node","b_nurture_customer_flow_state"
    };

    public static void main(String[] args) {
        DataSourceConfig dataSourceConfig = dataSourceConfig(url, database, driverName, username, password);
        AutoGenerator autoGenerator = new AutoGenerator(dataSourceConfig);
        GlobalConfig globalConfig = globalConfig(author, outputDir, fileOverride);
        autoGenerator.global(globalConfig);
        PackageConfig packageConfig = packageConfig(parentPackage, subControllerPackage, moduleName);
        autoGenerator.packageInfo(packageConfig);
        StrategyConfig strategyConfig = strategyConfig(tablePrefix, tableNameArr);
        autoGenerator.strategy(strategyConfig);
        autoGenerator.template(templateConfig());
        autoGenerator.execute();
    }
}
