package com.yaowu.alpha.codegen;


import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.builder.Controller;
import com.baomidou.mybatisplus.generator.config.builder.Entity;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.freedom.web.domain.BaseLogicTable;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public class CodeGeneratorByLiuzhenpeng {
    /**
     * 全局配置
     *
     * @param author       作者
     * @param outputDir    输出路径
     * @param fileOverride 是否覆盖已有文件
     * @return GlobalConfig
     */
    public static GlobalConfig globalConfig(String author, String outputDir, boolean fileOverride) {
        GlobalConfig.Builder builder = new GlobalConfig.Builder();
        builder.outputDir(StringUtils.hasText(outputDir) ? outputDir : (System.getProperty("user.dir") + "/src/main/java"));
        builder.author(StringUtils.hasText(author) ? author : "system");
        builder.dateType(DateType.TIME_PACK);
        builder.disableOpenDir();
        builder.enableSpringdoc();
        builder.enableSwagger();
        return builder.build();
    }

    /**
     * 数据源配置
     *
     * @param url      数据库地址
     * @param database 数据库
     * @param username 用户名
     * @param password 密码
     * @return DataSourceConfig
     */
    public static DataSourceConfig dataSourceConfig(String url, String database, String driverName, String username, String password) {
        String dbUrl = "jdbc:mysql://" + url + "/" + database + "?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8";
        DataSourceConfig.Builder builder = new DataSourceConfig.Builder(dbUrl, username, password);
        return builder.build();
    }

    /**
     * 包路径配置
     *
     * @param parent               指定父包名
     * @param subControllerPackage controller子包名
     * @param moduleName           模块名
     * @return PackageConfig
     */
    public static PackageConfig packageConfig(String parent, String subControllerPackage, String moduleName) {
        PackageConfig.Builder builder = new PackageConfig.Builder();
        builder.parent(parent);
        if (StringUtils.hasText(moduleName)) {
            builder.entity("model.entity." + moduleName);
            builder.controller("controller." + (StringUtils.hasText(subControllerPackage) ? subControllerPackage : moduleName));
            builder.service("domain." + moduleName + ".service.batis.service");
            builder.serviceImpl("domain." + moduleName + ".service.batis.service.impl");
            builder.mapper("domain." + moduleName + ".service.batis.mapper");
            builder.xml("resources." + moduleName);
        } else {
            builder.entity("model.entity");
            builder.controller("controller" + (StringUtils.hasText(subControllerPackage) ? subControllerPackage : ""));
            builder.service("service.batis.service");
            builder.serviceImpl("service.batis.service.impl");
            builder.mapper("service.batis.mapper");
            builder.xml("service.batis.mapper.xml");
        }
        return builder.build();
    }

    /**
     * 策略配置
     *
     * @param tablePrefix 表前缀
     * @param tableName   表名
     * @return 策略配置
     */
    public static StrategyConfig strategyConfig(String[] tablePrefix, String[] tableName) {
        StrategyConfig.Builder builder = new StrategyConfig.Builder();
        builder.addInclude(tableName);
        builder.addTablePrefix(tablePrefix);
        builder.enableSkipView();
        StrategyConfig config = builder.build();

        Entity.Builder entityBuilder = config.entityBuilder();
        entityBuilder.columnNaming(NamingStrategy.underline_to_camel);
        entityBuilder.naming(NamingStrategy.underline_to_camel);
        entityBuilder.superClass(BaseLogicTable.class);
        entityBuilder.enableLombok();
        entityBuilder.enableChainModel();
        entityBuilder.disableSerialVersionUID();
        entityBuilder.enableColumnConstant();
        entityBuilder.addSuperEntityColumns("id", "version", "delete_flag", "creator_id", "creator", "modifier_id", "modifier", "create_time", "update_time", "test_flag");

        Controller.Builder controllerBuilder = config.controllerBuilder();
        controllerBuilder.enableRestStyle();
        controllerBuilder.enableHyphenStyle();
        return config;
    }

//    public static TemplateConfig templateConfig() {
//        return new TemplateConfig();
//    }

    // 数据库ip及端号
    private final static String url = "rm-uf6io467mc774dvy6ho.mysql.rds.aliyuncs.com:3306";
    // 数据库名
    private final static String database = "h_alpha";
    // 数据库驱动
    private final static String driverName = "com.mysql.cj.jdbc.Driver";
    // 数据库用户名
    private final static String username = "g-dev";
    // 数据库密码
    private final static String password = "D6nIFWn48UP40SKy";
    // 作者
    private final static String author = "";
    // 生成文件输出路径, 可以为空，为空则输出到项目路径里 /src/main/java
    private final static String outputDir = "";
    // 是否覆盖已有文件
    private final static boolean fileOverride = true;
    // 父包名
    private final static String parentPackage = "com.yaowu.alpha";
    // 自定义controller子包名
    private final static String subControllerPackage = "v1.admin";
    // 模块名，可以为空
    private final static String moduleName = "";
    // 表前缀, 可指定多个
    private final static String[] tablePrefix = {"b_"};
    // 生成代码的表名，可指定多个
    private final static String[] tableNameArr = {
            "b_proxy_conversation"
    };

    public static void main(String[] args) {
        AutoGenerator autoGenerator = new AutoGenerator(dataSourceConfig(url, database, driverName, username, password));
        autoGenerator.global(globalConfig(author, outputDir, fileOverride))
                .packageInfo(packageConfig(parentPackage, subControllerPackage, moduleName))
                .strategy(strategyConfig(tablePrefix, tableNameArr));
        autoGenerator.execute();
    }
}
