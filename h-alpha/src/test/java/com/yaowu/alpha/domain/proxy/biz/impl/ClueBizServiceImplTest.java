package com.yaowu.alpha.domain.proxy.biz.impl;

import cn.hutool.json.JSONUtil;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.proxy.biz.IClueBizService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.extractor.CommonRequirementExtractorAgent;
import com.yaowu.alpha.model.dto.clue.ClueFeedbackDTO;
import com.yaowu.alpha.model.dto.clue.RemoteCluePageDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.clue.ClueChatRecordVO;
import com.yaowu.alpha.model.vo.clue.CluePageVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;


@SpringBootTest
public class ClueBizServiceImplTest {

    @Autowired
    private IClueBizService clueBizService;

    @Autowired
    private CommonRequirementExtractorAgent commonRequirementExtractorAgent;

    @Test
    @DisplayName("测试分页查询线索")
    void cluePage() {
        RemoteCluePageDTO cluePageDTO = new RemoteCluePageDTO();
        BasePage<CluePageVO> cluePageVOBasePage = clueBizService.cluePage(cluePageDTO);
        System.out.println(JSONUtil.toJsonStr(cluePageVOBasePage));
    }

    @Test
    @DisplayName("测试线索反馈")
    void testClueFeedBack() {
        ClueFeedbackDTO cluePageDTO = new ClueFeedbackDTO();
        cluePageDTO.setId(1907698737775841281L);
        cluePageDTO.setFeedbackType(2);
        Boolean feedback = clueBizService.feedback(cluePageDTO);
        System.out.println(JSONUtil.toJsonStr(feedback));
    }

    @Test
    @DisplayName("查询线索聊天记录")
    void testGetChatRecords() {
//        Long clueId = 1915243538175467521L;
//        List<ClueChatRecordVO> chatRecords = clueBizService.getChatRecords(clueId);
//        System.out.println(JSONUtil.toJsonStr(chatRecords));
    }

    @Test
    @DisplayName("测试提取需求的方法函数")
    void testExact(){
        Long extractorSessionId = 1915240143771250690L;
        String existingTriggerMessagesJson = "{}";
        boolean  isNewRequirement = true;
        String chatAgentAppKey = "419bc97969494a15bb10f4d69a0e88a9";
        ProxyAccount proxyAccount = new ProxyAccount();
        String exaction = commonRequirementExtractorAgent.collectTriggerMessages(extractorSessionId, existingTriggerMessagesJson, isNewRequirement, proxyAccount);
        System.out.println(JSONUtil.toJsonStr(exaction));
    }


}