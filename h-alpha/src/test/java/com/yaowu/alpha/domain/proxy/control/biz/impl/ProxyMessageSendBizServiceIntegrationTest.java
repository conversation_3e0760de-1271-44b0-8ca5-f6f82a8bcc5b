package com.yaowu.alpha.domain.proxy.control.biz.impl;

import com.yaowu.alpha.domain.proxy.control.biz.IProxyMessageSendBizService;
import com.yaowu.alpha.enums.proxy.MessageTypeEnum;
import com.yaowu.alpha.model.dto.proxy.SendMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

/**
 * 代理消息发送业务集成测试类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
class ProxyMessageSendBizServiceIntegrationTest {

    @Autowired
    private IProxyMessageSendBizService proxyMessageSendBizService;

    private static final String SENDER_ID = "wxid_bbqex25ubwhf22";
    private static final String RECEIVER_ID = "wxid_gfg0hmkzbfpg22";

    @Test
    void sendTextMessage_Integration_Test() {
        // 准备文本消息DTO
        SendMessageDTO textMessageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.TEXT.getValue())
                .setAccountId(1L)  // 实际环境中的账号ID
                .setReceiverId(RECEIVER_ID)
                .setContent("这是一条集成测试消息")
                .setIsGroup(false);

        // 执行发送
        log.info("开始发送文本消息测试: {}", textMessageDTO);
        proxyMessageSendBizService.sendMessage(textMessageDTO);
        log.info("文本消息发送完成");
    }

    @Test
    void sendImageMessage_Integration_Test() {
        // 准备图片消息DTO
        SendMessageDTO imageMessageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.IMAGE.getValue())
                .setAccountId(1L)  // 实际环境中的账号ID
                .setReceiverId(RECEIVER_ID)
                .setMediaUrl("https://example.com/test-image.jpg")  // 替换为实际可访问的图片URL
                .setIsGroup(false);

        // 执行发送
        log.info("开始发送图片消息测试: {}", imageMessageDTO);
        proxyMessageSendBizService.sendMessage(imageMessageDTO);
        log.info("图片消息发送完成");
    }

    @Test
    void sendBatchTextMessage_Integration_Test() {
        // 准备批量文本消息DTO
        SendMessageDTO batchTextMessageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.TEXT.getValue())
                .setAccountId(1L)  // 实际环境中的账号ID
                .setReceiverId(RECEIVER_ID)
                .setContent("这是一条批量发送的集成测试消息")
                .setIsGroup(true)
                .setAtUserIds(Arrays.asList(RECEIVER_ID, SENDER_ID));

        // 执行发送
        log.info("开始发送批量文本消息测试: {}", batchTextMessageDTO);
        proxyMessageSendBizService.sendMessage(batchTextMessageDTO);
        log.info("批量文本消息发送完成");
    }


    @Test
    void sendMediaMessage_Integration_Test() {
        // 准备文本消息DTO
        SendMessageDTO messageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.VIDEO_FILE.getValue())
                .setAccountId(1L)  // 实际环境中的账号ID
                .setReceiverId(RECEIVER_ID)
                .setMediaUrl("https://oss-biz-static.jxxqtech.com/biz-common/g-notice/pn/voice/f2b5bb765b4d93f79924611f1a7bccc7.wav?Expires=**********&OSSAccessKeyId=LTAI5t7zseVrLGbw5cLPZ8zc&Signature=KbMGJ9Lbka6lfPOICPsBbXY1Lcg%3D") //发送语音文件 | 多媒体文件 | 图片文件
                .setContent("多媒体消息发送")
                .setIsGroup(false);

        // 执行发送
        log.info("开始发送发送语音文件 | 多媒体文件 | 图片文件 消息测试: {}", messageDTO);
        proxyMessageSendBizService.sendMessage(messageDTO);
        log.info("多媒体消息发送完成");
    }
} 