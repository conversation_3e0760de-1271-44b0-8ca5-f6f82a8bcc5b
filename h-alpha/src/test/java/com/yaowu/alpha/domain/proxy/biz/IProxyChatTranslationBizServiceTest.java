package com.yaowu.alpha.domain.proxy.biz;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.AlphaApplication;
import com.yaowu.alpha.model.dto.translation.TranslateChatMessageDTO;
import com.yaowu.alpha.model.dto.translation.TranslateMessageDTO;
import com.yaowu.alpha.model.vo.translation.LanguageTypeVO;
import com.yaowu.alpha.model.vo.translation.TranslationResultVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;



@SpringBootTest(classes = AlphaApplication.class)
public class IProxyChatTranslationBizServiceTest {

    @Autowired
    private IProxyChatTranslationBizService chatTranslationBizService;

    @Test
    void translateMessage() throws InterruptedException {
        TranslateMessageDTO translateMessageDTO = new TranslateMessageDTO();
        translateMessageDTO.setTargetLanguageType(3);
        translateMessageDTO.setMessage("你好吗？");

        Flux<String> stringFlux = chatTranslationBizService.translateMessage(translateMessageDTO);
        // 创建一个 CountDownLatch 来等待流完成
        CountDownLatch latch = new CountDownLatch(1);
        StringBuilder result = new StringBuilder();

        System.out.println("开始订阅翻译流...");

        stringFlux.subscribe(
                // onNext - 处理每个元素
                chunk -> {
                    System.out.println("收到翻译片段: " + chunk);
                    result.append(chunk);
                },
                // onError - 处理错误
                error -> {
                    System.err.println("翻译出错: " + error.getMessage());
                    error.printStackTrace();
                    latch.countDown();
                },
                // onComplete - 处理完成
                () -> {
                    System.out.println("翻译完成，完整结果: " + result);
                    latch.countDown();
                }
        );

        System.out.println("等待翻译完成...");

        // 等待流完成，最多等待30秒
        boolean completed = latch.await(30, TimeUnit.SECONDS);

        if (!completed) {
            System.out.println("翻译超时，未收到完成信号");
        }
    }

    /**
     * {
     *     "messageId": 1907700373575372801,
     *     "message": "",
     *     "targetLanguageType": 2,
     *     "proxyAccountId": 1907644609615699969,
     *     "chatUserProxyId": "5169da50a5aa4bdfa6d7f69c946b5adc"
     * }
     */
    @Test
    void translateChatMessage() {
        TranslateChatMessageDTO translateChatMessageDTO = new TranslateChatMessageDTO();
        translateChatMessageDTO.setMessageId(1907700373575372801L);
        translateChatMessageDTO.setMessage("给我报价");
        translateChatMessageDTO.setTargetLanguageType(2);
        translateChatMessageDTO.setProxyAccountId(1907644609615699969L);
        translateChatMessageDTO.setChatUserProxyId("5169da50a5aa4bdfa6d7f69c946b5adc");
        TranslationResultVO translationResultVO = chatTranslationBizService.translateChatMessage(translateChatMessageDTO);
        System.out.println(JSONUtil.toJsonStr(translationResultVO));
    }

    @Test
    void getSupportedLanguages() {
        List<LanguageTypeVO> supportedLanguages = chatTranslationBizService.getSupportedLanguages();
        System.out.println(JSONUtil.toJsonStr(supportedLanguages));
    }
}