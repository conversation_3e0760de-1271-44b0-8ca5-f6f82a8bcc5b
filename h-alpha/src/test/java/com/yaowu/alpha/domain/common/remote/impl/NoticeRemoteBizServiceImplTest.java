package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.common.remote.INoticeRemoteBizService;
import com.yaowu.notice.model.vo.shortlink.RemoteShortLinkVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class NoticeRemoteBizServiceImplTest {

    @Autowired
    private INoticeRemoteBizService noticeRemoteBizService;

    @Test
    void generateShortLink() {
        RemoteShortLinkVO shortLink = noticeRemoteBizService.generateShortLink(
                "https://qamechlabs.jxxqtech.com/alpha/dashboard/clue", 7);
        System.out.println(JSONUtil.toJsonStr(shortLink));
    }
}