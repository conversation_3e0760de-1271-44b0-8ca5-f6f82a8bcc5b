package com.yaowu.alpha.domain.proxy.biz.impl;

import com.yaowu.alpha.domain.proxy.biz.IUserBusinessAccountBizService;
import com.yaowu.alpha.model.dto.proxy.OrderPurchasedBenefitsDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025/4/15-19:46
 */
@SpringBootTest
public class UserBusinessAccountBizServiceImplTest {

    @Autowired
    private IUserBusinessAccountBizService userBusinessAccountService;

    @Test
    public void test_orderPurchasedBenefits() {
        OrderPurchasedBenefitsDTO dto = new OrderPurchasedBenefitsDTO();
        dto.setOrderId(1912120789509660674L);
        userBusinessAccountService.writeBenifitsByOrder(dto);
    }
}
