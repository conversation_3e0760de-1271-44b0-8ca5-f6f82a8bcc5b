package com.yaowu.alpha.domain.proxy.control.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class IProxyAccountFriendBizServiceTest {

    @Autowired
    private IProxyAccountFriendBizService proxyAccountFriendBizService;


    @Test
    void refreshFriendInteractionTime() {
        proxyAccountFriendBizService.refreshFriendInteractionTime();
    }
}