package com.yaowu.alpha.domain.proxy.control.biz.impl.agent.extractor;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Sets;
import com.yaowu.alpha.domain.llm.biz.ILLMAgentBizService;
import com.yaowu.alpha.enums.llm.AgentMessageRoleTypeEnum;
import com.yaowu.alpha.model.bo.proxy.GenericRequirementData;
import com.yaowu.alpha.model.dto.agent.ExtractorAgentEditDTO;
import com.yaowu.alpha.model.entity.agent.AgentChatMessage;
import com.yaowu.alpha.model.entity.agent.AgentSession;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;
import com.yaowu.alpha.utils.common.StreamUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AbstractCustomerRequirementExtractorAgentTest {

    @Mock
    private ILLMAgentBizService llmAgentBizService;

    @InjectMocks
    private TestCustomerRequirementExtractorAgent extractorAgent;

    @BeforeEach
    public void setup() {
        // 使用反射设置私有字段
        ReflectionTestUtils.setField(extractorAgent, "llmAgentBizService", llmAgentBizService);

        // 初始化测试对象
        Mockito.lenient().when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(anyLong(), any(), any(), anyLong()))
                .thenReturn(new ArrayList<>());

        // 模拟会话查询
        AgentSession mockSession = new AgentSession();
        mockSession.setId(123L);
        mockSession.setFromUser("testUser");
        mockSession.setToUser("testAssistant");
        mockSession.setAgentAppKey("testAppKey");
        mockSession.setTenantId(1L);

        Mockito.lenient().when(llmAgentBizService.getSessionById(anyLong()))
                .thenReturn(mockSession);

        Mockito.lenient().when(llmAgentBizService.getSession(anyLong(), anyString(), anyString(), anyString()))
                .thenReturn(mockSession);
    }

    @Test
    public void testCollectTriggerMessages_NewRequirement() {
        // 准备测试数据
        Long agentSessionId = 123L;

        // 模拟最新的两条消息
        List<AgentChatMessage> messages = new ArrayList<>();
        AgentChatMessage aiMessage = new AgentChatMessage();
        aiMessage.setId(102L);
        aiMessage.setChatRole(AgentMessageRoleTypeEnum.ASSISTANT.getValue());
        aiMessage.setMessageTimestamp(System.currentTimeMillis());

        AgentChatMessage userMessage = new AgentChatMessage();
        userMessage.setId(101L);
        userMessage.setChatRole(AgentMessageRoleTypeEnum.USER.getValue());
        userMessage.setMessageTimestamp(System.currentTimeMillis() - 1000);

        messages.add(aiMessage);
        messages.add(userMessage);

        // 创建预期的消息ID集合
        Set<Long> expectedIds = new HashSet<>();
        expectedIds.add(101L);
        expectedIds.add(102L);

        // 使用MockedStatic模拟StreamUtil.toSet方法
        try (MockedStatic<StreamUtil> streamUtilMock = Mockito.mockStatic(StreamUtil.class)) {
            // 模拟StreamUtil.toSet方法的行为
            streamUtilMock.when(() -> StreamUtil.toSet(any(), any())).thenReturn(expectedIds);

            // 模拟llmAgentBizService的行为
            when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                    eq(agentSessionId),
                    eq(Sets.newHashSet(AgentMessageRoleTypeEnum.USER.getValue(), AgentMessageRoleTypeEnum.ASSISTANT.getValue())),
                    isNull(),
                    eq(2L)
            )).thenReturn(messages);

            // 调用被测试方法
            String result = extractorAgent.testCollectTriggerMessages(agentSessionId, null, true);

            // 验证结果
            assertNotNull(result);
            JSONObject jsonObject = JSONUtil.parseObj(result);
            assertEquals(2, jsonObject.size());
            assertTrue(jsonObject.containsKey("101"));
            assertTrue(jsonObject.containsKey("102"));
            assertEquals(true, jsonObject.get("101"));
            assertEquals(true, jsonObject.get("102"));
        }
    }

    @Test
    public void testCollectTriggerMessages_UpdateRequirement() {
        // 准备测试数据
        Long agentSessionId = 123L;
        String existingJson = "{\"101\":true,\"102\":true}";

        // 模拟新消息
        List<AgentChatMessage> messages = new ArrayList<>();
        AgentChatMessage aiMessage1 = new AgentChatMessage();
        aiMessage1.setId(104L);
        aiMessage1.setChatRole(AgentMessageRoleTypeEnum.ASSISTANT.getValue());
        aiMessage1.setMessageTimestamp(System.currentTimeMillis());

        AgentChatMessage userMessage1 = new AgentChatMessage();
        userMessage1.setId(103L);
        userMessage1.setChatRole(AgentMessageRoleTypeEnum.USER.getValue());
        userMessage1.setMessageTimestamp(System.currentTimeMillis() - 1000);

        AgentChatMessage aiMessage2 = new AgentChatMessage();
        aiMessage2.setId(102L); // 已存在的消息ID
        aiMessage2.setChatRole(AgentMessageRoleTypeEnum.ASSISTANT.getValue());
        aiMessage2.setMessageTimestamp(System.currentTimeMillis() - 2000);

        messages.add(aiMessage1);
        messages.add(userMessage1);
        messages.add(aiMessage2);

        // 创建预期的消息ID集合
        Set<Long> expectedIds = new HashSet<>();
        expectedIds.add(102L);
        expectedIds.add(103L);
        expectedIds.add(104L);

        // 使用MockedStatic模拟StreamUtil.toSet方法
        try (MockedStatic<StreamUtil> streamUtilMock = Mockito.mockStatic(StreamUtil.class)) {
            // 模拟StreamUtil.toSet方法的行为
            streamUtilMock.when(() -> StreamUtil.toSet(any(), any())).thenReturn(expectedIds);

            // 模拟llmAgentBizService的行为
            when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                    eq(agentSessionId),
                    eq(Sets.newHashSet(AgentMessageRoleTypeEnum.USER.getValue(), AgentMessageRoleTypeEnum.ASSISTANT.getValue())),
                    isNull(),
                    eq(50L)
            )).thenReturn(messages);

            // 调用被测试方法
            String result = extractorAgent.testCollectTriggerMessages(agentSessionId, existingJson, false);

            // 验证结果
            assertNotNull(result);
            JSONObject jsonObject = JSONUtil.parseObj(result);
            assertEquals(4, jsonObject.size()); // 101, 102, 103, 104
            assertTrue(jsonObject.containsKey("101"));
            assertTrue(jsonObject.containsKey("102"));
            assertTrue(jsonObject.containsKey("103"));
            assertTrue(jsonObject.containsKey("104"));
        }
    }

    @Test
    public void testCollectTriggerMessages_EmptyMessages() {
        // 准备测试数据
        Long agentSessionId = 123L;

        // 使用MockedStatic模拟StreamUtil.toSet方法
        try (MockedStatic<StreamUtil> streamUtilMock = Mockito.mockStatic(StreamUtil.class)) {
            // 模拟StreamUtil.toSet方法的行为，返回空集合
            streamUtilMock.when(() -> StreamUtil.toSet(any(), any())).thenReturn(new HashSet<>());

            // 模拟空消息列表
            when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                    eq(agentSessionId),
                    any(),
                    isNull(),
                    anyLong()
            )).thenReturn(new ArrayList<>());

            // 调用被测试方法
            String result = extractorAgent.testCollectTriggerMessages(agentSessionId, null, true);

            // 验证结果
            assertEquals("{}", result);
        }
    }

    @Test
    public void testCollectTriggerMessages_InvalidExistingJson() {
        // 准备测试数据
        Long agentSessionId = 123L;
        String invalidJson = "invalid json";

        // 模拟新消息
        List<AgentChatMessage> messages = new ArrayList<>();
        AgentChatMessage message = new AgentChatMessage();
        message.setId(101L);
        message.setMessageTimestamp(System.currentTimeMillis()); // 设置时间戳以避免 NullPointerException
        messages.add(message);

        // 创建预期的消息ID集合
        Set<Long> expectedIds = new HashSet<>();
        expectedIds.add(101L);

        // 使用MockedStatic模拟StreamUtil.toSet方法
        try (MockedStatic<StreamUtil> streamUtilMock = Mockito.mockStatic(StreamUtil.class)) {
            // 模拟StreamUtil.toSet方法的行为
            streamUtilMock.when(() -> StreamUtil.toSet(any(), any())).thenReturn(expectedIds);

            // 模拟llmAgentBizService的行为
            when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                    eq(agentSessionId),
                    any(),
                    isNull(),
                    anyLong()
            )).thenReturn(messages);

            // 调用被测试方法
            String result = extractorAgent.testCollectTriggerMessages(agentSessionId, invalidJson, false);

            // 验证结果 - 应该只包含新消息，忽略无效的JSON
            JSONObject jsonObject = JSONUtil.parseObj(result);
            assertEquals(1, jsonObject.size());
            assertTrue(jsonObject.containsKey("101"));
        }
    }

    // 测试异常情况
    @Test
    public void testCollectTriggerMessages_ExceptionHandling() {
        // 准备测试数据
        Long agentSessionId = 123L;

        // 使用MockedStatic模拟StreamUtil.toSet方法
        try (MockedStatic<StreamUtil> streamUtilMock = Mockito.mockStatic(StreamUtil.class)) {
            // 模拟StreamUtil.toSet方法的行为
            streamUtilMock.when(() -> StreamUtil.toSet(any(), any())).thenReturn(new HashSet<>());

            // 模拟异常
            when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                    eq(agentSessionId),
                    any(),
                    isNull(),
                    anyLong()
            )).thenThrow(new RuntimeException("Test exception"));

            // 调用被测试方法
            String result = extractorAgent.testCollectTriggerMessages(agentSessionId, null, true);

            // 验证结果 - 应该返回空对象，不抛出异常
            assertEquals("{}", result);
        }
    }

    @Test
    public void testParseExistingMessageIds() {
        // 测试有效的JSON
        String validJson = "{\"101\":true,\"102\":true}";
        Set<Long> result = extractorAgent.testParseExistingMessageIds(validJson);
        assertEquals(2, result.size());
        assertTrue(result.contains(101L));
        assertTrue(result.contains(102L));

        // 测试空JSON
        String emptyJson = "{}";
        result = extractorAgent.testParseExistingMessageIds(emptyJson);
        assertTrue(result.isEmpty());

        // 测试null
        result = extractorAgent.testParseExistingMessageIds(null);
        assertTrue(result.isEmpty());

        // 测试无效JSON
        String invalidJson = "invalid json";
        result = extractorAgent.testParseExistingMessageIds(invalidJson);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildTriggerMessagesJson() {
        // 测试非空集合
        Set<Long> messageIds = new HashSet<>();
        messageIds.add(101L);
        messageIds.add(102L);

        String result = extractorAgent.testBuildTriggerMessagesJson(messageIds);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        assertEquals(2, jsonObject.size());
        assertTrue(jsonObject.containsKey("101"));
        assertTrue(jsonObject.containsKey("102"));
        assertEquals(true, jsonObject.get("101"));
        assertEquals(true, jsonObject.get("102"));

        // 测试空集合
        messageIds.clear();
        result = extractorAgent.testBuildTriggerMessagesJson(messageIds);
        assertEquals("{}", result);

        // 测试null
        result = extractorAgent.testBuildTriggerMessagesJson(null);
        assertEquals("{}", result);
    }

    @Test
    public void testInitializeMessageIds() {
        // 测试新增需求时，应该返回空集合
        Set<Long> result = extractorAgent.testInitializeMessageIds(null, true);
        assertTrue(result.isEmpty());

        // 测试更新需求时，应该解析已有的消息ID
        result = extractorAgent.testInitializeMessageIds("{\"101\":true,\"102\":true}", false);
        assertEquals(2, result.size());
        assertTrue(result.contains(101L));
        assertTrue(result.contains(102L));

        // 测试无效的JSON
        result = extractorAgent.testInitializeMessageIds("invalid json", false);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testPrepareMessageContext() {
        // 准备测试数据
        Long agentSessionId = 123L;
        Set<Long> messageIds = new HashSet<>();

        // 模拟消息列表
        List<AgentChatMessage> messages = new ArrayList<>();
        AgentChatMessage message = new AgentChatMessage();
        message.setId(101L);
        message.setMessageTimestamp(System.currentTimeMillis());
        messages.add(message);

        // 模拟llmAgentBizService的行为
        when(llmAgentBizService.listSessionMessagesOrderByTimestampDesc(
                eq(agentSessionId),
                any(),
                isNull(),
                anyLong()
        )).thenReturn(messages);

        // 测试有效的上下文
        AbstractCustomerRequirementExtractorAgent.MessageContext context =
                extractorAgent.testPrepareMessageContext(agentSessionId, true, messageIds);

        // 验证结果
        assertTrue(context.isValid());
        assertEquals(messageIds, context.getMessageIds());
        assertNotNull(context.getTimeRange());
        assertTrue(context.getTimeRange().isValid());
    }

    // 用于测试的子类
    private static class TestCustomerRequirementExtractorAgent extends AbstractCustomerRequirementExtractorAgent<GenericRequirementData> {
        // 暴露protected方法用于测试
        public String testCollectTriggerMessages(Long agentSessionId, String existingTriggerMessagesJson, boolean isNewRequirement) {
            ProxyAccount config =new ProxyAccount();
            return collectTriggerMessages(agentSessionId, existingTriggerMessagesJson, isNewRequirement, config);
        }

        // 实现抽象方法
        @Override
        public Boolean support(String appKey, ProxyChatMessage message) {
            return false;
        }

        @Override
        protected boolean checkRequirementFactor(ExtractorAgentEditDTO extractor, Map<String, Object> requirementData) {
            return false;
        }

        @Override
        protected void fillDefaultRequirementData(ExtractorAgentEditDTO extractor, Map<String, Object> requirementData, String friendProxyId) {
            // 空实现
        }

        @Override
        protected boolean checkRequirementComplete(ExtractorAgentEditDTO extractor, Map<String, Object> requirementData) {
            return false;
        }

        // 暴露protected方法用于测试
        public Set<Long> testParseExistingMessageIds(String existingTriggerMessagesJson) {
            return parseExistingMessageIds(existingTriggerMessagesJson);
        }

        // 暴露protected方法用于测试
        public String testBuildTriggerMessagesJson(Set<Long> messageIds) {
            return buildTriggerMessagesJson(messageIds);
        }

        // 测试方法，不直接调用私有方法
        public Set<Long> testInitializeMessageIds(String existingTriggerMessagesJson, boolean isNewRequirement) {
            // 模拟 initializeMessageIds 方法的行为
            Set<Long> messageIds = new HashSet<>();
            if (!isNewRequirement) {
                messageIds.addAll(parseExistingMessageIds(existingTriggerMessagesJson));
            }
            return messageIds;
        }

        public MessageContext testPrepareMessageContext(Long extractorSessionId, boolean isNewRequirement, Set<Long> messageIds) {
            // 返回一个有效的测试上下文
            TimeRange timeRange = new TimeRange(System.currentTimeMillis(), System.currentTimeMillis() + 1000);
            return new MessageContext(messageIds, 123L, timeRange, true);
        }
    }
}
