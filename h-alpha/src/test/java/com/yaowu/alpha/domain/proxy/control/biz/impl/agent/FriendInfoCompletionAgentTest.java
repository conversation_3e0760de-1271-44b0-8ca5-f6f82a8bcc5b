package com.yaowu.alpha.domain.proxy.control.biz.impl.agent;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class FriendInfoCompletionAgentTest {

    @Autowired
    private FriendInfoCompletionAgent agent;

    @Test
    void complete() {
        long accountId = 1922956260164845570L;
        long friendId = 1931172512672706561L;
        String friendInfoDoc = """
                title: KeenEdge Pet Grooming in your home
                category: Pet groomer
                """;
        agent.complete(accountId, friendId, friendInfoDoc);

    }
}