package com.yaowu.alpha.domain.common.biz.impl;

import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.alpha.enums.common.AdLeadsCaptureSourceEnum;
import com.yaowu.alpha.model.dto.common.SubmitAdLeadsCaptureDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@SpringBootTest
public class AdLeadsCaptureBizServiceImplTest {


    @Autowired
    private AdLeadsCaptureBizServiceImpl adLeadsCaptureBizService;

    @Test
    void buildWeChatRoobotMsg_ShouldFormatMessageCorrectly() {
        // 准备测试数据
        SubmitAdLeadsCaptureDTO dto = new SubmitAdLeadsCaptureDTO();
        dto.setName("王五");
        dto.setContact("+86-13500000000");
        dto.setRequestDescription("我想租赁一台挖掘机");
        dto.setSource(AdLeadsCaptureSourceEnum.GOOGLE_ADS);
        adLeadsCaptureBizService.submit(dto);
    }
}
