package com.yaowu.alpha.domain.common.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class ITagBizServiceTest {

    @Autowired
    private ITagBizService tagBizService;

    @Test
    void listTagsByInstanceId() {
        List<Long> tagIds = CollUtil.newArrayList(1927337188700495873L, 1927288262886727681L);
        List<String> instanceIds = tagBizService.listTagInstanceIdsIntersection(tagIds);
        System.out.println(JSONUtil.toJsonStr(instanceIds));
    }
}