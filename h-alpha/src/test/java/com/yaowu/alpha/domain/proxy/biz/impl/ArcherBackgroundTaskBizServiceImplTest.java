package com.yaowu.alpha.domain.proxy.biz.impl;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.proxy.biz.IArcherBackgroundTaskBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.model.dto.friend.CompanyBackgroundCheckDTO;
import com.yaowu.alpha.model.dto.proxy.ArcherTaskResultDTO;
import com.yaowu.alpha.model.vo.friend.CompanyBackgroundCheckVO;
import com.yaowu.alpha.model.vo.proxy.CompanyBackgroundTaskVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;



/**
 * Archer背调任务业务服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
public class ArcherBackgroundTaskBizServiceImplTest {

    @Autowired
    private IArcherBackgroundTaskBizService iArcherBackgroundTaskBizService;

    @Autowired
    private IProxyAccountFriendBizService proxyAccountFriendBizService;
    @Test
    @DisplayName("获取最新的待处理任务列表")
    public void testGetPendingTasks() {

    }

    @Test
    @DisplayName("获取最新的待处理任务单个对象")
    public void testGetPendingTask() {
        CompanyBackgroundTaskVO pendingTask = iArcherBackgroundTaskBizService.getPendingTask();
        if (pendingTask != null) {
            System.out.println("最新待处理任务: " + JSONUtil.toJsonStr(pendingTask));
        } else {
            System.out.println("当前没有待处理的背调任务");
        }
    }

    @Test
    @DisplayName("处理Archer任务执行结果 - 成功")
    public void testHandleTaskResult_Success() {
        ArcherTaskResultDTO archerTaskResultDTO = new ArcherTaskResultDTO();
        archerTaskResultDTO.setTaskId(1L);
        archerTaskResultDTO.setTenantId(100L);
        archerTaskResultDTO.setCompanyName("测试公司");
        archerTaskResultDTO.setSuccess(true);
        archerTaskResultDTO.setBackgroundData("测试背调数据");

        iArcherBackgroundTaskBizService.handleTaskResult(archerTaskResultDTO);
        System.out.println("成功任务结果处理完成");
    }

    @Test
    @DisplayName("处理Archer任务执行结果 - 失败")
    public void testHandleTaskResult_Failed() {
        ArcherTaskResultDTO archerTaskResultDTO = new ArcherTaskResultDTO();
        archerTaskResultDTO.setTaskId(2L);
        archerTaskResultDTO.setTenantId(100L);
        archerTaskResultDTO.setCompanyName("失败测试公司");
        archerTaskResultDTO.setSuccess(false);
        archerTaskResultDTO.setErrorMessage("网络超时，无法获取背调数据");
        archerTaskResultDTO.setBackgroundData(null);

        iArcherBackgroundTaskBizService.handleTaskResult(archerTaskResultDTO);
        System.out.println("失败任务结果处理完成");
    }

    @Test
    @DisplayName("发起公司背调")
    public void testStartBackgroundCheck() {
        CompanyBackgroundCheckDTO companyBackgroundCheckDTO = new CompanyBackgroundCheckDTO();
        companyBackgroundCheckDTO.setCompanyName("测试公司");
        companyBackgroundCheckDTO.setOriginFriendId(123L);
        proxyAccountFriendBizService.startBackgroundCheck(companyBackgroundCheckDTO);
    }

    @Test
    @DisplayName("查询公司背调结果")
    public void testGetBackgroundCheckResult() {
        String companyName = "ADITYA BIRLA FASHION AND RETAIL LIMITED";
        String industry = "服装行业";
        CompanyBackgroundCheckVO backgroundCheckResult = proxyAccountFriendBizService.getBackgroundCheckResult(companyName, industry);
        System.out.println(JSONUtil.toJsonStr(backgroundCheckResult));
    }


}
