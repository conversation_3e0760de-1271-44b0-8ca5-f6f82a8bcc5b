package com.yaowu.alpha.domain.proxy.control.biz;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.agent.impl.chat.ChatAgent;
import com.yaowu.alpha.domain.llm.biz.ILLMAgentBizService;
import com.yaowu.alpha.domain.llm.biz.impl.LLMAgentBizServiceImpl;
import com.yaowu.alpha.domain.llm.service.batis.service.IAgentApplicationService;
import com.yaowu.alpha.domain.proxy.biz.IProxyWhatsAppMessageBizService;
import com.yaowu.alpha.domain.proxy.biz.IWeToolFacadeBizService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyTaskService;
import com.yaowu.alpha.model.bo.agent.AgentPromptTemplate;
import com.yaowu.alpha.model.bo.llm.LLMConfig;
import com.yaowu.alpha.model.bo.llm.LLMToolsConfig;
import com.yaowu.alpha.model.dto.llm.agent.LLMAgentChatSubmitDTO;
import com.yaowu.alpha.model.dto.proxy.control.CreateWhatsappAccountTaskDTO;
import com.yaowu.alpha.model.dto.proxy.control.LogoutWhatsappAccountTaskDTO;
import com.yaowu.alpha.model.dto.proxy.control.QueryWhatsAppMsgStatusTask;
import com.yaowu.alpha.model.dto.proxy.control.RevokeWhatsAppMsgTask;
import com.yaowu.alpha.model.dto.proxy.control.SearchWhatsappContactTaskDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolCommonRequest;
import com.yaowu.alpha.model.entity.agent.AgentApplication;
import com.yaowu.alpha.model.entity.agent.AgentChatMessage;
import com.yaowu.alpha.model.entity.agent.Function;
import com.yaowu.alpha.model.entity.agent.OpenAiToolCall;
import com.yaowu.alpha.model.vo.llm.agent.AgentChatSubmitVO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinRevokeWhatsappMsgRequestDTO.RevokeWhatsappMsgData;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolCommonResponseData;
import com.yaowu.alpha.model.vo.remote.llm.RemoteLLMAgentChatVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;


@SpringBootTest
public class IProxyTaskBizServiceTest {

    @Autowired
    private IProxyTaskBizService proxyTaskBizService;

    @Autowired
    private IWeToolFacadeBizService weToolFacadeBizService;

    @Autowired
    private IAgentApplicationService agentApplicationService;

    @Autowired
    private LLMAgentBizServiceImpl llmAgentBizService;

    @Autowired
    private ChatAgent chatAgent;


    @Test
    void addAgentApplicationService() {
        AgentApplication application = new AgentApplication();
        application.setPrompt("123");
        application.setAppKey("123");
        AgentPromptTemplate agentPromptTemplate = new AgentPromptTemplate();
        application.setPromptTemplate(agentPromptTemplate);
        LLMConfig llmConfig = new LLMConfig();
        LLMToolsConfig llmToolsConfig = new LLMToolsConfig();
        llmToolsConfig.setDefinition("{\n" +
                "        \"name\": \"tools-zhipu-mtl_customer_bot_func-send_message_by_user_input\",\n" +
                "        \"description\": \"- 功能：根据用户输入发送不同类型的消息- 入参：- 代理账号ID（account_id）- 接收方ID（receiver_id）- 文件唯一key（file_key）\",\n" +
                "        \"parameters\": {\n" +
                "            \"type\": \"object\",\n" +
                "            \"properties\": {\n" +
                "                \"account_id\": {\n" +
                "                    \"type\": \"integer\",\n" +
                "                    \"description\": \"代理账号ID\"\n" +
                "                },\n" +
                "                \"receiver_id\": {\n" +
                "                    \"type\": \"string\",\n" +
                "                    \"description\": \"接收方ID\"\n" +
                "                },\n" +
                "                \"file_key\": {\n" +
                "                    \"type\": \"string\",\n" +
                "                    \"description\": \"文件唯一key\"\n" +
                "                }\n" +
                "            }\n" +
                "        },\n" +
                "          \"required\": [\"account_id\", \"receiver_id\", \"file_key\"]\n" +
                "    }");
        llmConfig.setSkills(List.of(llmToolsConfig));
        application.setLlmConfig(llmConfig);
        agentApplicationService.save(application);
    }

    /**
     * 测试创建WhatsApp账号任务
     */
    @Test
    @Disabled("Skip until database schema is fixed")
    void createWhatsappAccountTask() {
        // 准备测试数据
        CreateWhatsappAccountTaskDTO createWhatsappAccountTaskDTO = new CreateWhatsappAccountTaskDTO();
        createWhatsappAccountTaskDTO.setAccountId(1L); // 测试账号ID
        createWhatsappAccountTaskDTO.setAppId("test-app-id");
        createWhatsappAccountTaskDTO.setUserId("test-user-id");
        createWhatsappAccountTaskDTO.setUuid(UUID.randomUUID().toString());

        // 执行测试
        proxyTaskBizService.createWhatsappAccountTask(createWhatsappAccountTaskDTO);
    }

    /**
     * 测试登出WhatsApp账号任务
     */
    @Test
    @Disabled("Skip until database schema is fixed")
    void createLogoutWhatsappAccountTask() {
        // 准备测试数据
        LogoutWhatsappAccountTaskDTO logoutWhatsappAccountTaskDTO = new LogoutWhatsappAccountTaskDTO();
        logoutWhatsappAccountTaskDTO.setProxyAccountId(1L); // 测试账号ID
        logoutWhatsappAccountTaskDTO.setAppId("test-app-id");
        logoutWhatsappAccountTaskDTO.setUserId("test-user-id");
        logoutWhatsappAccountTaskDTO.setPhone("+*************"); // 测试手机号
        logoutWhatsappAccountTaskDTO.setExpectExecuteTime(LocalDateTime.now());

        // 执行测试
        proxyTaskBizService.createLogoutWhatsappAccountTask(logoutWhatsappAccountTaskDTO);
    }

    /**
     * 测试查询WhatsApp联系人任务
     */
    @Test
    @Disabled("Skip until database schema is fixed")
    void createSearchWhatsappContactTask() {
        // 准备测试数据
        SearchWhatsappContactTaskDTO searchWhatsappContactTaskDTO = new SearchWhatsappContactTaskDTO();
        searchWhatsappContactTaskDTO.setProxyAccountId(1919581415640334338L); // 测试账号ID
        searchWhatsappContactTaskDTO.setAgentPhone("+*************"); // 测试代理手机号
        searchWhatsappContactTaskDTO.setTargetPhone("+*************"); // 测试目标手机号

        // 执行测试
        proxyTaskBizService.createSearchWhatsappContactTask(searchWhatsappContactTaskDTO);
    }

    @Test
    void testPullTask() {
        WetoolCommonRequest wetoolCommonRequest = new WetoolCommonRequest();
        wetoolCommonRequest.setAction("pull_task");
        wetoolCommonRequest.setWxid("*************");
        WetoolCommonResponseData wetoolCommonResponseData = weToolFacadeBizService.handleRequest(wetoolCommonRequest);
        System.out.println(JSONUtil.toJsonStr(wetoolCommonResponseData));
    }

    @Test
    void testLogOut() {
        WetoolCommonRequest wetoolCommonRequest = new WetoolCommonRequest();
        wetoolCommonRequest.setAction("logout_whatsapp_account");
        wetoolCommonRequest.setWxid("+86");
        WetoolCommonResponseData wetoolCommonResponseData = weToolFacadeBizService.handleRequest(wetoolCommonRequest);
        System.out.println(JSONUtil.toJsonStr(wetoolCommonResponseData));
    }

    @Test
    void testSearchContact() {
        WetoolCommonRequest wetoolCommonRequest = new WetoolCommonRequest();
        wetoolCommonRequest.setAction("search_whatsapp_contact_result");
        wetoolCommonRequest.setWxid("*************");
        WetoolCommonResponseData wetoolCommonResponseData = weToolFacadeBizService.handleRequest(wetoolCommonRequest);
        System.out.println(JSONUtil.toJsonStr(wetoolCommonResponseData));
    }




    @Test
    void testExecuteChatAgentRequest(){
        LLMAgentChatSubmitDTO submitDTO = new LLMAgentChatSubmitDTO();
        submitDTO.setSessionId(1922957095280132098L);
        submitDTO.setUserMessage("目录");
        submitDTO.setExtraSystemPrompt("## CurrentTime：2025-05-30 17:55:16\n## UserInfo：UserNickName为: \n\n");

        AgentApplication application = agentApplicationService.getById(1922956196000382977L);
        List<AgentChatMessage> messages = new ArrayList<>();
        AgentChatMessage userMessage1 = new AgentChatMessage();
        // 设置租户ID
        userMessage1.setTenantId(999999999999999999L);
        // 设置会话ID
        userMessage1.setAgentSessionId(1912465278512529409L);
        // 设置应用Key
        userMessage1.setAgentAppKey("8d2be8db4b514aae9f01d47e7f8afbe5");
        // 设置消息角色为用户
        userMessage1.setChatRole("user");
        // 设置消息类型为文本
        userMessage1.setMessageType("text");
        // 设置消息内容
        userMessage1.setContent("账户id 是 1 文件唯一key 是目录  接受方 是*************");
        // 设置消息时间戳
        userMessage1.setMessageTimestamp(1749037937572l);
        userMessage1.setTransId("1929875379980156929");


        AgentChatMessage userMessage3 = new AgentChatMessage();
        // 设置租户ID
        userMessage3.setTenantId(999999999999999999L);
        // 设置会话ID
        userMessage3.setAgentSessionId(1912465278512529409L);
        // 设置应用Key
        userMessage3.setAgentAppKey("8d2be8db4b514aae9f01d47e7f8afbe5");
        // 设置消息角色为用户
        userMessage3.setChatRole("tool");
        // 设置消息类型为文本
        userMessage3.setMessageType("function_call");
        // 设置消息内容
        userMessage3.setContent("消息发送结果：file error");
        // 设置消息时间戳
        userMessage3.setMessageTimestamp(1749037937572l);
        userMessage3.setTransId("1929875379980156929");
        userMessage3.setToolCallId("call_1x8uiME2aodePxU0XDwRN7FL");



        AgentChatMessage userMessage2 = new AgentChatMessage();
        // 设置租户ID
        userMessage2.setTenantId(999999999999999999L);
        // 设置会话ID
        userMessage2.setAgentSessionId(1912465278512529409L);
        // 设置应用Key
        userMessage2.setAgentAppKey("8d2be8db4b514aae9f01d47e7f8afbe5");
        // 设置消息角色为用户
        userMessage2.setChatRole("assistant");
        // 设置消息类型为文本
        userMessage2.setMessageType("tool_calls");
        // 设置消息内容
        userMessage2.setMessageTimestamp(1749037937572l);
        userMessage2.setTransId("1929875379980156929");
        userMessage2.setToolCallId("chatcmpl-BeYVVlpLyzPJwoUWk2nnD9sQB2jjx");
        OpenAiToolCall openAiToolCall = new OpenAiToolCall();
        openAiToolCall.setId("call_1x8uiME2aodePxU0XDwRN7FL");
        Function function = new Function();
        function.setName("tools_zhipu_mtl_customer_bot_func-send_message_by_user_input");
        function.setArguments("{\"account_id\":1,\"receiver_id\":\"*************\",\"file_key\":\"目录\"}");
        openAiToolCall.setFunction(function);
        openAiToolCall.setType("function");
        userMessage2.setToolCalls(List.of(openAiToolCall));

        AgentChatMessage userMessage4 = new AgentChatMessage();
        // 设置租户ID
        userMessage4.setTenantId(999999999999999999L);
        // 设置会话ID
        userMessage4.setAgentSessionId(1912465278512529409L);
        // 设置应用Key
        userMessage4.setAgentAppKey("8d2be8db4b514aae9f01d47e7f8afbe5");
        // 设置消息角色为用户
        userMessage4.setChatRole("assistant");
        // 设置消息类型为文本
        userMessage4.setMessageType("text");
        // 设置消息内容
        userMessage4.setContent("看起来在发送目录时出现了问题。请确认文件唯一 key 是否正确，或者您可以尝试重新发送。需要帮助的话，随时告诉我！");
        // 设置消息时间戳
        userMessage4.setMessageTimestamp(1749037937572l);
        userMessage4.setTransId("1929875379980156929");

        messages.add(userMessage1);
        messages.add(userMessage3);
        messages.add(userMessage2);
        messages.add(userMessage4);


        RemoteLLMAgentChatVO remoteLLMAgentChatVO = llmAgentBizService.executeChatAgentRequest(submitDTO, application, messages, userMessage1 );
        System.out.println(JSONUtil.toJsonStr(remoteLLMAgentChatVO));
    }

    public static void main(String[] args) {
        System.out.println(System.currentTimeMillis());;
    }


    //todo
    /**
     * 测试创建查询WhatsApp消息状态任务
     */
    @Test
    @Disabled("Skip until database schema is fixed")
    void createWhatsAppMsgStatusTask() {
        // 准备测试数据
        QueryWhatsAppMsgStatusTask queryWhatsAppMsgStatusTask = new QueryWhatsAppMsgStatusTask();
        queryWhatsAppMsgStatusTask.setProxyAccountId(1922956260164845570L); // 测试账号ID
        queryWhatsAppMsgStatusTask.setAgentPhone("+*************"); // 测试代理手机号
        queryWhatsAppMsgStatusTask.setTargetPhone("+*************"); // 测试目标手机号
        // 执行测试
        proxyTaskBizService.createWhatsAppMsgStatusTask(queryWhatsAppMsgStatusTask);

        System.out.println("查询WhatsApp消息状态任务创建成功");
        System.out.println("任务参数: " + JSONUtil.toJsonStr(queryWhatsAppMsgStatusTask));
    }

    @Test
    void  testWhatsAppMsgStatus(){
        WetoolCommonRequest wetoolCommonRequest = new WetoolCommonRequest();
        wetoolCommonRequest.setAction("query_whatsapp_msg_status");
        wetoolCommonRequest.setWxid("*************");
        WetoolCommonResponseData wetoolCommonResponseData = weToolFacadeBizService.handleRequest(wetoolCommonRequest);
        System.out.println(JSONUtil.toJsonStr(wetoolCommonResponseData));
    }

    /**
     * 测试创建撤回WhatsApp消息任务
     */
    @Test
    @Disabled("Skip until database schema is fixed")
    void createRevokeWhatsAppMsgTask() {
        // 准备测试数据
        RevokeWhatsAppMsgTask revokeWhatsAppMsgTask = new RevokeWhatsAppMsgTask();
        revokeWhatsAppMsgTask.setProxyAccountId(1922956260164845570L); // 测试账号ID
        revokeWhatsAppMsgTask.setTargetPhone("+*************"); // 测试代理手机号
        revokeWhatsAppMsgTask.setMessageId("3EB0C728BBE5D0B69C1FF9"); // 要撤回的消息ID

        // 执行测试
        proxyTaskBizService.createRevokeWhatsAppMsgTask(revokeWhatsAppMsgTask);

        System.out.println("撤回WhatsApp消息任务创建成功");
        System.out.println("任务参数: " + JSONUtil.toJsonStr(revokeWhatsAppMsgTask));
    }


    @Test
    void  testRevokeWhatsappMsg(){
        WetoolCommonRequest wetoolCommonRequest = new WetoolCommonRequest();
        wetoolCommonRequest.setAction("revoke_whatsapp_msg");
        wetoolCommonRequest.setWxid("+*************");
        RevokeWhatsappMsgData revokeWhatsappMsgData = new RevokeWhatsappMsgData();
        revokeWhatsappMsgData.setTask_id("1927710226111303682");
        revokeWhatsappMsgData.setTask_result(1);
        revokeWhatsappMsgData.setMsg_id("3EB0A5ED2C8F2E8F49DF8C");
        revokeWhatsappMsgData.setRevoke_result(1);
        revokeWhatsappMsgData.setRevoke_timestamp(1748436909593L);
        wetoolCommonRequest.setData(new JSONObject(revokeWhatsappMsgData));
        WetoolCommonResponseData wetoolCommonResponseData = weToolFacadeBizService.handleRequest(wetoolCommonRequest);
        System.out.println(JSONUtil.toJsonStr(wetoolCommonResponseData));
    }
}