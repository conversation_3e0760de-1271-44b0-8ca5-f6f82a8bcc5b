package com.yaowu.alpha.domain.proxy.biz.impl;

import cn.hutool.json.JSONUtil;
import com.freedom.objectstorage.utils.CloudUploadUtil;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.google.api.client.auth.oauth2.Credential;
import com.google.api.gax.core.CredentialsProvider;
import com.google.api.services.gmail.model.Message;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.OAuth2CredentialsWithRefresh;
import com.yaowu.alpha.config.exception.AlphaException;
import com.yaowu.alpha.domain.auth.IAuthBizService;
import com.yaowu.alpha.domain.email.GmailAuthManager;
import com.yaowu.alpha.domain.email.GmailConstant;
import com.yaowu.alpha.domain.email.gmail.GmailMessageParser;
import com.yaowu.alpha.domain.email.gmail.GmailUtil;
import com.yaowu.alpha.domain.proxy.biz.IProxyAccountBizService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.enums.proxy.*;
import com.yaowu.alpha.model.bo.email.GmailAuthRequest;
import com.yaowu.alpha.model.dto.proxy.*;
import com.yaowu.alpha.model.dto.proxy.control.ProxyAccountQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.ProxyAccountVO;
import com.yaowu.alpha.utils.common.EnumUtil;
import com.yaowu.alpha.utils.convertor.proxy.ProxyAccountMapperStruct;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2025/3/20-13:24
 */
@SpringBootTest
@Slf4j
public class ProxyAccountBizServiceImplTest {

    @Autowired
    private IProxyAccountConfigService proxyAccountConfigService;

    @Autowired
    private IAuthBizService authBizService;

    @Autowired
    private IProxyAccountBizService proxyAccountBizService;

    private ProxyAccount mockProxyAccount;

    @BeforeEach
    void setUp() {
        mockProxyAccount = new ProxyAccount();
        mockProxyAccount.setId(1L);
        mockProxyAccount.setProxyId("testProxyId");
        mockProxyAccount.setThirdType(ProxyThirdTypeEnum.WECHAT.getValue());
        mockProxyAccount.setAccountStatus(ProxyStatusEnum.DISABLED.getValue());
        mockProxyAccount.setTenantId(100L);
    }

    @Test
    void testCreateProxyAccount_Success() {
        ProxyAccountCreateDTO createDTO = new ProxyAccountCreateDTO();
        createDTO.setProxyId("testProxyId---1");
        createDTO.setThirdType(ProxyThirdTypeEnum.PHONE.getValue());
        createDTO.setProxyType(ProxyTypeEnum.AICC.getValue());

        createDTO.setAccountName("testAccountName");
        createDTO.setChatAgentAppKey("testChatAgentAppKey");
        createDTO.setExtractorAgentAppKey("testExtractorAgentAppKey");
        createDTO.setExtractorMode(ProxyExtractorModeEnum.SINGLE_CHAT.getValue());
        createDTO.setGroupMonitorStatus(ProxyAccountGroupMonitorStatusEnum.ONLY_MONITOR.getValue());
        createDTO.setRemark("testRemark");
        Long result = proxyAccountBizService.createProxyAccount(createDTO);
    }

    @Test
    void testCreateProxyAccount_Failure_Duplicate() {
        ProxyAccountCreateDTO createDTO = new ProxyAccountCreateDTO();
        createDTO.setProxyId("testProxyId");
        createDTO.validate();

        when(proxyAccountConfigService.getByCondition(any(ProxyAccountQueryDTO.class))).thenReturn(mockProxyAccount);

        assertThrows(BusinessException.class, () -> proxyAccountBizService.createProxyAccount(createDTO));
    }

    @Test
    void testUpdateProxyAccount_Success() {
        ProxyAccountUpdateDTO updateDTO = new ProxyAccountUpdateDTO();
        updateDTO.setId(1L);

        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(100L);
        when(proxyAccountConfigService.updateById(any(ProxyAccount.class))).thenReturn(true);

        boolean result = proxyAccountBizService.updateProxyAccount(updateDTO);
        assertTrue(result);
    }

    @Test
    void testUpdateProxyAccount_Failure_Unauthorized() {
        ProxyAccountUpdateDTO updateDTO = new ProxyAccountUpdateDTO();
        updateDTO.setId(1L);

        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(200L);

        assertThrows(AlphaException.class, () -> proxyAccountBizService.updateProxyAccount(updateDTO));
    }

    @Test
    void testOperateProxyAccount_Success_Delete() {
        ProxyAccountOperationDTO operationDTO = new ProxyAccountOperationDTO();
        operationDTO.setId(1L);
        operationDTO.setOperationType(ProxyAccountOperationDTO.OperationType.ACCOUNT_DELETE);

        mockProxyAccount.setAccountStatus(ProxyStatusEnum.DISABLED.getValue());

        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(100L);
        when(proxyAccountConfigService.removeById(1L)).thenReturn(true);

        boolean result = proxyAccountBizService.operateProxyAccount(operationDTO);
        assertTrue(result);
    }

    @Test
    void testOperateProxyAccount_Failure_Delete_NotDisabled() {
        ProxyAccountOperationDTO operationDTO = new ProxyAccountOperationDTO();
        operationDTO.setId(1L);
        operationDTO.setOperationType(ProxyAccountOperationDTO.OperationType.ACCOUNT_DELETE);

        mockProxyAccount.setAccountStatus(ProxyStatusEnum.DISABLED.getValue());

        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(100L);

        assertThrows(BusinessException.class, () -> proxyAccountBizService.operateProxyAccount(operationDTO));
    }

    @Test
    void testPage_Success() {
        ProxyAccountRequestQueryDTO requestQueryDTO = new ProxyAccountRequestQueryDTO();
        ProxyAccountQueryDTO queryDTO = ProxyAccountMapperStruct.INSTANCE.toPageQueryDto(requestQueryDTO);

        List<ProxyAccount> proxyAccountList = Arrays.asList(mockProxyAccount);
        BasePage<ProxyAccount> page = new BasePage<>();
        page.setRecords(proxyAccountList);

        when(proxyAccountConfigService.pageByCondition(queryDTO)).thenReturn(page);

        BasePage<ProxyAccountVO> result = proxyAccountBizService.page(requestQueryDTO);
        assertNotNull(result);
        assertEquals(1, result.getRecords().size());
    }

    @Test
    void testDetail_Success() {
        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(100L);

        ProxyAccountVO result = proxyAccountBizService.detail(1L);
        assertNotNull(result);
        assertEquals("testProxyId", result.getProxyId());
    }

    @Test
    void testDetail_Failure_Unauthorized() {
        when(proxyAccountConfigService.getById(1L)).thenReturn(mockProxyAccount);
        when(authBizService.currentTenantId()).thenReturn(200L);

        assertThrows(AlphaException.class, () -> proxyAccountBizService.detail(1L));
    }

    @Test
    public void test_page() {
        ProxyAccountRequestQueryDTO requestQueryDTO = new ProxyAccountRequestQueryDTO();
        BasePage<ProxyAccountVO> page = this.proxyAccountBizService.page(requestQueryDTO);
        System.out.println(JSONUtil.toJsonStr(page));
    }

    @Test
    public void test_gmail() throws IOException {
        String code = "4/0AQSTgQG8qxEGBIReKDVzUf1t8i_TSZ1UueJE_junCO3jSeAizG1ePWeZiZhUpoDsEMK3wA";
        GmailAuthRequest param = GmailAuthRequest.commonParam(GmailConstant.CURRENT_AUTHENTICATED_USER, GmailAuthManager.REDIRECT_URI,"local").authCode(code).applicationName("xx").userId("me").build();
        // TODO(developer): Replace these variables before running the sample.
        String projectId = "yaowu-454308";
        String subscriptionId = "dada-sub";
        CredentialsProvider provider = () -> {
            Credential credential = GmailUtil.getCredential(param);
            return OAuth2CredentialsWithRefresh.newBuilder().setAccessToken(new AccessToken(credential.getAccessToken(), new Date(credential.getExpirationTimeMilliseconds()))).setRefreshHandler(new OAuth2CredentialsWithRefresh.OAuth2RefreshHandler() {
                @Override
                public AccessToken refreshAccessToken() throws IOException {
                    credential.refreshToken();
                    return new AccessToken(credential.getAccessToken(), new Date(credential.getExpirationTimeMilliseconds()));
                }
            }).build();
        };

        Message message = GmailUtil.getMessage(param, "195d6f7830797074");
        // 解析邮件
        // GmainParseUtil.ParsedEmail parsedEmail = GmainParseUtil.parseGmailMessage(message);
        GmailMessageParser.ParsedGmailMessage mg = GmailMessageParser.parseMessage(GmailUtil.gmailService(param), param.getUserId(), message,
                "D:\\IdeaProjects\\h-alpha\\h-alpha\\src\\main\\java\\com\\yaowu\\alpha\\domain\\email\\gmail\\");
        // listMessage(param);
        // historyMessage(GmailHistoryRequest.builder().startHistoryId(101981L).build(), param);

    }
}
