package com.yaowu.alpha.domain.proxy.control.biz.impl;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.model.dto.proxy.ChatMessageCursorQueryDTO;
import com.yaowu.alpha.model.dto.proxy.NewMessageCheckDTO;
import com.yaowu.alpha.model.vo.friend.ChatMessageCursorPageVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class ProxyAccountFriendBizServiceImplTest {

    @Autowired
    private IProxyAccountFriendBizService proxyAccountFriendBizService;

    @Test
    void cursorPageMessages() {
        ChatMessageCursorQueryDTO dto = new ChatMessageCursorQueryDTO();
        dto.setSize(20);
        dto.setFriendProxyId("mchany");
        dto.setProxyAccountId(1L);
        ChatMessageCursorPageVO chatMessageCursorPageVO = proxyAccountFriendBizService.cursorPageMessages(dto);
        System.out.println(JSONUtil.toJsonStr(chatMessageCursorPageVO));
    }

    @Test
    void countNewerMessages() {
        NewMessageCheckDTO dto = new NewMessageCheckDTO();
        dto.setFriendProxyId("mchany");
        dto.setProxyAccountId(1L);
        dto.setNewerThan(1904503543923847170L);
        boolean sendFlag = proxyAccountFriendBizService.countNewerMessages(dto);
        System.out.println(JSONUtil.toJsonStr(sendFlag));
    }

}