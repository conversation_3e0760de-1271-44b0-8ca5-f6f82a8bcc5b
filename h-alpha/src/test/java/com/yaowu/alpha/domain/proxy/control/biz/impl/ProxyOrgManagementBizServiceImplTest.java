package com.yaowu.alpha.domain.proxy.control.biz.impl;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyOrgManagementBizService;
import com.yaowu.alpha.model.dto.common.OrgListQueryDTO;
import com.yaowu.alpha.model.vo.common.OrgListVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;


@SpringBootTest
public class ProxyOrgManagementBizServiceImplTest {

    @Autowired
    private IProxyOrgManagementBizService proxyOrgManagementBizService;

    @Test
    void getOrgList() {
        OrgListQueryDTO orgListQueryDTO = new OrgListQueryDTO();
        List<OrgListVO> orgList = proxyOrgManagementBizService.getOrgList(orgListQueryDTO);
        System.out.println(JSONUtil.toJsonStr(orgList));
    }
}