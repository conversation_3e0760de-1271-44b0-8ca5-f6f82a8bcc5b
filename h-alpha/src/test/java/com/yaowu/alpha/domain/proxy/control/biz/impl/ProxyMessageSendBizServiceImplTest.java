package com.yaowu.alpha.domain.proxy.control.biz.impl;

import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.control.factory.MessageSendStrategyFactory;
import com.yaowu.alpha.domain.proxy.control.strategy.impl.ImageMessageSendStrategy;
import com.yaowu.alpha.domain.proxy.control.strategy.impl.TextMessageSendStrategy;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.enums.proxy.MessageTypeEnum;
import com.yaowu.alpha.model.dto.proxy.SendMessageDTO;
import com.yaowu.alpha.model.bo.proxy.SendMsgTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 代理消息发送业务测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ProxyMessageSendBizServiceImplTest {

    @InjectMocks
    private ProxyMessageSendBizServiceImpl proxyMessageSendBizService;

    @Mock
    private MessageSendStrategyFactory messageSendStrategyFactory;

    @Mock
    private IProxyTaskBizService proxyTaskBizService;

    @Mock
    private TextMessageSendStrategy textMessageSendStrategy;

    @Mock
    private ImageMessageSendStrategy imageMessageSendStrategy;

    @Captor
    private ArgumentCaptor<SendMsgTask> sendMsgTaskCaptor;

    private SendMessageDTO textMessageDTO;
    private SendMessageDTO imageMessageDTO;

    @BeforeEach
    void setUp() {
        // 准备文本消息DTO
        textMessageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.TEXT.getValue())
                .setAccountId(1L)
                .setReceiverId("***********@chatroom")
                .setContent("Hello")
                .setIsGroup(Boolean.TRUE)
                .setAtUserIds(Arrays.asList("789"));

        // 准备图片消息DTO
        imageMessageDTO = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.IMAGE.getValue())
                .setAccountId(123L)
                .setReceiverId("456")
                .setMediaUrl("http://example.com/image.jpg")
                .setIsGroup(false);
    }

    @Test
    void sendMessage_WithTextMessage_ShouldCreateTextMsgTask() {
        // 配置mock
        when(messageSendStrategyFactory.getStrategy(MessageTypeEnum.TEXT))
                .thenReturn(textMessageSendStrategy);

        // 执行
        proxyMessageSendBizService.sendMessage(textMessageDTO);

        // 验证策略工厂调用
        verify(messageSendStrategyFactory).getStrategy(MessageTypeEnum.TEXT);
        
        // 验证策略方法调用
        verify(textMessageSendStrategy).buildMsgTask(eq(textMessageDTO), any(SendMsgTask.class));
        verify(textMessageSendStrategy).sendMsg(any(SendMsgTask.class));
    }

    @Test
    void sendMessage_WithImageMessage_ShouldCreateImageMsgTask() {
        // 配置mock
        when(messageSendStrategyFactory.getStrategy(MessageTypeEnum.IMAGE))
                .thenReturn(imageMessageSendStrategy);

        // 执行
        proxyMessageSendBizService.sendMessage(imageMessageDTO);

        // 验证策略工厂调用
        verify(messageSendStrategyFactory).getStrategy(MessageTypeEnum.IMAGE);
        
        // 验证策略方法调用
        verify(imageMessageSendStrategy).buildMsgTask(eq(imageMessageDTO), any(SendMsgTask.class));
        verify(imageMessageSendStrategy).sendMsg(any(SendMsgTask.class));
    }

    @Test
    void sendMessage_WithNullMessageType_ShouldThrowException() {
        // 准备
        SendMessageDTO dto = new SendMessageDTO()
                .setMessageType(null)
                .setAccountId(123L)
                .setReceiverId("456");

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
                () -> proxyMessageSendBizService.sendMessage(dto));
        
        assertEquals(ErrorCodeEnum.UN_SUPPORT_MESSAGE_TYPE.getCode(), exception.getCode());
        assertEquals(ErrorCodeEnum.UN_SUPPORT_MESSAGE_TYPE.getMessage(), exception.getMessage());
        
        // 验证策略工厂和策略方法都没有被调用
        verifyNoInteractions(messageSendStrategyFactory, textMessageSendStrategy, imageMessageSendStrategy);
    }

    @Test
    void sendMessage_WithTextMessageAndNullContent_ShouldThrowException() {
        // 准备
        SendMessageDTO dto = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.TEXT.getValue())
                .setAccountId(123L)
                .setReceiverId("456")
                .setContent(null);

        // 执行和验证
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> proxyMessageSendBizService.sendMessage(dto));
        
        assertEquals("文本消息内容不能为空", exception.getMessage());
        
        verifyNoInteractions(messageSendStrategyFactory, textMessageSendStrategy, imageMessageSendStrategy);
    }

    @Test
    void sendMessage_WithImageMessageAndNullImageUrl_ShouldThrowException() {
        // 准备
        SendMessageDTO dto = new SendMessageDTO()
                .setMessageType(MessageTypeEnum.IMAGE.getValue())
                .setAccountId(123L)
                .setReceiverId("456")
                .setMediaUrl(null);

        // 执行和验证
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> proxyMessageSendBizService.sendMessage(dto));
        
        assertEquals("图片URL不能为空", exception.getMessage());
        
        verifyNoInteractions(messageSendStrategyFactory, textMessageSendStrategy, imageMessageSendStrategy);
    }
} 