package com.yaowu.alpha.utils.email.component;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class EmailIntegrationServiceTest {

    @Autowired
    private EmailIntegrationService emailIntegrationService;

    @Test
    void receiveMessages() {
//        MessageReceiveRequestDTO requestDTO = MessageReceiveRequestDTO.builder()
//                .maxCount(100)
//                .unreadOnly(true)
//                .lastSyncTime(LocalDateTime.now())
//                .syncSentFolder(false)
//                .build();
//
//        ProxyAccount proxyAccount = new ProxyAccount();
//        proxyAccount.setId(1954829743105576961L);
//        proxyAccount.setProxyId("<EMAIL>");
//        proxyAccount.setTenantId(104L);
//        proxyAccount.setThirdType(19);
//        /**
//         * imapPort": 993,
//         *     "pop3Port": null,
//         *     "smtpPort": 465,
//         *     "imapServer": "imappro.zoho.com",
//         *     "pop3Server": "",
//         *     "smtpServer": "smtppro.zoho.com",
//         *     "lastSyncTime": "2025-08-12 09:39:02.*********",
//         *     "authorizationCode": "YaoWu112233"
//         */
//        String extendJson = "{\"avatar\": \"\", \"tagInfo\": null, \"saleBdId\": null, \"emailInfo\": {\"authTime\": \"\", \"imapPort\": 993, \"pop3Port\": null, \"smtpPort\": 465, \"imapServer\": \"imappro.zoho.com\", \"pop3Server\": \"\", \"smtpServer\": \"smtppro.zoho.com\", \"lastSyncTime\": \"\", \"authorizationCode\": \"YaoWu112233\"}, \"gmailInfo\": null, \"authStatus\": \"AUTHORIZED\", \"whatsAppInfo\": null, \"supportFriendId\": null, \"extractorAgentAppKey\": \"\"}";
//        ProxyAccountExtendInfo extendInfo = JSONUtil.toBean(extendJson, ProxyAccountExtendInfo.class);
//        proxyAccount.setExtendInfo(extendInfo);
//
//        emailIntegrationService.receiveMessages(requestDTO, proxyAccount);
    }
}