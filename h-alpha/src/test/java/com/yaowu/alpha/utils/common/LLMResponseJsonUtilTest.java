package com.yaowu.alpha.utils.common;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResponseJsonUtil工具类测试
 * 
 * <AUTHOR>
 * @date 2024/12/31
 */
class LLMResponseJsonUtilTest {

    @Test
    void testExtractFirstJsonObject_DirectJson() {
        String content = "{\"name\": \"test\", \"age\": 18}";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertEquals("test", result.getStr("name"));
        assertEquals(18, result.getInt("age"));
    }

    @Test
    void test() {
        String json = "```json\n{\n  \"subject\": \"Enhance Your Luxury Perfume Packaging with Eco-Friendly Solutions\",\n  \"content\": \"<!DOCTYPE html><html><head><style>\\\n    body { font-family: 'Arial', sans-serif; line-height: 1.6; color: #333; max-width: 650px; margin: 0 auto; padding: 20px; background-color: #f8f8f8; }\\\n    .header { font-size: 22px; margin-bottom: 20px; color: #1a3e72; font-weight: 600; }\\\n    .content { margin-bottom: 20px; font-size: 16px; }\\\n    .highlight { color: #2a5699; font-weight: 600; }\\\n    .section-title { font-weight: 600; color: #1a3e72; margin: 20px 0 10px 0; font-size: 18px; border-bottom: 2px solid #e0e8f5; padding-bottom: 8px; }\\\n    .opportunity-item { margin-bottom: 10px; padding-left: 20px; position: relative; }\\\n    .opportunity-item:before { content: '•'; position: absolute; left: 0; color: #3a6cb5; font-weight: bold; }\\\n    .solution-item { margin-bottom: 15px; padding-left: 25px; text-indent: -25px; }\\\n    .solution-item:before { content: attr(data-number); color: #3a6cb5; font-weight: 600; margin-right: 10px; }\\\n    .feature { font-weight: 600; color: #2a5699; }\\\n    .cta-container { text-align: center; margin: 30px 0; }\\\n    .cta-button { display: inline-block; background-color: #25D366; color: white; padding: 12px 24px; text-decoration: none; border-radius: 20px; font-weight: 500; font-size: 16px; transition: all 0.3s ease; }\\\n    .cta-button:hover { background-color: #128C7E; }\\\n    .signature { margin-top: 30px; font-style: normal; color: #555; }\\\n    .company-name { font-weight: 600; color: #1a3e72; }\\\n    .quote-block { border-left: 3px solid #e0e8f5; padding-left: 20px; margin: 20px 0; color: #555; }\\\n  </style></head><body>\\\n    <div class='header'>Dear Aryeh Nakache,</div>\\\n    <div class='content'>I hope this message finds you well. As the CEO of Paris Perfumes, your dedication to offering premium, luxury perfumes has always been an inspiration. Your brand's commitment to quality and elegance in the fragrance industry is truly commendable.</div>\\\n    <div class='section-title'>Potential Enhancement Opportunities</div>\\\n    <div class='content'>\\\n      <div class='opportunity-item'><span class='feature'>Elevate Brand Elegance</span> with sustainable packaging that aligns with your luxury positioning.</div>\\\n      <div class='opportunity-item'><span class='feature'>Enhance Product Protection</span> using advanced materials to safeguard the integrity of your fragrances.</div>\\\n      <div class='opportunity-item'><span class='feature'>Boost Environmental Credentials</span> by adopting eco-friendly packaging solutions that resonate with your discerning customers.</div>\\\n    </div>\\\n    <div class='section-title'>How Fomalhaut Can Support Paris Perfumes</div>\\\n    <div class='content'>\\\n      <div class='solution-item' data-number='1.'><span class='feature'>Sustainable Luxury</span><br>Utilize PCR plastics and sugarcane-based materials for an eco-friendly yet luxurious packaging experience.</div>\\\n      <div class='solution-item' data-number='2.'><span class='feature'>Advanced Protection</span><br>Our airless bottles and jars provide superior protection against light and air, ensuring your fragrances stay pristine.</div>\\\n      <div class='solution-item' data-number='3.'><span class='feature'>Customized Elegance</span><br>Benefit from our one-stop customization services to create packaging that perfectly complements your brand's aesthetic.</div>\\\n      <div class='solution-item' data-number='4.'><span class='feature'>Quality Assured</span><br>With certifications like APR and FTO, and rigorous quality testing, we guarantee the highest standards for your packaging.</div>\\\n    </div>\\\n    <div class='quote-block'>We believe that our eco-friendly and high-quality packaging solutions can significantly enhance the luxury experience of your perfumes, aligning with your brand's values and customer expectations.</div>\\\n    <div class='cta-container'><a href='https://wa.me/*************' class='cta-button'>Contact us on WhatsApp</a></div>\\\n    <div class='signature'>Best regards,<br><span class='company-name'>Sarah, Key Account Manager<br>Fomalhaut Eco-Pack Material Tech Co., Ltd</span></div>\\\n  </body></html>\"\n}\n```";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(json);
        System.out.println(result.getStr("content"));
        assertNotNull(result);
        assertNotNull(result.getStr("subject"));
    }

    @Test
    void testExtractFirstJsonObject_MarkdownCodeBlock() {
        String content = """
            ```json
            {
              "status": "success",
              "code": 200,
              "message": "Processing completed",
              "data": {
                "type": "email",
                "content": "This is a test message"
              }
            }
            ```
            """;
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertEquals("success", result.getStr("status"));
        assertEquals(200, result.getInt("code"));
        assertEquals("Processing completed", result.getStr("message"));
        assertNotNull(result.getJSONObject("data"));
        assertEquals("email", result.getJSONObject("data").getStr("type"));
    }

    @Test
    void testExtractFirstJsonObject_MarkdownCodeBlockUpperCase() {
        String content = "```JSON\n{\"result\": true, \"message\": \"OK\"}\n```";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertTrue(result.getBool("result"));
        assertEquals("OK", result.getStr("message"));
    }

    @Test
    void testExtractFirstJsonObject_InlineCode() {
        String content = "请参考这个配置 `{\"timeout\": 5000, \"retry\": 3}` 进行设置。";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertEquals(5000, result.getInt("timeout"));
        assertEquals(3, result.getInt("retry"));
    }

    @Test
    void testExtractFirstJsonObject_MultiLine() {
        String content = "处理结果如下：\n\n{\"data\": [1, 2, 3], \"total\": 3}\n\n操作完成。";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertNotNull(result.getJSONArray("data"));
        assertEquals(3, result.getInt("total"));
    }

    @Test
    void testExtractFirstJsonObject_ComplexText() {
        String content = """
            根据您的请求，我生成了以下配置：
            
            ```json
            {
                "database": {
                    "host": "localhost",
                    "port": 3306,
                    "name": "testdb"
                },
                "cache": {
                    "enabled": true,
                    "ttl": 3600
                }
            }
            ```
            
            这个配置包含了数据库和缓存的设置。
            """;
        
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertNotNull(result.getJSONObject("database"));
        assertNotNull(result.getJSONObject("cache"));
        assertEquals("localhost", result.getJSONObject("database").getStr("host"));
        assertTrue(result.getJSONObject("cache").getBool("enabled"));
    }

    @Test
    void testExtractFirstJsonArray_DirectArray() {
        String content = "[{\"id\": 1, \"name\": \"item1\"}, {\"id\": 2, \"name\": \"item2\"}]";
        JSONArray result = LLMResponseJsonUtil.extractFirstJsonArray(content);
        
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.getJSONObject(0).getInt("id"));
        assertEquals("item2", result.getJSONObject(1).getStr("name"));
    }

    @Test
    void testExtractFirstJsonArray_MarkdownCodeBlock() {
        String content = "列表数据：\n```json\n[\"apple\", \"banana\", \"orange\"]\n```";
        JSONArray result = LLMResponseJsonUtil.extractFirstJsonArray(content);
        
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("apple", result.getStr(0));
        assertEquals("banana", result.getStr(1));
        assertEquals("orange", result.getStr(2));
    }

    @Test
    void testExtractAllJsonObjects_MultipleObjects() {
        String content = """
            第一个配置：
            ```json
            {"type": "config1", "value": 100}
            ```
            
            第二个配置：
            ```json
            {"type": "config2", "value": 200}
            ```
            
            还有一个内联的 `{"type": "config3", "value": 300}` 配置。
            """;
        
        List<JSONObject> results = LLMResponseJsonUtil.extractAllJsonObjects(content);
        
        assertNotNull(results);
        assertEquals(3, results.size());
        
        assertEquals("config1", results.get(0).getStr("type"));
        assertEquals(100, results.get(0).getInt("value"));
        
        assertEquals("config2", results.get(1).getStr("type"));
        assertEquals(200, results.get(1).getInt("value"));
        
        assertEquals("config3", results.get(2).getStr("type"));
        assertEquals(300, results.get(2).getInt("value"));
    }

    @Test
    void testExtractFirstJsonObject_EmptyContent() {
        assertNull(LLMResponseJsonUtil.extractFirstJsonObject(""));
        assertNull(LLMResponseJsonUtil.extractFirstJsonObject(null));
        assertNull(LLMResponseJsonUtil.extractFirstJsonObject("   "));
    }

    @Test
    void testExtractFirstJsonObject_NoJsonContent() {
        String content = "这里没有任何JSON内容，只是普通的文本描述。";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNull(result);
    }

    @Test
    void testExtractFirstJsonObject_InvalidJson() {
        String content = "```json\n{\"name\": \"test\", \"age\": }\n```";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNull(result);
    }

    @Test
    void testExtractFirstJsonObject_MixedContent() {
        String content = """
            这是一个复杂的响应内容。
            
            首先是一些说明文字。
            
            然后是JSON数据：
            ```json
            {
                "success": true,
                "data": {
                    "userId": 12345,
                    "userName": "张三",
                    "roles": ["admin", "user"]
                },
                "timestamp": "2024-12-31T10:00:00Z"
            }
            ```
            
            最后是总结信息。
            """;
        
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertTrue(result.getBool("success"));
        assertNotNull(result.getJSONObject("data"));
        assertEquals(12345, result.getJSONObject("data").getInt("userId"));
        assertEquals("张三", result.getJSONObject("data").getStr("userName"));
        assertNotNull(result.getJSONObject("data").getJSONArray("roles"));
    }

    @Test
    void testExtractFirstJsonObject_NestedBraces() {
        String content = "配置如下: {\"outer\": {\"inner\": {\"value\": \"test\"}}, \"count\": 1}";
        JSONObject result = LLMResponseJsonUtil.extractFirstJsonObject(content);
        
        assertNotNull(result);
        assertEquals(1, result.getInt("count"));
        assertNotNull(result.getJSONObject("outer"));
        assertEquals("test", result.getJSONObject("outer").getJSONObject("inner").getStr("value"));
    }
} 