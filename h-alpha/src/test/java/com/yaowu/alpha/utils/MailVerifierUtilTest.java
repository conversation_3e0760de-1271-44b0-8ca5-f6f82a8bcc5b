package com.yaowu.alpha.utils;

import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.http.OkHttpClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * 邮件验证工具类测试
 * 
 * <AUTHOR>
 * @date 2025-01-27 16:30:00
 * @version 2.0 - 适配组件式重构，简化配置
 */
@Slf4j
@SpringBootTest
class MailVerifierUtilTest {

    private MailVerifierUtil mailVerifierUtil;
    
    private static final String API_KEY = "";
    private static final String INVALID_API_KEY = "INVALID_KEY";
    
    @BeforeEach
    void setUp() {
        mailVerifierUtil = new MailVerifierUtil();
        // 使用反射设置私有字段apiKey
        ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", API_KEY);
    }

    @Test
    @DisplayName("测试验证有效邮箱地址")
    void testVerifyEmail_ValidEmail() {
        String email = "<EMAIL>";
        
        MailVerifierUtil.MailVerifyResult result = mailVerifierUtil.verifyEmail(email);
        
        assertNotNull(result, "验证结果不应为空");
        assertEquals(email, result.getEmail(), "返回的邮箱地址应与输入一致");
        assertNotNull(result.getCode(), "验证码不应为空");
        assertNotNull(result.getStatusDescription(), "状态描述不应为空");
        
        log.info("验证结果: 邮箱={}, 代码={}, 状态={}, 有效={}", 
            result.getEmail(), result.getCode(), result.getStatusDescription(), result.isValid());
    }

    @ParameterizedTest
    @CsvSource({
        "<EMAIL>, 有效邮箱测试",
        "<EMAIL>, Gmail邮箱测试",
        "<EMAIL>, Outlook邮箱测试",
        "<EMAIL>, Yahoo邮箱测试"
    })
    @DisplayName("测试验证多个邮箱地址")
    void testVerifyEmail_MultipleEmails(String email, String description) {
        try {
            MailVerifierUtil.MailVerifyResult result = mailVerifierUtil.verifyEmail(email);
            
            assertNotNull(result, description + " - 验证结果不应为空");
            assertEquals(email, result.getEmail(), description + " - 返回的邮箱地址应与输入一致");
            assertNotNull(result.getCode(), description + " - 验证码不应为空");
            
            log.info("{}: 邮箱={}, 状态={}, 有效={}", 
                description, result.getEmail(), result.getStatusDescription(), result.isValid());
        } catch (Exception e) {
            log.warn("{} 验证失败: {}", description, e.getMessage());
        }
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "invalid-email",
        "@invalid.com",
        "invalid@",
        "<EMAIL>",
        "invalid <EMAIL>"
    })
    @DisplayName("测试验证无效邮箱格式")
    void testVerifyEmail_InvalidFormat(String email) {
        try {
            MailVerifierUtil.MailVerifyResult result = mailVerifierUtil.verifyEmail(email);
            
            assertNotNull(result, "即使格式无效，也应返回验证结果");
            // 注意：API对于格式无效的邮箱可能返回null的email字段，这是正常的
            if (result.getEmail() != null) {
                assertEquals(email, result.getEmail(), "如果返回邮箱地址，应与输入一致");
            }
            
            log.info("无效格式邮箱 {}: 返回邮箱={}, 状态={}, 代码={}", 
                email, result.getEmail(), result.getStatusDescription(), result.getCode());
        } catch (Exception e) {
            log.warn("验证无效格式邮箱 {} 失败: {}", email, e.getMessage());
        }
    }

    @Test
    @DisplayName("测试邮箱地址为空的异常处理")
    void testVerifyEmail_EmptyEmail() {
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.verifyEmail(""),
            "空邮箱地址应抛出BusinessException");
        
        assertEquals("邮箱地址不能为空", exception.getMessage());
        log.info("空邮箱地址异常测试通过: {}", exception.getMessage());
    }

    @Test
    @DisplayName("测试邮箱地址为null的异常处理")
    void testVerifyEmail_NullEmail() {
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.verifyEmail(null),
            "null邮箱地址应抛出BusinessException");
        
        assertEquals("邮箱地址不能为空", exception.getMessage());
        log.info("null邮箱地址异常测试通过: {}", exception.getMessage());
    }

    @Test
    @DisplayName("测试API密钥未配置的异常处理")
    void testVerifyEmail_EmptyApiKey() {
        // 设置空的API密钥
        ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", "");
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.verifyEmail("<EMAIL>"),
            "API密钥未配置应抛出BusinessException");
        
        assertEquals("邮件验证API密钥未配置", exception.getMessage());
        log.info("API密钥未配置异常测试通过: {}", exception.getMessage());
    }

    @Test
    @DisplayName("测试API密钥为null的异常处理")
    void testVerifyEmail_NullApiKey() {
        // 设置null的API密钥
        ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", null);
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.verifyEmail("<EMAIL>"),
            "API密钥为null应抛出BusinessException");
        
        assertEquals("邮件验证API密钥未配置", exception.getMessage());
        log.info("API密钥为null异常测试通过: {}", exception.getMessage());
    }

    @Test
    @DisplayName("测试无效API密钥的处理")
    void testVerifyEmail_InvalidApiKey() {
        try {
            // 设置无效的API密钥
            ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", INVALID_API_KEY);
            MailVerifierUtil.MailVerifyResult result = mailVerifierUtil.verifyEmail(
                "<EMAIL>");
            
            // 无效密钥可能返回错误码-4
            if (result.getCode() != null && result.getCode() == -4) {
                assertEquals("KEY错误", result.getStatusDescription());
                log.info("无效API密钥测试通过: {}", result.getStatusDescription());
            } else {
                log.warn("无效API密钥返回意外结果: 代码={}, 状态={}", 
                    result.getCode(), result.getStatusDescription());
            }
        } catch (Exception e) {
            log.info("无效API密钥抛出异常: {}", e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取剩余验证次数")
    void testGetCredits_ValidApiKey() {
        try {
            Integer credits = mailVerifierUtil.getCredits();
            
            assertNotNull(credits, "剩余次数不应为空");
            assertTrue(credits >= 0, "剩余次数应大于等于0");
            
            log.info("当前剩余验证次数: {}", credits);
        } catch (Exception e) {
            log.warn("获取剩余次数失败: {}", e.getMessage());
        }
    }

    @Test
    @DisplayName("测试获取剩余次数时API密钥未配置的异常处理")
    void testGetCredits_EmptyApiKey() {
        // 设置空的API密钥
        ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", "");
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.getCredits(),
            "API密钥未配置应抛出BusinessException");
        
        assertEquals("邮件验证API密钥未配置", exception.getMessage());
        log.info("获取剩余次数API密钥未配置异常测试通过: {}", exception.getMessage());
    }
    
    @Test
    @DisplayName("测试获取剩余次数时API密钥为null的异常处理")
    void testGetCredits_NullApiKey() {
        // 设置null的API密钥
        ReflectionTestUtils.setField(mailVerifierUtil, "apiKey", null);
        
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> mailVerifierUtil.getCredits(),
            "API密钥为null应抛出BusinessException");
        
        assertEquals("邮件验证API密钥未配置", exception.getMessage());
        log.info("获取剩余次数API密钥为null异常测试通过: {}", exception.getMessage());
    }

    @Test
    @DisplayName("测试MailVerifyResult的便捷方法")
    void testMailVerifyResult_ConvenienceMethods() {
        String email = "<EMAIL>";
        
        try {
            MailVerifierUtil.MailVerifyResult result = mailVerifierUtil.verifyEmail(email);
            
            // 测试isValid方法
            boolean isValid = result.isValid();
            boolean expectedValid = Integer.valueOf(1).equals(result.getCode());
            assertEquals(expectedValid, isValid, "isValid方法应正确判断邮箱有效性");
            
            // 测试getStatusDescription方法
            String description = result.getStatusDescription();
            assertNotNull(description, "状态描述不应为空");
            assertFalse(description.trim().isEmpty(), "状态描述不应为空字符串");
            
            log.info("便捷方法测试: 有效={}, 描述={}", isValid, description);
        } catch (Exception e) {
            log.warn("便捷方法测试失败: {}", e.getMessage());
        }
    }

    @Test
    @DisplayName("测试状态码枚举映射")
    void testStatusCodeEnumMapping() {
        // 测试枚举的fromCode方法
        assertEquals(MailVerifierUtil.VerifyStatusCode.VALID, 
            MailVerifierUtil.VerifyStatusCode.fromCode(1));
        assertEquals(MailVerifierUtil.VerifyStatusCode.INVALID, 
            MailVerifierUtil.VerifyStatusCode.fromCode(0));
        assertEquals(MailVerifierUtil.VerifyStatusCode.FORMAT_ERROR, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-1));
        assertEquals(MailVerifierUtil.VerifyStatusCode.NETWORK_ERROR, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-2));
        assertEquals(MailVerifierUtil.VerifyStatusCode.INSUFFICIENT_BALANCE, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-3));
        assertEquals(MailVerifierUtil.VerifyStatusCode.KEY_ERROR, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-4));
        assertEquals(MailVerifierUtil.VerifyStatusCode.UNVERIFIED_DOMAIN, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-5));
        assertEquals(MailVerifierUtil.VerifyStatusCode.CONCURRENT_LIMIT_EXCEEDED, 
            MailVerifierUtil.VerifyStatusCode.fromCode(-6));
        
        // 测试未知状态码
        assertNull(MailVerifierUtil.VerifyStatusCode.fromCode(999));
        assertNull(MailVerifierUtil.VerifyStatusCode.fromCode(null));
        
        log.info("状态码枚举映射测试通过");
    }
    
    @Test
    @DisplayName("测试状态码描述映射")
    void testStatusCodeMapping() {
        // 创建测试用的MailVerifyResult对象
        MailVerifierUtil.MailVerifyResult result = new MailVerifierUtil.MailVerifyResult();
        
        // 测试各种状态码
        result.setCode(1);
        assertEquals("邮箱地址有效", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.VALID, result.getStatusCode());
        assertTrue(result.isValid());
        
        result.setCode(0);
        assertEquals("邮箱地址无效", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.INVALID, result.getStatusCode());
        assertFalse(result.isValid());
        
        result.setCode(-1);
        assertEquals("格式错误", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.FORMAT_ERROR, result.getStatusCode());
        assertFalse(result.isValid());
        
        result.setCode(-2);
        assertEquals("网络错误", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.NETWORK_ERROR, result.getStatusCode());
        
        result.setCode(-3);
        assertEquals("余额不足", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.INSUFFICIENT_BALANCE, result.getStatusCode());
        
        result.setCode(-4);
        assertEquals("KEY错误", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.KEY_ERROR, result.getStatusCode());
        
        result.setCode(-5);
        assertEquals("未验证域名", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.UNVERIFIED_DOMAIN, result.getStatusCode());
        
        result.setCode(-6);
        assertEquals("超出并发限制", result.getStatusDescription());
        assertEquals(MailVerifierUtil.VerifyStatusCode.CONCURRENT_LIMIT_EXCEEDED, result.getStatusCode());
        
        result.setCode(999);
        assertEquals("未知错误码: 999", result.getStatusDescription());
        assertNull(result.getStatusCode());
        assertFalse(result.isValid());
        
        result.setCode(null);
        assertEquals("未知状态", result.getStatusDescription());
        assertNull(result.getStatusCode());
        assertFalse(result.isValid());
        
        log.info("状态码描述映射测试通过");
    }
    
    @Test
    @DisplayName("测试枚举的isValid方法")
    void testEnumIsValidMethod() {
        assertTrue(MailVerifierUtil.VerifyStatusCode.VALID.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.INVALID.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.FORMAT_ERROR.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.NETWORK_ERROR.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.INSUFFICIENT_BALANCE.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.KEY_ERROR.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.UNVERIFIED_DOMAIN.isValid());
        assertFalse(MailVerifierUtil.VerifyStatusCode.CONCURRENT_LIMIT_EXCEEDED.isValid());
        
        log.info("枚举isValid方法测试通过");
    }
}