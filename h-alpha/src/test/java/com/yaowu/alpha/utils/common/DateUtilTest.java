package com.yaowu.alpha.utils.common;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

class DateUtilTest {

    @Test
    void toMilliSecond() {
        LocalDateTime time = DateUtil.toLocalDateTime(1754866724000L);
        System.out.println(LocalDateTimeUtil.format(time, DatePattern.NORM_DATETIME_PATTERN));
        Long curTimeZoneTimeStamp = DateUtil.toMilliSecond(time);
        System.out.println(curTimeZoneTimeStamp);
    }
}