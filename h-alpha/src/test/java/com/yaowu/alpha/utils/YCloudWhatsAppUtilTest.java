package com.yaowu.alpha.utils;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.model.dto.whatsapp.WhatsAppTemplateMessageDTO;
import com.ycloud.client.model.WhatsappMessage;
import com.ycloud.client.model.WhatsappTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class YCloudWhatsAppUtilTest {

    @Autowired
    private YCloudWhatsAppUtil yCloudWhatsAppUtil;

    @Test
    void sendTemplateMessage() {

        WhatsAppTemplateMessageDTO messageDTO = new WhatsAppTemplateMessageDTO();
        messageDTO.setFrom("+8618826112072");
        messageDTO.setTo("+861882611510");
        messageDTO.setTemplateName("mister_tool_en_template_marketing_03_20250522143626");
        messageDTO.setLanguageCode("en");

        WhatsappMessage message = yCloudWhatsAppUtil.sendTemplateMessage(messageDTO);
        System.out.println(JSONUtil.toJsonStr(message));
    }

    @Test
    void sendTextMessage() {
    }

    @Test
    void retrieveTemplate() {
        String wabaId = "573190672537518";
        String templateName = "mister_tool_en_template_marketing_03_20250522143626";
        String languageCode = "en";
        WhatsappTemplate template = yCloudWhatsAppUtil.retrieveTemplate(wabaId, templateName, languageCode);
        System.out.println(JSONUtil.toJsonStr(template));
    }
}