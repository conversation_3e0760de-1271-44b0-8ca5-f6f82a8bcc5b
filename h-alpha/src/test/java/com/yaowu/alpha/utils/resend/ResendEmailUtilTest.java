package com.yaowu.alpha.utils.resend;

import com.yaowu.alpha.utils.email.resend.ResendEmailUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class ResendEmailUtilTest {

    @Autowired
    private ResendEmailUtil resendEmailUtil;

    @Test
    void sendHtmlEmail() {
        String htmlContent = "<html>\n" +
                "<body>\n" +
                "<h1>Welcome to Haida</h1>\n" +
                "<p>Dear IMI Climate Control Team,</p>\n" +
                "\n" +
                "<p>Greetings from Haida, a leading manufacturer of precision metal stamping products based in Zhangjiagang, China. With over 20 years of expertise in the field, we specialize in delivering high-quality metal stamping components for various industries, including automotive, electrical, construction, machinery, and new energy.</p>\n" +
                "\n" +
                "<p>Following up on our previous conversation with <PERSON> regarding potential collaboration opportunities, we wanted to formally introduce Haida to your entire team. Our extensive product range includes components for window lift motors, seat motors, fuel pump motors, door lock systems, and more. We pride ourselves on our commitment to innovation, quality, and customer satisfaction, which has earned us a reputation as a trusted partner for global industry leaders.</p>\n" +
                "\n" +
                "<p>As we discussed with Rich, we are particularly excited about the possibility of providing our precision metal stamping solutions to IMI Climate Control. Our products are designed to meet the highest standards of performance and reliability, ensuring seamless integration into your HVAC systems. We believe our expertise in metal stamping can contribute to your mission of delivering sustainable and energy-efficient solutions worldwide.</p>\n" +
                "\n" +
                "<p>Building on our initial conversation, we would be delighted to further discuss how Haida can support your projects and provide tailored solutions to meet your specific needs. Please feel free to reach out to us for more information or to schedule a more detailed consultation.</p>\n" +
                "\n" +
                "<p>Best regards,</p>\n" +
                "<p>July</p>\n" +
                "<p>Key Account Manager</p>\n" +
                "<p>Haida</p>\n" +
                "</body>\n" +
                "</html>";
        String subject = "Discover Precision Metal Stamping Solutions from Haida";

        resendEmailUtil.sendHtmlEmail("<EMAIL>", "<EMAIL>",subject, htmlContent);
    }

    @Test
    void sendHtmlEmailWithReplyTo() {
        String htmlContent = "<html>\n" +
                "<body>\n" +
                "<h1>TEST2</h1>\n" +
                " <div style=\"text-align: center;\">\n" +
                "        <a href=\"mailto:#{sendEmail}\" class=\"cta-button\">Yes, I'm interested</a>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
        String subject = "TEST2";
        String replyTo = "<EMAIL>";
//        String replyTo = "<EMAIL>";
//        String to = "<EMAIL>";
        String to = "<EMAIL>";

        resendEmailUtil.sendHtmlEmail("<EMAIL>", to, replyTo, subject, htmlContent);
    }



}