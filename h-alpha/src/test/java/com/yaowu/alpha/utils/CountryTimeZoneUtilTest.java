package com.yaowu.alpha.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.LocalTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 国家时区工具类测试
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2023/07/10
 */
@Slf4j
class CountryTimeZoneUtilTest {

    @ParameterizedTest
    @CsvSource({
        "中国, Asia/Shanghai",
        "美国, America/New_York",
        "英国, Europe/London",
        "日本, Asia/Tokyo",
        "澳大利亚, Australia/Sydney",
        "Germany, Europe/Berlin",
        "Italy, Europe/Rome",
        "France, Europe/Paris",
        "spain, Europe/Madrid",
        "cn, Asia/Shanghai",
        "us, America/New_York",
        "uk, Europe/London",
        "jp, Asia/Tokyo",
        "北美洲, America/New_York",
        "南美洲, America/Sao_Paulo",
        "欧洲, Europe/London",
        "亚洲, Asia/Shanghai",
        "非洲, Africa/Cairo",
        "俄罗斯, Europe/Moscow"
    })
    @DisplayName("测试获取国家时区 - 直接匹配")
    void testGetTimeZone_DirectMatch(String country, String expected) {
        assertEquals(expected, CountryTimeZoneUtil.getTimeZone(country));
    }

    @ParameterizedTest
    @CsvSource({
        "中 国, Asia/Shanghai",
        "美  国, America/New_York",
        "英-国, Europe/London",
        "日_本, Asia/Tokyo",
        "United States, America/New_York",
        "United Kingdom, Europe/London"
    })
    @DisplayName("测试获取国家时区 - 特殊格式匹配")
    void testGetTimeZone_SpecialFormat(String country, String expected) {
        assertEquals(expected, CountryTimeZoneUtil.getTimeZone(country));
    }

    @ParameterizedTest
    @ValueSource(strings = {"中", "美", "英", "日", "德", "fr"})
    @DisplayName("测试获取国家时区 - 模糊匹配")
    void testGetTimeZone_FuzzyMatch(String country) {
        String timeZone = CountryTimeZoneUtil.getTimeZone(country);
        assertNotEquals("UTC", timeZone, "应能对'" + country + "'进行模糊匹配");
        log.info("国家[{}]模糊匹配到时区: {}", country, timeZone);
    }

    @Test
    @DisplayName("测试获取国家时区 - 空值和不存在的国家")
    void testGetTimeZone_NullAndNonExistent() {
        assertEquals("UTC", CountryTimeZoneUtil.getTimeZone(""));
        assertEquals("UTC", CountryTimeZoneUtil.getTimeZone(null));
        assertEquals("UTC", CountryTimeZoneUtil.getTimeZone("不存在的国家"));
    }

    @ParameterizedTest
    @CsvSource({
        "8, true",
        "12, true",
        "17, true",
        "7, false",
        "18, false",
        "20, false",
        "0, false"
    })
    @DisplayName("测试是否在工作时间内")
    void testIsInBusinessHours(int hour, boolean expected) {
        LocalTime time = LocalTime.of(hour, 0);
        assertEquals(expected, CountryTimeZoneUtil.isInBusinessHours(time));
    }

    @ParameterizedTest
    @CsvSource({
        "7, 1",      // 早上7点，距工作开始1小时
        "6, 2",      // 早上6点，距工作开始2小时
        "12, 0",     // 中午12点，已在工作时间内
        "18, 14",    // 晚上18点，距离明天工作开始14小时
        "22, 10"     // 晚上22点，距离明天工作开始10小时
    })
    @DisplayName("测试计算到达工作时间的小时数")
    void testHoursUntilBusinessHours(int hour, int expected) {
        LocalTime time = LocalTime.of(hour, 0);
        assertEquals(expected, CountryTimeZoneUtil.hoursUntilBusinessHours(time));
    }

    @Test
    @DisplayName("测试获取工作时间范围")
    void testGetBusinessHours() {
        assertEquals(LocalTime.of(8, 0), CountryTimeZoneUtil.getBusinessStartTime());
        assertEquals(LocalTime.of(18, 0), CountryTimeZoneUtil.getBusinessEndTime());
    }
} 