package com.yaowu.alpha.controller.v1.proxy;

import cn.hutool.json.JSONUtil;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.model.dto.group.GroupMessagePageDTO;
import com.yaowu.alpha.model.dto.group.GroupPageDTO;
import com.yaowu.alpha.model.dto.group.GroupStatusUpdateDTO;
import com.yaowu.alpha.model.dto.group.RemoteGroupPageDTO;
import com.yaowu.alpha.model.vo.proxy.group.GroupDetailVO;
import com.yaowu.alpha.model.vo.proxy.group.GroupMessageVO;
import com.yaowu.alpha.model.vo.proxy.group.GroupPageVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;



@SpringBootTest
public class DigitalBotGroupControllerTest {

    @Autowired
    private IProxyGroupBizService proxyGroupBizService;

    @Test
    @DisplayName("分页查询群组-通过")
    void pageGroups() {
        RemoteGroupPageDTO dto = new RemoteGroupPageDTO();
        BasePage<GroupPageVO> result = proxyGroupBizService.pageGroups(dto);
        System.out.println(JSONUtil.toJsonStr(result));
    }

    @Test
    @DisplayName("测试群聊状态(人工/代理)-通过")
    void updateGroupStatus() {
        // 1. 准备测试数据
        GroupStatusUpdateDTO dto = new GroupStatusUpdateDTO();
        dto.setId(1897922564174610434L);  // 使用已知存在的群ID
        dto.setProxyStatus(1);  // 设置为代理状态
        // 2. 执行测试
        Boolean result = proxyGroupBizService.updateGroupStatus(dto);
    }

    @Test
    @DisplayName("查询群聊详情信息-通过")
    void getGroupDetail() {
        // 1. 准备测试数据
        Long groupId = 1897922564174610434L;  // 使用已知存在的群ID
        // 2. 执行测试
        GroupDetailVO result = proxyGroupBizService.getGroupDetail(groupId);
        System.out.println(JSONUtil.toJsonStr(result));
    }

    @Test
    @DisplayName("群聊消息返回对象-通过")
    void pageGroupMessages() {
        // 1. 准备测试数据
        GroupMessagePageDTO dto = new GroupMessagePageDTO();
        dto.setGroupId(1897922564174610434L);
        // 2. 执行测试
        BasePage<GroupMessageVO> result = proxyGroupBizService.pageGroupMessages(dto);
        System.out.println(JSONUtil.toJsonStr(result));
    }
}