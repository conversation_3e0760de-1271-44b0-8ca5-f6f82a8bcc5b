package com.yaowu.alpha.controller.v1.proxy;

import cn.hutool.json.JSONUtil;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.llm.service.batis.service.IAgentSessionService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.model.dto.friend.ChatMessageQueryDTO;
import com.yaowu.alpha.model.dto.friend.QueryChatSessionDTO;
import com.yaowu.alpha.model.dto.friend.RemoteFriendPageDTO;
import com.yaowu.alpha.model.dto.friend.FriendProxyStatusUpdateDTO;
import com.yaowu.alpha.model.dto.friend.PageProxyAccountFriendTreeDTO;
import com.yaowu.alpha.model.dto.friend.FriendQueryRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.FriendMarkReadRequest;
import com.yaowu.alpha.model.vo.friend.ChatMessageVO;
import com.yaowu.alpha.model.vo.friend.ChatSessionVO;
import com.yaowu.alpha.model.vo.friend.FriendPageVO;
import com.yaowu.alpha.model.vo.friend.ProxyAccountFriendPageVO;
import com.yaowu.alpha.model.vo.friend.FriendDetailVO;
import com.yaowu.alpha.model.vo.proxy.CompanyBackgroundTaskVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import com.yaowu.alpha.model.dto.friend.CompanyBackgroundTaskPageDTO;

@SpringBootTest
public class DigitalBotFriendControllerTest {

    @Autowired
    private IProxyAccountFriendBizService friendBizService;

    @Autowired
    private  IAgentSessionService agentSessionService;

    @Test
    @DisplayName("测试分页查询好友信息-通过")
    void pageFriends() {
        RemoteFriendPageDTO friendPageDTO = new RemoteFriendPageDTO();
        BasePage<FriendPageVO> friendPageVOBasePage = friendBizService.pageFriends(friendPageDTO);
        System.out.println(JSONUtil.toJsonStr(friendPageVOBasePage));
    }

    @Test
    @DisplayName("更新好友代理状态-通过")
    void updateProxyStatus() {
        //修改好友代理状态
        FriendProxyStatusUpdateDTO dto = new FriendProxyStatusUpdateDTO();
        dto.setId(1900475239162499074L);
        dto.setFriendStatus(2);
        Boolean b = friendBizService.updateFriendStatus(dto);
    }
    @Test
    @DisplayName("分页查询代理账户和用户关联树-通过")
    void pageProxyAccountFriendTree() {
        PageProxyAccountFriendTreeDTO pageProxyAccountFriendTreeDTO = new PageProxyAccountFriendTreeDTO();
        BasePage<ProxyAccountFriendPageVO> proxyAccountTreePageVOBasePage = friendBizService.pageProxyAccountFriendTree(pageProxyAccountFriendTreeDTO);
         System.out.println(JSONUtil.toJsonStr(proxyAccountTreePageVOBasePage));
    }

    @Test
    @DisplayName("查询查询会话信息-通过")
    void getChatMessages() {
        ChatMessageQueryDTO chatMessageQueryDTO = new ChatMessageQueryDTO();
        chatMessageQueryDTO.setFriendProxyId("5169da50a5aa4bdfa6d7f69c946b5adc");
        chatMessageQueryDTO.setProxyAccountId(1907644609615699969L);
        BasePage<ChatMessageVO> chatMessageVOBasePage = friendBizService.pageMessages(chatMessageQueryDTO);
        System.out.println(JSONUtil.toJsonStr(chatMessageVOBasePage));
    }

    @Test
    @DisplayName("查询会话聊天-通过")
    void getChatSession() {
        Long proxyAccountId = 1902595444438466561L;
        String friendIdProxyId = "test";
        QueryChatSessionDTO queryChatSessionDTO = new QueryChatSessionDTO();
        queryChatSessionDTO.setFriendProxyId(friendIdProxyId);
        queryChatSessionDTO.setProxyAccountId(proxyAccountId);
        ChatSessionVO chatSession = friendBizService.getChatSession(queryChatSessionDTO);
        System.out.println(JSONUtil.toJsonStr(chatSession));
    }

    @Test
    @DisplayName("查询好友详情-通过")
    void detail() {
        FriendQueryRequestDTO dto = new FriendQueryRequestDTO();
        Long proxyAccountId = 1L;
        String friendIdProxyId = "wxid_pupukznnyvv222";
        dto.setAccountId(proxyAccountId);
        dto.setFriendProxyId(friendIdProxyId);
        FriendDetailVO friendDetailVO = friendBizService.detail(dto);
        System.out.println(JSONUtil.toJsonStr(friendDetailVO));
    }

    @Test
    @DisplayName("标记消息为已读-通过")
    void markMessagesAsRead() {
        FriendMarkReadRequest request = new FriendMarkReadRequest();
        Long proxyAccountId = 1L;
        String friendIdProxyId = "wxid_pupukznnyvv222";
        request.setAccountId(proxyAccountId);
        request.setFriendProxyId(friendIdProxyId);
        Boolean result = friendBizService.markMessagesAsRead(request);
        System.out.println("标记消息已读结果: " + result);
    }

    @Test
    @DisplayName("分页查询公司背调任务")
    public void testPageBackgroundTasks() {
        CompanyBackgroundTaskPageDTO dto = new CompanyBackgroundTaskPageDTO();
        dto.setSize(10);
        dto.setCompanyName("测试公司");
        dto.setTaskStatus(1);
        
        BasePage<CompanyBackgroundTaskVO> result = friendBizService.pageBackgroundTasks(dto);
        System.out.println("分页查询结果: " + JSONUtil.toJsonStr(result));
    }
}