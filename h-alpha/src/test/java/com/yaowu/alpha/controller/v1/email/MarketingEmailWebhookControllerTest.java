package com.yaowu.alpha.controller.v1.email;

import com.yaowu.alpha.utils.email.resend.ResendWebhookVerifier;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * MarketingEmailWebhookController测试类
 */
@SpringBootTest
class MarketingEmailWebhookControllerTest {

    @Autowired
    private MarketingEmailWebhookController controller;
    private final String testSecret = "whsec_MfKQ9r8GKYqrTwjUPD8ILPZIo2LaLaSw";


    @Test
    @DisplayName("应该成功验证有效的webhook签名")
    void shouldVerifyValidWebhookSignature() {
        // Given
        String svixId = "msg_p5jXN8AQM9LWM0D4loKWxJek";
        String svixTimestamp = String.valueOf(Instant.now().getEpochSecond());
        String payload = "{\"event_type\":\"email.delivered\",\"data\":{\"id\":\"test-123\"}}";
        
        // 生成有效的签名
        String signature = generateTestSignature(svixId, svixTimestamp, payload, testSecret);
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("svix-id", svixId);
        request.addHeader("svix-timestamp", svixTimestamp);
        request.addHeader("svix-signature", "v1," + signature);

        // When
        ResponseEntity<String> response = controller.resendHook(payload, request);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Webhook received", response.getBody());
    }

    @Test
    @DisplayName("当缺少必要headers时应该返回400错误")
    void shouldReturn400WhenMissingHeaders() {
        // Given
        String payload = "{\"event_type\":\"email.delivered\"}";
        MockHttpServletRequest request = new MockHttpServletRequest();
        // 故意不添加headers

        // When
        ResponseEntity<String> response = controller.resendHook(payload, request);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Missing required headers", response.getBody());
    }

    @Test
    @DisplayName("当签名无效时应该返回401错误")
    void shouldReturn401WhenInvalidSignature() {
        // Given
        String svixId = "msg_p5jXN8AQM9LWM0D4loKWxJek";
        String svixTimestamp = String.valueOf(Instant.now().getEpochSecond());
        String payload = "{\"event_type\":\"email.delivered\"}";
        String invalidSignature = "invalid_signature";
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("svix-id", svixId);
        request.addHeader("svix-timestamp", svixTimestamp);
        request.addHeader("svix-signature", "v1," + invalidSignature);

        // When
        ResponseEntity<String> response = controller.resendHook(payload, request);

        // Then
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertEquals("Invalid signature", response.getBody());
    }

    @Test
    @DisplayName("当时间戳过期时应该返回401错误")
    void shouldReturn401WhenTimestampExpired() {
        // Given
        String svixId = "msg_p5jXN8AQM9LWM0D4loKWxJek";
        // 使用10分钟前的时间戳（超过5分钟容差）
        String svixTimestamp = String.valueOf(Instant.now().getEpochSecond() - 600);
        String payload = "{\"event_type\":\"email.delivered\"}";
        
        // 生成有效的签名
        String signature = generateTestSignature(svixId, svixTimestamp, payload, testSecret);
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("svix-id", svixId);
        request.addHeader("svix-timestamp", svixTimestamp);
        request.addHeader("svix-signature", "v1," + signature);

        // When
        ResponseEntity<String> response = controller.resendHook(payload, request);

        // Then
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertEquals("Invalid timestamp", response.getBody());
    }

    @Test
    @DisplayName("应该支持多个版本的签名")
    void shouldSupportMultipleSignatureVersions() {
        // Given
        String svixId = "msg_p5jXN8AQM9LWM0D4loKWxJek";
        String svixTimestamp = String.valueOf(Instant.now().getEpochSecond());
        String payload = "{\"event_type\":\"email.delivered\"}";
        
        // 生成有效的签名
        String signature1 = generateTestSignature(svixId, svixTimestamp, payload, testSecret);
        String signature2 = "invalid_signature";
        
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("svix-id", svixId);
        request.addHeader("svix-timestamp", svixTimestamp);
        request.addHeader("svix-signature", "v1," + signature2 + " v1," + signature1);

        // When
        ResponseEntity<String> response = controller.resendHook(payload, request);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    /**
     * 生成测试用的签名
     */
    private String generateTestSignature(String svixId, String svixTimestamp, String payload, String secret) {
        ResendWebhookVerifier verifier = new ResendWebhookVerifier(secret);
        return verifier.generateSignature(svixId, svixTimestamp, payload);
    }
} 