<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.alpha.domain.email.service.batis.mapper.MarketingEmailEventLogMapper">

    <!-- 根据条件查询邮件事件类型去重数据（按任务ID和事件类型去重） -->
    <select id="getEventStatsGroupByType" resultType="com.yaowu.alpha.model.dto.email.EmailEventTypeStatsDTO">
        SELECT DISTINCT
            log.marketing_email_task_id AS taskId,
            log.event_type AS eventType
        FROM b_marketing_email_event_log log
        LEFT JOIN b_marketing_email_task task ON log.marketing_email_task_id = task.id
        <where>
            <if test="tenantId != null">
                AND task.tenant_id = #{tenantId}
            </if>
            <if test="startTime != null">
                AND log.event_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND log.event_time &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>
