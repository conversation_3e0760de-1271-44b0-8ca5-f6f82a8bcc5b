<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookGroupMemberMapper">

    <!-- 根据任务ID列表统计每个任务的群组成员数量 -->
    <select id="getGroupMemberStatisticByTaskIds" resultType="java.util.Map">
        SELECT
        task_id as taskId,
        COUNT(*) as count
        FROM b_facebook_group_member
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND delete_flag = 0
        GROUP BY task_id
    </select>

    <select id="getGroupMemberStatisticByGroupIds" resultType="java.util.Map">
        SELECT
        group_id as groupId,
        COUNT(*) as count
        FROM b_facebook_group_member
        WHERE group_id IN
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
        AND delete_flag = 0
        GROUP BY group_id
    </select>

</mapper> 