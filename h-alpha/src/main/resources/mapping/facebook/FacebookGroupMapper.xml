<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookGroupMapper">

    <!-- 根据任务ID列表统计每个任务的群组数量 -->
    <select id="getGroupStatisticByTaskIds" resultType="java.util.Map">
        SELECT
        task_id as taskId,
        COUNT(*) as count
        FROM b_facebook_group
        WHERE task_id IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND delete_flag = 0
        GROUP BY task_id
    </select>

</mapper> 