<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.alpha.domain.statistics.service.batis.mapper.StatisticsCommonMapper">

    <!-- 邮件统计查询 -->
    <select id="selectEmailStatistics" resultType="com.yaowu.alpha.model.vo.statistics.EmailStatisticsVO">
        SELECT
        SUM(CASE WHEN email_status = 0 THEN 1 ELSE 0 END) as pendingCount,
        SUM(CASE WHEN email_status = 1 THEN 1 ELSE 0 END) as sentCount,
        SUM(CASE WHEN email_status = 2 OR email_status = 3 THEN 1 ELSE 0 END) as deliveredCount,
        SUM(CASE WHEN email_status = 90 THEN 1 ELSE 0 END) as complainedCount,
        <PERSON>UM(CASE WHEN email_status = 91 THEN 1 ELSE 0 END) as bouncedCount,
        SUM(CASE WHEN email_status = 92 THEN 1 ELSE 0 END) as openedCount,
        SUM(CASE WHEN email_status = 93 THEN 1 ELSE 0 END) as clickedCount,
        SUM(CASE WHEN email_status = 94 THEN 1 ELSE 0 END) as repliedCount
        FROM b_marketing_email_task
        <where>
            delete_flag = 0
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time &lt;= #{query.endTime}
            </if>
        </where>
    </select>

    <!-- 每日邮件统计查询 -->
    <select id="selectDailyEmailStatistics" resultType="com.yaowu.alpha.model.vo.statistics.DailyEmailStatisticsVO">
        SELECT
            DATE(e.event_time) AS eventDate,
            SUM(CASE WHEN e.event_type = 'email.failed' THEN 1 ELSE 0 END) as failedCount,
            SUM(CASE WHEN e.event_type = 'email.sent' THEN 1 ELSE 0 END) as sentCount,
            SUM(CASE WHEN e.event_type = 'email.delivered' OR e.event_type = 'email.delivery_delayed' THEN 1 ELSE 0 END) as deliveredCount,
            SUM(CASE WHEN e.event_type = 'email.complained' THEN 1 ELSE 0 END) as complainedCount,
            SUM(CASE WHEN e.event_type = 'email.bounced' THEN 1 ELSE 0 END) as bouncedCount,
            SUM(CASE WHEN e.event_type = 'email.opened' THEN 1 ELSE 0 END) as openedCount,
            SUM(CASE WHEN e.event_type = 'email.clicked' THEN 1 ELSE 0 END) as clickedCount,
            SUM(CASE WHEN e.event_type = 'email.replied' THEN 1 ELSE 0 END) as repliedCount
        FROM b_marketing_email_event_log e 
        LEFT JOIN b_marketing_email_task t ON e.marketing_email_task_id = t.id
        <where>
            e.delete_flag = 0
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND t.tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(e.event_time) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(e.event_time) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY DATE(e.event_time)
        ORDER BY DATE(e.event_time)
    </select>

    <!-- 对话统计查询 -->
    <select id="selectChatStatistics" resultType="com.yaowu.alpha.model.vo.statistics.ChatStatisticsVO">
        SELECT
            c.third_type AS thirdType,
            COUNT(DISTINCT m.session_proxy_id, m.account_id) AS conversationCount
        FROM b_proxy_chat_message m 
        LEFT JOIN b_proxy_account c ON m.account_id = c.id
        <where>
            m.delete_flag = 0 AND m.group_flag = 0
            AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) IS NOT NULL
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND m.tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY c.third_type
        ORDER BY c.third_type
    </select>

    <!-- 渠道对话统计查询（新增方法，包含to_user_proxy_id条件） -->
    <select id="selectChatReplyStatistics" resultType="com.yaowu.alpha.model.vo.statistics.ChatStatisticsVO">
        SELECT
        COALESCE(c.third_type, 0) AS thirdType,
        COUNT(DISTINCT m.session_proxy_id, m.account_id) AS conversationCount
        FROM b_proxy_chat_message m 
        LEFT JOIN b_proxy_account c ON m.account_id = c.id
        <where>
            m.delete_flag = 0 AND m.group_flag = 0 AND m.to_user_proxy_id = c.proxy_id
            AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) IS NOT NULL
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND m.tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY c.third_type
        ORDER BY c.third_type
    </select>

    <!-- 每日对话客户数统计查询 -->
    <select id="selectDailyChatStatistics" resultType="com.yaowu.alpha.model.vo.statistics.DailyChatStatisticsVO">
        SELECT
        COALESCE(c.third_type, 0) AS thirdType,
        DATE(FROM_UNIXTIME(m.message_timestamp/1000)) AS chatDate,
        COUNT(DISTINCT m.session_proxy_id, m.account_id) AS customerCount
        FROM b_proxy_chat_message m
        LEFT JOIN b_proxy_account c ON m.account_id = c.id
        <where>
            m.delete_flag = 0 AND m.group_flag = 0
            AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) IS NOT NULL
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND m.tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY c.third_type, DATE(FROM_UNIXTIME(m.message_timestamp/1000))
        ORDER BY c.third_type, DATE(FROM_UNIXTIME(m.message_timestamp/1000))
    </select>

    <!-- 每日回复客户数统计查询 -->
    <select id="selectDailyChatReplyStatistics" resultType="com.yaowu.alpha.model.vo.statistics.DailyChatStatisticsVO">
        SELECT
        c.third_type AS thirdType,
        DATE(FROM_UNIXTIME(m.message_timestamp/1000)) AS chatDate,
        COUNT(DISTINCT m.session_proxy_id, m.account_id) AS customerCount
        FROM b_proxy_chat_message m
        LEFT JOIN b_proxy_account c ON m.account_id = c.id
        <where>
            m.delete_flag = 0 AND m.group_flag = 0 AND m.to_user_proxy_id = c.proxy_id
            AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) IS NOT NULL
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND m.tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(FROM_UNIXTIME(m.message_timestamp/1000)) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY c.third_type, DATE(FROM_UNIXTIME(m.message_timestamp/1000))
        ORDER BY c.third_type, DATE(FROM_UNIXTIME(m.message_timestamp/1000))
    </select>

    <!-- 打招呼各渠道任务统计查询 -->
    <select id="selectGreetingTaskStatistics" resultType="com.yaowu.alpha.model.vo.statistics.GreetingTaskStatisticsVO">
        SELECT
            third_type AS thirdType,
            SUM(CASE WHEN task_status = 0 THEN 1 ELSE 0 END) as pendingCount,
            SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END) as executedCount,
            SUM(CASE WHEN task_status = -1 THEN 1 ELSE 0 END) as failedCount,
            COUNT(*) as totalCount
        FROM b_proxy_greeting_task
        <where>
            delete_flag = 0
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY third_type
        ORDER BY third_type
    </select>

    <!-- 每日打招呼任务执行情况统计查询 -->
    <select id="selectDailyGreetingTaskStatistics" resultType="com.yaowu.alpha.model.vo.statistics.DailyGreetingTaskStatisticsVO">
        SELECT
            third_type AS thirdType,
            DATE(task_success_time) AS taskDate,
            SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END) as executedCount,
            SUM(CASE WHEN task_status = -1 THEN 1 ELSE 0 END) as failedCount,
            COUNT(*) as totalExecutedCount
        FROM b_proxy_greeting_task
        <where>
            delete_flag = 0 AND task_status &lt;> 0
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND DATE(task_success_time) >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND DATE(task_success_time) &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY third_type, DATE(task_success_time)
        ORDER BY third_type, DATE(task_success_time)
    </select>

    <!-- 客户培育流程统计查询 -->
    <select id="selectCustomerNurtureFlowStatistics" resultType="com.yaowu.alpha.model.vo.statistics.CustomerNurtureFlowStatisticsVO">
        SELECT
        customer_contact_type AS customerContactType,
        SUM(CASE WHEN flow_status = -1 THEN 1 ELSE 0 END) as abnormalCount,
        SUM(CASE WHEN flow_status = 0 THEN 1 ELSE 0 END) as pendingCount,
        SUM(CASE WHEN flow_status = 10 THEN 1 ELSE 0 END) as executingCount,
        SUM(CASE WHEN flow_status = 98 THEN 1 ELSE 0 END) as terminatedCount,
        SUM(CASE WHEN flow_status = 99 THEN 1 ELSE 0 END) as completedCount,
        COUNT(*) as totalCount
        FROM b_nurture_customer_flow
        <where>
            delete_flag = 0
            <if test="query.tenantIds != null and query.tenantIds.size() > 0">
                AND tenant_id IN
                <foreach collection="query.tenantIds" item="tenantId" open="(" separator="," close=")">
                    #{tenantId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                AND create_time >= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                AND create_time &lt;= #{query.endTime}
            </if>
        </where>
        GROUP BY customer_contact_type
        ORDER BY customer_contact_type
    </select>

</mapper>
