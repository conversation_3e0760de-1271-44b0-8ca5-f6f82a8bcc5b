<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yaowu.alpha.domain.collection.service.batis.mapper.DataCollectionRawMapper">

    <!-- 批量统计任务的采集数据数量 -->
    <select id="countByTaskIds" parameterType="java.util.List" resultType="com.yaowu.alpha.model.bo.collection.TaskDataCountBO">
        SELECT 
            task_id,
            COUNT(*) AS data_count
        FROM b_data_collection_raw_data
        WHERE delete_flag = 0
        <if test="taskIds != null and taskIds.size() > 0">
            AND task_id IN
            <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        GROUP BY task_id
    </select>

</mapper> 