<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yaowu.alpha.domain.common.service.batis.mapper.AreaMapper">

    <!-- 查询指定国家下的所有城市 -->
    <select id="selectCitiesByCountryAreaId" parameterType="java.lang.Long" resultType="com.yaowu.alpha.model.entity.common.Area">
        SELECT 
            *
        FROM b_area 
        WHERE parent_id IN (
            SELECT area_id
            FROM b_area 
            WHERE parent_id = #{countryAreaId}
            AND delete_flag = 0
        )
        AND delete_flag = 0
        ORDER BY area_id
    </select>

</mapper>
