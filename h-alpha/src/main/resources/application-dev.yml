spring:
  cloud:
    # k8s
    kubernetes:
      enabled: true # 是否启用k8s集成
      discovery:
        enabled: true # 启用服务发现
        all-namespaces: false # 允许所有命名空间的服务被发现
        include-not-ready-addresses: false # 包括未标记为“就绪”的服务也能被发现
        namespaces:
          - ${POD_NAMESPACE}
  # 数据库
  datasource:
    url: jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/h_alpha?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}

  # redis配置
  data:
    redis:
      database: 0
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      redisson:
        file: classpath:redisson/redisson-dev.yml

# security权限配置
security:
  oauth2:
    resource:
      # 开启资源管理器
      enabled: true
      # 开启方法安全认证
      method:
        enabled: false

remote:
  service:
    third-party: 'g-third-party'
    passport: 'h-passport'
    pms: 'h-pms'
    notice: 'g-notice'
    melina: 'h-melina'
    llm: 'mstar-llm-rebot'
    h-riskcontrol: 'h-riskcontrol'
    customer-service: 'h-customer-service'
    hera: 'h-hera'
    h-agent-service: 'h-agent-service'


# xxl-job配置
xxl:
  job:
    executor:
      ### 执行器AppName
      appname: dev-h-alpha


# 微信消息工具回调地址配置
wetool:
  upload-url: https://devgateway.yaowutech.cn/h-alpha/v1/wetool/upload

# whatsapp
whatsapp:
  baseUrl: http://h-alpha-waclient