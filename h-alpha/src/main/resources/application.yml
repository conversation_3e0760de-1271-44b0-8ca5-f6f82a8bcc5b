server:
  # 端口
  port: 8016
  # 设置webflux netty的请求头大小为100kb
  max-http-request-header-size: 102400

spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: h-alpha
  profiles:
    active: default
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************
    username: g-dev
    password: D6nIFWn48UP40SKy
    hikari:
      maxLifetime: 1765000 #一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒以上
      maximumPoolSize: 30 #连接池中允许的最大连接数。缺省值：10；推荐的公式：((core_count * 2) + effective_spindle_count)
      minimum-idle: 10
  cloud:
    # 负载均衡
    loadbalancer:
      # cloud 20.0.3版本已经废掉Ribbon，自己实现了spring cloud loadbalancer
      enabled: true
    # k8s
    kubernetes:
      enabled: false # 是否启用k8s集成
      discovery:
        enabled: false # 关闭服务发现
        all-namespaces: false # 允许所有命名空间的服务被发现
        include-not-ready-addresses: false # 包括未标记为"就绪"的服务也能被发现
        namespaces:
          - ${POD_NAMESPACE}
      config:
        #        name: default-name
        #        namespace: default-namespace
        enabled: false # 关闭configMap配置
      loadbalancer:
        enabled: true # 开启负载均衡
        mode: SERVICE # 在k8s的service做均衡
      secrets:
        enabled: false
      leader:
        enabled: false
  # redis配置
  data:
    redis:
      database: 0
      timeout: 3s
      host: r-uf6dd4d2hikjs2htaipd.redis.rds.aliyuncs.com
      port: 6379
      password: 4oRa2M4bC2
      lettuce:
        pool:
          max-idle: 50
          min-idle: 10
          max-active: 50
      client-type: lettuce
      redisson:
        file: classpath:redisson/redisson.yml
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  # mq 配置
  rabbitmq:
    host: amqp-cn-lbj3e7cwl004.cn-shanghai.amqp-1.net.mq.amqp.aliyuncs.com
    port: 5672
    username: MjphbXFwLWNuLWxiajNlN2N3bDAwNDpMVEFJNXQ3TjZtRmhxODJRZm5lN2Fvdms=
    password: QkUyN0JCNkM3OUEyOUQ0QTY3OTAwRjU0ODQxMzQwQkU3NUNDNUM5MjoxNjk1NzE5NTcwNzc0
    virtual-host: dev  # 环境隔离，dev,test,test1,test2,pre等
    listener:
      simple:
        auto-startup: false
      direct:
        auto-startup: false

feign:
  circuitbreaker:
    enabled: true

# mybatis-plus配置
mybatis-plus:
  check-config-location: true
  mapper-locations: classpath*:mapping/**/*.xml
  typeEnumsPackage: com.yaowu.alpha.enums.*
  type-aliases-package: com.yaowu.alpha.model.entity,com.yaowu.alpha.**.entity
  configuration:
    mapUnderscoreToCamelCase: true # 是否开启自动驼峰命名规则（camel case）映射
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  global-config:
    db-config:
      db-type: mysql # 数据库类型
      id-type: auto # 主键id  AUTO(0):自增ID，NONE(1)：未设置主键类型，INPUT(2)：用户输入ID，ID_WORKER(3)：全局唯一ID (64位idWorker)，UUID(4)：全局唯一ID (32位UUID)，ID_WORKER_STR(5)：字符串全局唯一ID (64位idWorker 的字符串表示)
      db-column-underline: true #驼峰下划线转换
  type-handlers-package: com.yaowu.alpha.model.entity.typehandler

# 开启SQL日志
custom-sql-logging:
  enabled: true

# 健康检查 http://127.0.0.1:端口/actuator/health
management:
  endpoints:
    # 使用web-http请求暴漏站点
    web:
      exposure:
        # 只暴露健康检查
        include: 'health'
  endpoint:
    health:
      # 不展示细节
      show-details: never
  health:
    elasticsearch:
      #关闭es监控检查
      enabled: false


# security权限配置
security:
  oauth2:
    auth:
      # 登陆密码相关
      password:
        # 密码是否加密
        encrypt-enable: true
        # 密码公钥私钥
        public-key-value:
          MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJU/jR/pHiteVwoiflT70JrwrJyaLfhti7zhj3mAuXtAtW+gwBKmNNbn3De4OagnrWnoDU//fB8lqxiRqlwmgNgO+TD+RLcBvnR/EyIzxrWDDEVx8zUFjuS7YCL03veO9BMnyR9oYUGnwE0ETko32YGwGR6IxccxXkti3euZ0uKQIDAQAB
        private-key-value:
          MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIlT+NH+keK15XCiJ+VPvQmvCsnJot+G2LvOGPeYC5e0C1b6DAEqY01ufcN7g5qCetaegNT/98HyWrGJGqXCaA2A75MP5EtwG+dH8TIjPGtYMMRXHzNQWO5LtgIvTe9470EyfJH2hhQafATQROSjfZgbAZHojFxzFeS2Ld65nS4pAgMBAAECgYBY6Cl14qcWLIb/dav4lLghFdcZLtaoekfExrh5S1FuBJ020SmGc6fYkIkRTtyz4neejP2BaLpBMSMwP0AmQodUB6sgle6Fmyfkak/acePq64YCmhSBqOkXsM7TUsUv5NKU5KSu52bcOWGQTfNQj25n0HwfcVhBHW/YLU0giZSltQJBAOI/LWFjnLyoAnlPSwP5FVPQlPMTAYA4KF581rosHLSDLwttshQGES7J0/JZ+NJ0ygbCA8pBFyvMXrfPlY1xhtMCQQCbY0GHJRCasZ05ItYjmVijmNiSmjDsbVkccfzKPsM036odGu6z6n0fuy7P9Uu/FalAJ7wiRBZ9XFOTbs0ArFGTAkEAhH7TKLGZXLJXoItbNxxr7QW99QwwWNKKcatSZPzoYp5gsqSvISLcdTYlNUyTd0yF86czaQLteuzYm3Sp9IeDmQJAa2/QQeHlvh+qRnQIvWpseAjZTVvhEcd1Qo6pqyiEDc3Lr/4MJ0etJcBlWJIQwlj2wg33LXYIWrr4akrE4X9DkwJAJ0vPTBeRxZnJfrHSOGv7JzF1BKzW1Sx+4simsHAtODfoy6Lh1SfUJACIOOBufa46jK9bcmmLpQ6Sb11a4Prf1Q==
      # jwt公钥私钥
      jwt:
        public-key-value:
          MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJU/jR/pHiteVwoiflT70JrwrJyaLfhti7zhj3mAuXtAtW+gwBKmNNbn3De4OagnrWnoDU//fB8lqxiRqlwmgNgO+TD+RLcBvnR/EyIzxrWDDEVx8zUFjuS7YCL03veO9BMnyR9oYUGnwE0ETko32YGwGR6IxccxXkti3euZ0uKQIDAQAB
        private-key-value:
          MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIlT+NH+keK15XCiJ+VPvQmvCsnJot+G2LvOGPeYC5e0C1b6DAEqY01ufcN7g5qCetaegNT/98HyWrGJGqXCaA2A75MP5EtwG+dH8TIjPGtYMMRXHzNQWO5LtgIvTe9470EyfJH2hhQafATQROSjfZgbAZHojFxzFeS2Ld65nS4pAgMBAAECgYBY6Cl14qcWLIb/dav4lLghFdcZLtaoekfExrh5S1FuBJ020SmGc6fYkIkRTtyz4neejP2BaLpBMSMwP0AmQodUB6sgle6Fmyfkak/acePq64YCmhSBqOkXsM7TUsUv5NKU5KSu52bcOWGQTfNQj25n0HwfcVhBHW/YLU0giZSltQJBAOI/LWFjnLyoAnlPSwP5FVPQlPMTAYA4KF581rosHLSDLwttshQGES7J0/JZ+NJ0ygbCA8pBFyvMXrfPlY1xhtMCQQCbY0GHJRCasZ05ItYjmVijmNiSmjDsbVkccfzKPsM036odGu6z6n0fuy7P9Uu/FalAJ7wiRBZ9XFOTbs0ArFGTAkEAhH7TKLGZXLJXoItbNxxr7QW99QwwWNKKcatSZPzoYp5gsqSvISLcdTYlNUyTd0yF86czaQLteuzYm3Sp9IeDmQJAa2/QQeHlvh+qRnQIvWpseAjZTVvhEcd1Qo6pqyiEDc3Lr/4MJ0etJcBlWJIQwlj2wg33LXYIWrr4akrE4X9DkwJAJ0vPTBeRxZnJfrHSOGv7JzF1BKzW1Sx+4simsHAtODfoy6Lh1SfUJACIOOBufa46jK9bcmmLpQ6Sb11a4Prf1Q==
    resource:
      # 开启资源管理器
      enabled: false
      # 开启方法安全认证
      method:
        enabled: false
      # jwt公钥私钥
      jwt:
        public-key-value:
          MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJU/jR/pHiteVwoiflT70JrwrJyaLfhti7zhj3mAuXtAtW+gwBKmNNbn3De4OagnrWnoDU//fB8lqxiRqlwmgNgO+TD+RLcBvnR/EyIzxrWDDEVx8zUFjuS7YCL03veO9BMnyR9oYUGnwE0ETko32YGwGR6IxccxXkti3euZ0uKQIDAQAB
        private-key-value:
          MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIlT+NH+keK15XCiJ+VPvQmvCsnJot+G2LvOGPeYC5e0C1b6DAEqY01ufcN7g5qCetaegNT/98HyWrGJGqXCaA2A75MP5EtwG+dH8TIjPGtYMMRXHzNQWO5LtgIvTe9470EyfJH2hhQafATQROSjfZgbAZHojFxzFeS2Ld65nS4pAgMBAAECgYBY6Cl14qcWLIb/dav4lLghFdcZLtaoekfExrh5S1FuBJ020SmGc6fYkIkRTtyz4neejP2BaLpBMSMwP0AmQodUB6sgle6Fmyfkak/acePq64YCmhSBqOkXsM7TUsUv5NKU5KSu52bcOWGQTfNQj25n0HwfcVhBHW/YLU0giZSltQJBAOI/LWFjnLyoAnlPSwP5FVPQlPMTAYA4KF581rosHLSDLwttshQGES7J0/JZ+NJ0ygbCA8pBFyvMXrfPlY1xhtMCQQCbY0GHJRCasZ05ItYjmVijmNiSmjDsbVkccfzKPsM036odGu6z6n0fuy7P9Uu/FalAJ7wiRBZ9XFOTbs0ArFGTAkEAhH7TKLGZXLJXoItbNxxr7QW99QwwWNKKcatSZPzoYp5gsqSvISLcdTYlNUyTd0yF86czaQLteuzYm3Sp9IeDmQJAa2/QQeHlvh+qRnQIvWpseAjZTVvhEcd1Qo6pqyiEDc3Lr/4MJ0etJcBlWJIQwlj2wg33LXYIWrr4akrE4X9DkwJAJ0vPTBeRxZnJfrHSOGv7JzF1BKzW1Sx+4simsHAtODfoy6Lh1SfUJACIOOBufa46jK9bcmmLpQ6Sb11a4Prf1Q==
    # 客户端配置类型，yaml、nacos
    client-config-type: nacos

# 日志
log:
  # 开启api日志
  apilog: true

swagger:
  show: true

# 对象存储
os:
  type: TX
  enabled: true
  sign:
    enabled: true
  # 腾讯cos
  cos:
    secret-id: AKIDHPK1gv33sXyCE5nJdljT4TffT8ctviGa
    secret-key: 80d3EBuQilZ8TbU8XkhRwCE0XoqdDYhc
    app-id: 1304519326
    buckets:
      - id: private
        region-name: ap-shanghai
        bucket: g-server-private

auth:
  matchers:
    - '/v1/web/common/user/create-user'

# feigh调用请求头设置
feign-header:
  list:
    - key: SERVICE-INVOKE-SECRET
      value: PikvlLWpWAbZZeZ1
    - key: ESIGN-FLAG
      value: sandbox
    - key: accept-language
      value: "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2"

resource:
  secret:
    name: SERVICE-INVOKE-SECRET
    value: PikvlLWpWAbZZeZ1
  matchers:
    # 微信代理wetool api
    - '/v1/wetool/api'
    # 微信代理wetool 上传文件接口
    - '/v1/wetool/upload'
    - '/ws/**'
    - '/v1/agent/digital-bot/**'
    - '/v1/proxy/accounts/account-auth-qrcode-callback'
    - '/v1/proxy/accounts/account-auth-qrcode-remove'
    - '/v1/captcha/**'
    - '/v1/passport/**'
    - '/v1/ad/leads/capture/submit'
    - '/api/v1/proxy/message/send-by-user-input'
    - '/v1/collection/instruction/*'
    - '/v1/collection/task/complete'
    - '/v1/collection/task/raw/report'
    - '/v1/marketing-email/webhook/**'
    - 'archer/background-task/pending'
    - 'archer/background-task/result'
    - 'archer/background-task/in-progress'
    - '/v1/wetool/batch-auth-status'

#env: devgateway
env: qagateway

remote:
  service:
    third-party: 'https://${env}.yaowutech.cn/g-third-party'
    passport: 'https://${env}.yaowutech.cn/h-passport'
    pms: 'https://${env}.yaowutech.cn/h-pms'
    notice: 'https://${env}.yaowutech.cn/g-notice'
    melina: 'https://${env}.yaowutech.cn/h-melina'
    h-riskcontrol: 'https://${env}.yaowutech.cn/h-riskcontrol'
    customer-service: 'https://${env}.yaowutech.cn/h-customer-service'
    hera: 'https://${env}.yaowutech.cn/h-hera'
    h-agent-service: 'https://${env}.yaowutech.cn/h-agent-service'
    llm: 'https://${env}.yaowutech.cn/mstar-llm-rebot'
#    llm: '127.0.0.1:8001'
    settle: 'https://${env}.yaowutech.cn/g-settle'



# redis 自定义配置
redis:
  customize:
    enabled: true
    key-prefix: H-ALPHA
    exclude-keys-pattern:
      - "^G-IAM:AUTHORITY:\\d*$"
      - "^TEST-FLAG$"
      - "^G-IAM:TOKEN-JTI:[\\da-z\\-]*$"
      - "^G-IAM:LOGIN-INFO:[\\d\\D]*$"

common:
  notice:
    appKey: a860a4a1ecb940a68fdb8888fc7e4a9f
    appSecret: 6ad7e36067ea45d49194c99d9bf940f6Ω

# xxl-job配置
xxl:
  job:
    admin:
      ### 调度中心部署根地址：如调度中心集群部署存在多个地址则用逗号分隔。
      addresses: ${XXL_JOB_ADMIN_ADDRESSES:http://xxljob.yaowutech.cn/xxl-job-admin}
    ### 执行器通讯TOKEN
    accessToken: ${XXL_JOB_ACCESS_TOKEN:qoPMhHRTS6Sg7GqD9zIIFsRm6wH7d5zht6V6N383jGms19qTtX}
    executor:
      ### 执行器AppName
      appname: h-alpha
      ### 执行器端口号
      port: 18690
      ### 执行器运行日志文件存储磁盘路径，格式:/data/xxljoblogs/{项目名称}/jobhandler
      logpath: /data/xxljoblogs/h-alpha/jobhandler
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30

#springodc 基本配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: true
  default-flat-param-object: true

#knife4j springdoc ui配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 实体类列表

nacos:
  config:
    username: ${NACOS_USERNAME:h-alpha-test} # 用户名
    password: ${NACOS_PASSWORD:Admin123} # 密码
    server-addr: ${NACOS_SERVER_ADDR:nacos-test.yaowutech.cn} # 配置中心服务地址
    namespace: ${NACOS_NAMESPACE:H_ALPHA} # 命名空间
    group: ${NACOS_GROUP:dev} # 分组
    data-id: h-alpha # 指定配置文件
    type: yaml # 指定配置文件格式
    auto-refresh: true # 开启热更新
    bootstrap:
      enable: false # 开启true后，@NacosValue的自动刷新会失效，应该是bug，目前先关闭


#OSS桶配置信息
aliyun-oss:
  buckets:
    - bucket-name: biz-common-offline
      region-name: cn-shanghai
      secret-id: ${ALIYUN_OSS_BIZ_COMMON_SECRET_ID}
      secret-key: ${ALIYUN_OSS_BIZ_COMMON_SECRET_KEY}
      access-type: PRIVATE
      endpoint: https://oss-biz-offline-static.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/ram-oss-sts
      roleSessionName: ramOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
    - bucket-name: biz-secret-offline
      region-name: cn-shanghai
      secret-id: ${ALIYUN_OSS_BIZ_SECRET_SECRET_ID}
      secret-key: ${ALIYUN_OSS_BIZ_SECRET_SECRET_KEY}
      access-type: SECRET
      endpoint: https://oss-bizs-offline-static.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/ram-oss-sts
      roleSessionName: ramOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
    - bucket-name: biz-public-offline
      region-name: cn-shanghai
      secret-id: LTAI5t6GwWsvkRDY5NRQLgon
      secret-key: ******************************
      access-type: PUBLIC
      endpoint: https://oss-biz-public-offline.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/ram-oss-sts
      roleSessionName: ramOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
  enabled: true

# 测试数据隔离配置
test-flag:
  # 是否开启，true为开启
  enabled: true
  # 模式，include：包含模式，exclude：排除模式
  mode: exclude
  # 过滤模式，none：测试可以看到所有数据，self：测试只能看到自己的数据
  filter-mode: none
  # model为include，包含哪些表
  # model为exclude，排除哪里表
  exclude-tables: flyway_schema_history,shedlock,shedlock_1,shedlock_2,s_tcc_record
  # 测试标识列名
  column: test_flag


# 多租户配置
tenant-line:
  tenant-id-column: merchant_id
  enabled: true
  ignore-tables:

aliyun-call-center:
  secret-id: ${ALIYUN_CALL_CENTER_SECRET_ID}
  secret-key: ${ALIYUN_CALL_CENTER_SECRET_KEY}
  chatbot-instance-id: chatbot-cn-z7Qei7Qu6e
  chatbot-agent-key: 50ce176a0f414ec9989f28c1ba737f6c_p_beebot_public
  voice-navigator-instance-id: 71d2e8f2a7664b43bd475ba84bd50cd0

# 微信消息工具回调地址配置
wetool:
  upload-url: http://127.0.0.1:8016/v1/crm/wetool/upload


# 企微API账号配置
work-qw:
  corpid: ${WORK_QW_CORPID}
  corpsecret: ${WORK_QW_CORPSECRET}

# 科大讯飞的配置
xunfei:
  app-id: d973667d
  secret-key: 7e9093db7ec324cfe61387e59daa9f2a


gmail:
  redirect-url: http://localhost:8016/v1/proxy/accounts/auth-callback
  proxy: local


# whatsapp
whatsapp:
  baseUrl: http://************:80
  # 验证配置
  verification:
    # 是否启用验证功能
    enabled: true
    # 代理账号配置（从Nacos动态获取）
    proxy-accounts:
      # 默认配置，实际使用时从Nacos获取
      default:
        # 代理账号ID
        proxy-id: "default_whatsapp_proxy"
        # 代理手机号
        phone: "+**********"
        # 账号状态：true-在线，false-离线
        online: true
        # 是否禁用：true-禁用，false-启用
        forbidden: false
        # 第三方类型：6-WhatsApp
        third-type: 6


# 短信配置
sms:
  # 短信验证码
  captcha:
    enable: false
    # 过期时间（秒）
    expire:
      seconds: 600
  config:
    #type已经没用了，根据nacos的supplyMerchant来决定
    type: tencent
    app-id: **********
    #腾讯云的keyId
    secret-id: AKIDHPK1gv33sXyCE5nJdljT4TffT8ctviGa
    #腾讯云的secret-key
    secret-key: 80d3EBuQilZ8TbU8XkhRwCE0XoqdDYhc
    sign: 机械星球Mech
    #阿里云的keyId
    aliAccessKeyId: ${SMS_ALI_ACCESS_KEY_ID:LTAI5tCUrKNf4sM7qaDGez1c}
    #阿里云的keySecret
    aliAccessKeySecret: ${SMS_ALI_ACCESS_KEY_SECRET:******************************}
    # 是否为模拟模式
    mock: false

# YCloud配置
ycloud:
  api-key: 54a9c70b7b21c861332d1d30c198bf95
  base-path: https://api.ycloud.com/v2
resend:
  api-key: re_7mDmVT3w_GT8bzw8zA9N4wZK3FtnK15D6

mail:
  # 邮件验证器配置
  verifier:
    api-key: ${MAIL_VERIFIER_API_KEY:C7567E0C9E6E12BA3213A98DA7F47FD8}