spring:
  cloud:
    # k8s
    kubernetes:
      enabled: true # 是否启用k8s集成
      discovery:
        enabled: true # 启用服务发现
        all-namespaces: false # 允许所有命名空间的服务被发现
        include-not-ready-addresses: false # 包括未标记为“就绪”的服务也能被发现
        namespaces:
          - ${POD_NAMESPACE}
  # 数据库
  datasource:
    url: jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/h_alpha?useUnicode=true&characterEncoding=utf-8&serverTimezone=GMT%2B8
    username: ${MYSQL_USER}
    password: ${MYSQL_PASSWORD}

  # redis配置
  data:
    redis:
      database: 3
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      password: ${REDIS_PASSWORD}
      redisson:
        file: classpath:redisson/redisson-prod.yml
  rabbitmq:
    host: ${RABBITMQ_HOST}
    port: ${RABBITMQ_PORT}
    username: ${RABBITMQ_USERNAME}
    password: ${RABBITMQ_PASSWORD}
    virtual-host: ${RABBITMQ_VIRTUAL_HOST}

# security权限配置
security:
  oauth2:
    auth:
      # 登陆密码相关
      password:
        # 密码是否加密
        encrypt-enable: true
        # 密码公钥私钥
        public-key-value:
          ${SECURITY_RSA_PUBLIC_KEY_VALUE}
        private-key-value:
          ${SECURITY_RSA_PRIVATE_KEY_VALUE}
      # jwt公钥私钥
      jwt:
        public-key-value:
          ${SECURITY_RSA_PUBLIC_KEY_VALUE}
        private-key-value:
          ${SECURITY_RSA_PRIVATE_KEY_VALUE}
    resource:
      # 开启资源管理器
      enabled: true
      # 开启方法安全认证
      method:
        enabled: true
      # jwt公钥私钥
      jwt:
        public-key-value:
          ${SECURITY_RSA_PUBLIC_KEY_VALUE}
        private-key-value:
          ${SECURITY_RSA_PRIVATE_KEY_VALUE}

remote:
  service:
    third-party: 'g-third-party'
    passport: 'h-passport'
    pms: 'h-pms'
    notice: 'g-notice'
    melina: 'h-melina'
    llm: 'mstar-llm-rebot'
    h-riskcontrol: 'h-riskcontrol'
    customer-service: 'h-customer-service'
    hera: 'h-hera'
    h-agent-service: 'h-agent-service'
    settle: 'g-settle'

# feigh调用请求头设置
feign-header:
  list:
    - key: SERVICE-INVOKE-SECRET
      value: PikvlLWpWAbZZeZ1
    - key: ESIGN-FLAG
      value: ywtech
    - key: accept-language
      value: "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2"

common:
  notice:
    appKey: d0362590eb1148c4bf86598c91b13151
    appSecret: 83b50e03fef34178bda30991321baad2


# xxl-job配置
xxl:
  job:
    executor:
      ### 执行器AppName
      appname: prod-h-alpha

mybatis:
  sensitive:
    seed: ${MYBATIS_SENSITIVE_SEED:257ffe119b564e073a1653cfc3b8ea04}
    enabled: true

aliyun-oss:
  buckets:
    - bucket-name: biz-common
      region-name: cn-shanghai
      secret-id: ${ALIYUN_OSS_BIZ_COMMON_SECRET_ID}
      secret-key: ${ALIYUN_OSS_BIZ_COMMON_SECRET_KEY}
      access-type: PRIVATE
      endpoint: https://oss-biz-static.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/prod-ram-oss-sts
      roleSessionName: prodRamOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
    - bucket-name: biz-secret
      region-name: cn-shanghai
      secret-id: ${ALIYUN_OSS_BIZ_SECRET_SECRET_ID}
      secret-key: ${ALIYUN_OSS_BIZ_SECRET_SECRET_KEY}
      access-type: SECRET
      endpoint: https://oss-bizs-static.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/prod-ram-oss-sts
      roleSessionName: prodRamOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
    - bucket-name: biz-public-offline
      region-name: cn-shanghai
      secret-id: ${ALIYUN_OSS_BIZ_PUBLIC_ID}
      secret-key: ${ALIYUN_OSS_BIZ_PUBLIC_KEY}
      access-type: PUBLIC
      endpoint: https://oss-biz-public-offline.jxxqtech.com
      roleArn: acs:ram::1155230655182326:role/ram-oss-sts
      roleSessionName: ramOssSts
      sts-endpoint: sts.cn-shanghai.aliyuncs.com
  enabled: true

# 微信消息工具回调地址配置
wetool:
  upload-url: https://gateway.yaowutech.cn/h-alpha/v1/wetool/upload


# whatsapp
whatsapp:
  baseUrl: http://h-alpha-waclient
# resend
resend:
  webhook:
    secret: whsec_e94C4N0QhpBjUpbRdwvVh26MTCYNcXTW
  api-key: re_7mDmVT3w_GT8bzw8zA9N4wZK3FtnK15D6