#spring:
#  cloud:
#    # k8s
#    kubernetes:
#      enabled: false # 是否启用k8s集成


# 配置中心
nacos:
  config:
    username: ${NACOS_USERNAME:h-alpha-test} # 用户名
    password: ${NACOS_PASSWORD:Admin123} # 密码
    server-addr: ${NACOS_SERVER_ADDR:nacos-test.yaowutech.cn} # 配置中心服务地址
    namespace: ${NACOS_NAMESPACE:H_ALPHA} # 命名空间
    group: ${NACOS_GROUP:dev} # 分组
    data-id: h-alpha # 指定配置文件
    type: yaml # 指定配置文件格式
    auto-refresh: true # 开启热更新
    bootstrap:
      enable: false # 开启true后，@NacosValue的自动刷新会失效，应该是bug，目前先关闭