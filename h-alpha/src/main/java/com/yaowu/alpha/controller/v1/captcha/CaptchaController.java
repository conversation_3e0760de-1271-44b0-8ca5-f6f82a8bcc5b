package com.yaowu.alpha.controller.v1.captcha;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.captcha.biz.ICaptchaBizService;
import com.yaowu.alpha.model.dto.passport.EmailCaptchaDTO;
import com.yaowu.alpha.model.dto.passport.SmsCaptchaDTO;
import com.yaowu.alpha.model.vo.passport.CrownCodeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Valid
@RestController
@Api(value = "验证码", tags = "验证码相关接口")
@RequestMapping("/v1/captcha")
@Slf4j
public class CaptchaController {

    @Autowired
    private ICaptchaBizService captchaBizService;

    @ApiOperation(value = "冠号列表")
    @GetMapping("/crown-code-list")
    public BaseResult<List<CrownCodeVO>> crownCodeList() {
        List<CrownCodeVO> list = captchaBizService.crownCodeList();
        return BaseResult.success(list);
    }

    @ApiOperation(value = "发送短信验证码")
    @PostMapping("/send-sms-captcha")
    public BaseResult<Boolean> getSmsCaptcha(@RequestBody @Valid SmsCaptchaDTO dto) {
        Boolean bool = captchaBizService.sendSmsCaptcha(dto);
        return BaseResult.success(bool);
    }

    @ApiOperation(value = "发送邮箱验证码")
    @PostMapping("/send-email-captcha")
    public BaseResult<Boolean> getEmailCaptcha(@RequestBody @Valid EmailCaptchaDTO dto) {
        Boolean bool = captchaBizService.sendEmailCaptcha(dto);
        return BaseResult.success(bool);
    }

}
