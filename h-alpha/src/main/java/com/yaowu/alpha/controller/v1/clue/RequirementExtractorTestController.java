package com.yaowu.alpha.controller.v1.clue;


import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.extractor.AbstractCustomerRequirementExtractorAgent;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyChatMessageService;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;
import io.swagger.annotations.Api;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Valid
@RestController
@Api(value = "需求提取测试", tags = "需求提取测试接口")
@RequestMapping("/v1/requirement/test")
@Slf4j
public class RequirementExtractorTestController {

    @Qualifier("commonRequirementExtractorAgent")
    @Autowired
    private AbstractCustomerRequirementExtractorAgent extractorAgent;

    @Autowired
    private IProxyChatMessageService proxyChatMessageService;

    @Autowired
    private IProxyAccountConfigService proxyAccountConfigService;

    @GetMapping("/extractUserRequirementTest")
    public BaseResult<Boolean> extractUserRequirementTest(Long proxyChatMessageId, Long accountId) {
        ProxyChatMessage proxyChatMessage = proxyChatMessageService.getById(proxyChatMessageId);
        if (proxyChatMessage == null) {
            throw new RuntimeException("消息不存在");
        }
        ProxyAccount proxyAccount = proxyAccountConfigService.getById(accountId);
        extractorAgent.extractUserRequirement(proxyChatMessage, proxyAccount);
        return BaseResult.success(true);
    }
}
