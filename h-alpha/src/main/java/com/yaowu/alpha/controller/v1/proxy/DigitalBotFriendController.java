package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.objectstorage.annotation.FileSignHandle;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.config.nacos.TenantUserPropertyConfig;
import com.yaowu.alpha.domain.common.biz.ITagBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.dto.friend.*;
import com.yaowu.alpha.model.dto.proxy.ChatMessageCursorQueryDTO;
import com.yaowu.alpha.model.dto.proxy.NewMessageCheckDTO;
import com.yaowu.alpha.model.dto.proxy.control.FriendMarkReadRequest;
import com.yaowu.alpha.model.vo.friend.*;
import com.yaowu.alpha.model.vo.proxy.CompanyBackgroundTaskVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2024/12/16 21:02
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "数字员工好友管理", tags = "数字员工好友管理")
@RequestMapping("/v1/agent/digital/friend/")
@Slf4j
public class DigitalBotFriendController {

    private final IProxyAccountFriendBizService friendBizService;
    private final ITagBizService tagBizService;
    private final TenantUserPropertyConfig tenantUserPropertyConfig;

    @PostMapping("/page")
    @Operation(summary = "分页查询好友列表", description = "支持按标签ID列表筛选好友，多个标签取交集")
    public BaseResult<BasePage<FriendPageVO>> pageFriends(@RequestBody @Validated RemoteFriendPageDTO dto) {
        return BaseResult.success(friendBizService.pageFriends(dto));
    }

    @PostMapping("/update-friend-proxy-status")
    @Operation(summary = "更新好友代理状态")
    public BaseResult<Boolean> updateProxyStatus(@RequestBody @Validated FriendProxyStatusUpdateDTO dto) {
        return BaseResult.success(friendBizService.updateFriendStatus(dto));
    }

    @PostMapping("/page-proxy-account-friend-tree")
    @Operation(summary = "获取代理账号及好友列表", description = "获取所有代理账号及其好友层级关系")
    @FileSignHandle
    public BaseResult<BasePage<ProxyAccountFriendPageVO>> pageProxyAccountFriendTree(@RequestBody @Validated PageProxyAccountFriendTreeDTO dto) {
        return BaseResult.success(friendBizService.pageProxyAccountFriendTree(dto));
    }

    @PostMapping("/chat/messages")
    @Operation(summary = "获取聊天消息", description = "分页查询指定代理账号与好友的聊天记录")
    @FileSignHandle
    public BaseResult<BasePage<ChatMessageVO>> getChatMessages(@RequestBody @Validated ChatMessageQueryDTO dto) {
        return BaseResult.success(friendBizService.pageMessages(dto));
    }


    @PostMapping("/chat/session")
    @Operation(summary = "获取会话信息", description = "获取代理账号与好友的会话信息")
    public BaseResult<ChatSessionVO> getChatSession(@RequestBody @Validated QueryChatSessionDTO dto) {
        return BaseResult.success(friendBizService.getChatSession(dto));
    }

    @PostMapping("/detail")
    @Operation(summary = "好友详情信息")
    public BaseResult<FriendDetailVO> detail(@RequestBody @Validated FriendQueryRequestDTO dto) {
        return BaseResult.success(friendBizService.detail(dto));
    }

    @PostMapping("/mark-read")
    @Operation(summary = "标记消息为已读")
    public BaseResult<Boolean> markMessagesAsRead(@RequestBody @Validated FriendMarkReadRequest request) {
        return BaseResult.success(friendBizService.markMessagesAsRead(request));
    }
    
    @PostMapping("/chat/messages/cursor")
    @Operation(summary = "基于游标的聊天消息分页查询", description = "使用游标分页方式查询消息，避免消息断层问题")
    @FileSignHandle
    public BaseResult<ChatMessageCursorPageVO> cursorPageMessages(@RequestBody @Validated ChatMessageCursorQueryDTO dto) {
        return BaseResult.success(friendBizService.cursorPageMessages(dto));
    }

    /**
     * 检查是否有新消息
     */
    @PostMapping("/chat/messages/check-new")
    @Operation(summary = "检查是否有新消息", description = "检查指定消息ID之后是否有新消息")
    public BaseResult<Boolean> checkNewMessages(@RequestBody @Validated NewMessageCheckDTO dto) {
        return BaseResult.success(friendBizService.countNewerMessages(dto));
    }

    @Deprecated
    @PostMapping("/extension/update")
    @Operation(summary = "更新好友扩展信息", description = "维护好友扩展信息")
    public BaseResult<ProxyAccountFriendExtensionVO> updateFriendExtension(@RequestBody @Validated ProxyAccountFriendExtensionUpdateDTO dto) {
        // TODO: 2025-06-06 废弃该方法，后续删除
        return BaseResult.success(null);
    }

    @Deprecated
    @PostMapping("/extension/get")
    @Operation(summary = "获取好友扩展信息", description = "根据好友ID获取扩展信息")
    public BaseResult<ProxyAccountFriendExtensionVO> getFriendExtension(@RequestBody @Validated IdDTO dto) {
        // TODO: 2025-06-06 废弃该方法，后续删除
        return BaseResult.success(null);
    }

    @FileSignHandle
    @PostMapping("/property/get")
    @Operation(summary = "获取好友属性", description = "根据好友ID获取好友属性")
    public BaseResult<ProxyFriendPropertyVO> getProperty(@RequestBody @Validated IdDTO dto) {
        return BaseResult.success(friendBizService.getFriendProperty(dto.getId()));
    }
    
    @PostMapping("/property/update")
    @Operation(summary = "更新好友属性", description = "更新好友属性")
    public BaseResult<Void> updateProperty(@RequestBody @Validated ProxyFriendPropertiesUpdateDTO dto) {
        friendBizService.updateFriendProperty(dto);
        return BaseResult.success();
    }

    @PostMapping("/company/background-check/start")
    @Operation(summary = "发起公司背调", description = "发起公司背调任务，返回任务状态")
    public BaseResult<BackgroundCheckTaskVO> startBackgroundCheck(@RequestBody @Validated CompanyBackgroundCheckDTO dto) {
        return BaseResult.success(friendBizService.startBackgroundCheck(dto));
    }

    @PostMapping("/company/background-check/result")
    @Operation(summary = "查询公司背调结果", description = "根据公司名称和行业查询背调状态和结果")
    public BaseResult<CompanyBackgroundCheckVO> getBackgroundCheckResult(@RequestBody @Validated CompanyBackgroundCheckResultQueryDTO dto) {
        return BaseResult.success(friendBizService.getBackgroundCheckResult(dto.getCompanyName(), dto.getIndustry()));
    }

    @PostMapping("/company/background-check/tasks")
    @Operation(summary = "分页查询公司背调任务", description = "分页查询当前用户创建的背调任务，支持按公司名称、行业、任务状态、来源好友ID筛选")
    public BaseResult<BasePage<CompanyBackgroundTaskVO>> pageBackgroundTasks(@RequestBody @Validated CompanyBackgroundTaskPageDTO dto) {
        return BaseResult.success(friendBizService.pageBackgroundTasks(dto));
    }
}
