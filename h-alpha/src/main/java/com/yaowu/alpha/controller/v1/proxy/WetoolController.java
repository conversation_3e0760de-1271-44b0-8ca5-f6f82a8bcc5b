package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IWeToolFacadeBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.model.dto.proxy.control.CreateDelFriendTaskDTO;
import com.yaowu.alpha.model.dto.proxy.control.GetAddFriendTakDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportTaskResultDTO;
import com.yaowu.alpha.model.dto.proxy.ProxyAccountAuthStatusRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolCommonRequest;
import com.yaowu.alpha.model.vo.proxy.AddFriendTaskVO;
import com.yaowu.alpha.model.vo.proxy.ProxyAccountAuthStatusListVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolCommonResponseData;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolFileUploadResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "微信智能体", tags = "微信账号托管协议接口")
@RequestMapping("/v1/wetool")
@Slf4j
public class WetoolController {

    private final IProxyTaskBizService proxyTaskBizService;

    private final IWeToolFacadeBizService weToolFacadeBizService;

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;


    @PostMapping("/api")
    public WetoolCommonResponseData handleRequest(@RequestBody WetoolCommonRequest requestData) {
        return weToolFacadeBizService.handleRequest(requestData);
    }

    @Operation(summary = "内网文件上传")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public WetoolFileUploadResponseData uploadFile(@ApiParam("文件") @RequestParam(value = "file") MultipartFile file,
                                                   @RequestParam(value = "file_index") String fileIndex) {
        return weToolFacadeBizService.uploadFile(file, fileIndex);
    }

    @Operation(summary = "获取需要添加好友的任务")
    @GetMapping("/get-add-friend-task")
    public BaseResult<List<AddFriendTaskVO>> getAddFriendTask(@SpringQueryMap @Validated GetAddFriendTakDTO dto) {
        return BaseResult.success(proxyTaskBizService.getAddFriendTask(dto.getKeyword()));
    }

    @Operation(summary = "添加删除好友的任务")
    @PostMapping("/task/create/del-friend")
    public BaseResult<Void> createDelFriendTask(@RequestBody @Validated CreateDelFriendTaskDTO dto) {
        proxyTaskBizService.createDelFriendTask(dto);
        return BaseResult.success();
    }

    @Operation(summary = "上报任务执行结果")
    @PostMapping("/report-task-result")
    public BaseResult<Boolean> reportTaskResult(@RequestBody @Validated ReportTaskResultDTO dto) {
        return BaseResult.success(proxyTaskBizService.reportTaskResult(dto));
    }

    @Operation(summary = "批量查询账号授权状态")
    @PostMapping("/batch-auth-status")
    public BaseResult<ProxyAccountAuthStatusListVO> batchAuthStatus(@RequestBody @Validated ProxyAccountAuthStatusRequestDTO requests) {
        return BaseResult.success(proxyAccountConfigBizService.batchCheckAuthByPhoneAndThirdType(requests));
    }
}