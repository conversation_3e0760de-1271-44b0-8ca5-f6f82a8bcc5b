package com.yaowu.alpha.controller.v1.email;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IProxyAccountBizService;
import com.yaowu.alpha.model.dto.email.EmailProxyAccountCreateDTO;
import com.yaowu.alpha.model.dto.email.EmailProxyAccountQueryDTO;
import com.yaowu.alpha.model.dto.email.EmailProxyAccountUpdateDTO;
import com.yaowu.alpha.model.vo.email.EmailProxyAccountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Email代理账号控制器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Tag(name = "Email Proxy Account", description = "Email代理账号管理接口")
@RestController
@RequestMapping("/api/v1/email/proxy-accounts")
@RequiredArgsConstructor
@Slf4j
public class EmailProxyAccountController {

    private final IProxyAccountBizService proxyAccountBizService;

    /**
     * 添加Email代理账号
     *
     * @param dto Email代理账号创建参数
     * @return 创建的账号ID
     */
    @Operation(summary = "添加Email代理账号", 
               description = "创建新的Email代理账号，邮箱配置信息将存储到extend_info字段")
    @PostMapping("/create")
    public BaseResult<Long> createEmailProxyAccount(@RequestBody @Valid EmailProxyAccountCreateDTO dto) {
        Long accountId = proxyAccountBizService.createEmailProxyAccount(dto);
        return BaseResult.success(accountId);
    }

    /**
     * 分页获取Email代理账号列表
     *
     * @param dto 查询参数
     * @return Email代理账号分页列表
     */
    @Operation(summary = "分页获取Email代理账号列表", 
               description = "分页查询Email代理账号列表，支持按租户、邮箱账号、状态等条件筛选")
    @PostMapping("/page")
    public BaseResult<BasePage<EmailProxyAccountVO>> pageEmailProxyAccounts(@RequestBody @Valid EmailProxyAccountQueryDTO dto) {
        BasePage<EmailProxyAccountVO> result = proxyAccountBizService.pageEmailProxyAccounts(dto);
        return BaseResult.success(result);
    }

    /**
     * 更新Email代理账号
     *
     * @param dto Email代理账号更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新Email代理账号", 
               description = "更新Email邮件配置等")
    @PostMapping("/update")
    public BaseResult<Void> updateEmailProxyAccount(@RequestBody @Valid EmailProxyAccountUpdateDTO dto) {
        proxyAccountBizService.updateEmailProxyAccount(dto);
        return BaseResult.success();
    }
}