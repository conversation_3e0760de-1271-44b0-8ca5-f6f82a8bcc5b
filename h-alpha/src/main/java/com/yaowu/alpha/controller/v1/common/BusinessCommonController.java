package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.ILangDictBizService;
import com.yaowu.alpha.model.dto.common.LangDictAddRequest;
import com.yaowu.alpha.model.dto.common.LangDictParentRequest;
import com.yaowu.alpha.model.dto.common.LangDictQueryRequest;
import com.yaowu.alpha.model.vo.common.LangDictVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 语音字段
 *
 * <AUTHOR>
 * @date 2025/4/22-19:16
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "公共业务", tags = "公共业务")
@RequestMapping("/v1/common/")
@Slf4j
public class BusinessCommonController {

    @Autowired
    private ILangDictBizService langDictBizService;

    @Operation(summary = "查询语言字典列表")
    @PostMapping("/lang-dict/query")
    public BaseResult<List<LangDictVO>> langDict(@RequestBody @Validated LangDictQueryRequest dto) {
        return BaseResult.success(langDictBizService.list(dto));
    }

    @Operation(summary = "分页查询语言字典")
    @PostMapping("/lang-dict/page")
    public BaseResult<BasePage<LangDictVO>> page(@RequestBody @Validated LangDictQueryRequest dto) {
        return BaseResult.success(langDictBizService.page(dto));
    }

    @Operation(summary = "新增或者更新一级字典")
    @PostMapping("/lang-dict/add-or-update-parent")
    public BaseResult<Long> addOrUpdate(@RequestBody @Validated LangDictParentRequest request) {
        return BaseResult.success(langDictBizService.addOrUpdateParent(request));
    }

    @Operation(summary = "新增或者更新二级字典")
    @PostMapping("/lang-dict/add-or-update")
    public BaseResult<Long> add(@RequestBody @Validated LangDictAddRequest request) {
        return BaseResult.success(langDictBizService.addOrUpdate(request));
    }

    @Operation(summary = "删除语言字典")
    @GetMapping("/lang-dict/delete")
    public BaseResult<Boolean> delete(@RequestParam("id") Long id) {
        return BaseResult.success(langDictBizService.delete(id));
    }
}
