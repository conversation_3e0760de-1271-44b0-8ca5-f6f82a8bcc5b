package com.yaowu.alpha.controller.v1.passport;

import com.freedom.redis.annotation.DistributedLock;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.passport.biz.ILoginBizService;
import com.yaowu.alpha.model.dto.passport.LoginByEmailDTO;
import com.yaowu.alpha.model.dto.passport.LoginByPwdDTO;
import com.yaowu.alpha.model.dto.passport.LoginBySmsDTO;
import com.yaowu.alpha.model.dto.passport.RefreshTokenDTO;
import com.yaowu.alpha.model.vo.passport.TokenVO;
import com.yaowu.alpha.model.vo.passport.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Valid
@RestController
@Api(value = "登录", tags = "登录相关接口")
@RequestMapping("/v1/passport")
@Slf4j
public class PassportController {

    @Autowired
    private ILoginBizService loginBizService;

    @DistributedLock(key = "#dto.username")
    @ApiOperation(value = "密码登录")
    @PostMapping("/login-by-pwd")
    public BaseResult<TokenVO> loginByPwd(@RequestBody @Valid LoginByPwdDTO dto) {
        TokenVO tokenVO = loginBizService.loginByPwd(dto);
        return BaseResult.success(tokenVO);
    }

    @DistributedLock(key = "#dto.phone")
    @ApiOperation(value = "短信登录")
    @PostMapping("/login-by-sms")
    public BaseResult<TokenVO> loginBySms(@RequestBody @Valid LoginBySmsDTO dto) {
        TokenVO tokenVO = loginBizService.loginBySms(dto);
        return BaseResult.success(tokenVO);
    }

    @DistributedLock(key = "#dto.email")
    @ApiOperation(value = "邮箱登录")
    @PostMapping("/login-by-email")
    public BaseResult<TokenVO> loginByEmail(@RequestBody @Valid LoginByEmailDTO dto) {
        TokenVO tokenVO = loginBizService.loginByEmail(dto);
        return BaseResult.success(tokenVO);
    }

    @ApiOperation(value = "刷新token")
    @PostMapping("/refresh-token")
    public BaseResult<TokenVO> refreshToken(@RequestBody @Valid RefreshTokenDTO dto) {
        TokenVO tokenVO = loginBizService.refreshToken(dto.getRefreshToken());
        return BaseResult.success(tokenVO);
    }

    @ApiOperation(value = "获取用户信息")
    @GetMapping("/get-user-info")
    public BaseResult<UserInfoVO> getUserInfo() {
        UserInfoVO userInfo = loginBizService.getUserInfo();
        return BaseResult.success(userInfo);
    }
}
