package com.yaowu.alpha.controller.v1.payment;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.payment.biz.IPaymentOrderBizService;
import com.yaowu.alpha.model.dto.payment.PaymentOrderCreateRequest;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryDTO;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryRequest;
import com.yaowu.alpha.model.vo.payment.PaymentOrderDetailVO;
import com.yaowu.alpha.model.vo.payment.PaymentOrderVO;
import com.yaowu.alpha.model.vo.payment.PaymentProductResultVO;
import com.yaowu.alpha.model.vo.payment.PaymentResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Locale;


@Valid
@RestController
@Api(value = "支付订单", tags = "支付订单")
@RequestMapping("/v1/payment-order")
@Slf4j
public class PaymentOrderController {

    @Autowired
    private IPaymentOrderBizService paymentOrderBizService;

    @ApiOperation(value = "创建订单并且进行支付")
    @PostMapping("/create-and-pay")
    public BaseResult<PaymentResultVO> createOrderAndPay(@RequestBody @Valid PaymentOrderCreateRequest dto) {
        PaymentResultVO orderAndPay = paymentOrderBizService.createOrderAndPay(dto);
        return BaseResult.success(orderAndPay);
    }

    @ApiOperation(value = "订单分页数据")
    @PostMapping("/page")
    public BaseResult<BasePage<PaymentOrderVO>> paymentOrderPage(@RequestBody PaymentOrderQueryRequest dto, Locale locale) {
        BasePage<PaymentOrderVO> page = paymentOrderBizService.page(dto);
        return BaseResult.success(page);
    }

    @ApiOperation(value = "订单详情")
    @GetMapping("/detail")
    public BaseResult<PaymentOrderDetailVO> paymentOrderDetail(@RequestParam("id") Long id) {
        PaymentOrderDetailVO detail = paymentOrderBizService.detail(id);
        return BaseResult.success(detail);
    }

    @ApiOperation(value = "商品数据")
    @GetMapping("/products")
    public BaseResult<PaymentProductResultVO> products() {
        PaymentProductResultVO res = paymentOrderBizService.products();
        return BaseResult.success(res);
    }
}