package com.yaowu.alpha.controller.v1.email;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.email.biz.IEmailConversationBizService;
import com.yaowu.alpha.domain.email.biz.IEmailSendBizService;
import com.yaowu.alpha.domain.email.biz.IEmailSyncBizService;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.dto.mail.MailConversationQueryDTO;
import com.yaowu.alpha.model.dto.mail.MailDetailQueryDTO;
import com.yaowu.alpha.model.dto.mail.MailMarkReadDTO;
import com.yaowu.alpha.model.dto.mail.MailReplyDTO;
import com.yaowu.alpha.model.vo.email.EmailConversationVO;
import com.yaowu.alpha.model.vo.email.EmailDetailVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 邮件会话控制器
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Tag(name = "Mail", description = "邮件管理接口")
@RestController
@RequestMapping("/mail")
@RequiredArgsConstructor
@Slf4j
public class EmailController {

    private final IEmailConversationBizService emailConversationService;
    private final IEmailSendBizService emailSendBizService;
    private final IEmailSyncBizService emailSyncBizService;

    @Operation(summary = "分页获取邮件会话列表")
    @PostMapping("/conversation/page")
    public BaseResult<BasePage<EmailConversationVO>> pageConversations(@Validated @RequestBody MailConversationQueryDTO queryDTO) {
        BasePage<EmailConversationVO> result = emailConversationService.pageConversations(queryDTO);
        return BaseResult.success(result);
    }

    @Operation(summary = "获取会话邮件列表")
     @PostMapping("/conversation/mails")
     public BaseResult<List<EmailDetailVO>> getConversationMails(@Validated @RequestBody MailDetailQueryDTO queryDTO) {
         List<EmailDetailVO> result = emailConversationService.getConversationMails(queryDTO);
         return BaseResult.success(result);
     }

    @PostMapping("/conversation/mark-read")
    @Operation(summary = "标记消息为已读")
    public BaseResult<Void> markMessagesAsRead(@RequestBody @Validated MailMarkReadDTO dto) {
        emailConversationService.markMessagesAsRead(dto);
        return BaseResult.success();
    }

    @Operation(summary = "回复邮件")
    @PostMapping("/reply")
    public BaseResult<Boolean> replyMail(@Validated @RequestBody MailReplyDTO replyDTO) {
        return BaseResult.success(emailSendBizService.replyMail(replyDTO));
    }

    @Operation(summary = "同步指定账号邮件")
    @PostMapping("/account/sync")
    public BaseResult<Void> syncEmailAccount(@Validated @RequestBody IdDTO idDTO) {
        emailSyncBizService.syncEmailsByAccount(idDTO.getId());
        return BaseResult.success();
    }
}