package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IProxyGreetingBizService;
import com.yaowu.alpha.model.dto.proxy.CreateEmailGreetingTaskDTO;
import com.yaowu.alpha.model.dto.proxy.CreateWhatsAppBusinessTemplateGreetingTaskDTO;
import com.yaowu.alpha.model.dto.proxy.CreateWhatsAppGreetingTaskDTO;
import com.yaowu.alpha.model.dto.proxy.ResetGreetingTaskStatusDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代理打招呼任务控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/20
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/proxy/greeting")
@Tag(name = "代理打招呼任务相关接口")
public class ProxyGreetingController {
    
    private final IProxyGreetingBizService proxyGreetingBizService;
    
    @Operation(summary = "创建WhatsApp打招呼任务")
    @PostMapping("/whatsapp/create")
    public BaseResult<Void> createWhatsAppGreetingTask(@Validated @RequestBody CreateWhatsAppGreetingTaskDTO dto) {
        proxyGreetingBizService.createWhatsAppGreetingTask(dto);
        return BaseResult.success();
    }
    
    @Operation(summary = "创建WhatsApp Business API模板消息打招呼任务")
    @PostMapping("/whatsapp-business/template/create")
    public BaseResult<Void> createWhatsAppBusinessTemplateGreetingTask(@Validated @RequestBody CreateWhatsAppBusinessTemplateGreetingTaskDTO dto) {
        proxyGreetingBizService.createWhatsAppBusinessTemplateGreetingTask(dto);
        return BaseResult.success();
    }

    @Operation(summary = "创建邮箱打招呼任务")
    @PostMapping("/email/create")
    public BaseResult<Void> createEmailGreetingTask(@Validated @RequestBody CreateEmailGreetingTaskDTO dto) {
        proxyGreetingBizService.createEmailGreetingTask(dto);
        return BaseResult.success();
    }
    
    @Operation(summary = "重置打招呼任务状态")
    @PostMapping("/task/reset")
    public BaseResult<Void> resetGreetingTaskStatus(@Validated @RequestBody ResetGreetingTaskStatusDTO dto) {
        proxyGreetingBizService.resetGreetingTaskStatus(dto.getTaskId());
        return BaseResult.success();
    }
}