package com.yaowu.alpha.controller.v1.statistics;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.statistics.biz.IStatisticsCommonBizService;
import com.yaowu.alpha.model.dto.statistics.StatisticsCommonDTO;
import com.yaowu.alpha.model.vo.statistics.StatisticDataGroupVO;
import com.yaowu.alpha.model.vo.statistics.StatisticDataItemVO;
import com.yaowu.alpha.model.vo.statistics.StatisticsDailyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计控制器
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@RestController
@RequestMapping("/v1/statistics")
@RequiredArgsConstructor
@Tag(name = "统计管理", description = "统计相关接口")
public class StatisticsController {

    private final IStatisticsCommonBizService statisticsCommonBizService;

    @PostMapping("/email-total-statistics")
    @Operation(summary = "邮件统计")
    public BaseResult<List<StatisticDataItemVO>> getEmailTotalStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticDataItemVO> list = statisticsCommonBizService.getEmailTotalStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/email-daily-statistics")
    @Operation(summary = "邮件日报表统计")
    public BaseResult<List<StatisticsDailyVO>> getEmailDailyStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticsDailyVO> list = statisticsCommonBizService.getEmailDailyStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/chat-total-statistics")
    @Operation(summary = "对话统计")
    public BaseResult<List<StatisticDataGroupVO>> getChatTotalStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticDataGroupVO> list = statisticsCommonBizService.getChatTotalStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/chat-daily-statistics")
    @Operation(summary = "对话日报表统计")
    public BaseResult<List<StatisticsDailyVO>> getChatDailyStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticsDailyVO> list = statisticsCommonBizService.getChatDailyStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/greeting-total-statistics")
    @Operation(summary = "打招呼任务统计")
    public BaseResult<List<StatisticDataGroupVO>> getGreetingTotalStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticDataGroupVO> list = statisticsCommonBizService.getGreetingTotalStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/greeting-daily-statistics")
    @Operation(summary = "打招呼任务日报表统计")
    public BaseResult<List<StatisticsDailyVO>> getGreetingDailyStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticsDailyVO> list = statisticsCommonBizService.getGreetingDailyStatistics(queryDTO);
        return BaseResult.success(list);
    }

    @PostMapping("/nurture-customer-flow-total-statistics")
    @Operation(summary = "培育客户流程统计")
    public BaseResult<List<StatisticDataGroupVO>> getNurtureCustomerFlowTotalStatistics(@RequestBody StatisticsCommonDTO queryDTO) {
        List<StatisticDataGroupVO> list = statisticsCommonBizService.getNurtureCustomerFlowTotalStatistics(queryDTO);
        return BaseResult.success(list);
    }
}
