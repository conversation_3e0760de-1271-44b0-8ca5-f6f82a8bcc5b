package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IArcherBackgroundTaskBizService;
import com.yaowu.alpha.model.dto.proxy.ArcherTaskResultDTO;
import com.yaowu.alpha.model.dto.proxy.CompanyBackgroundTaskStatusUpdateDTO;
import com.yaowu.alpha.model.vo.proxy.CompanyBackgroundTaskVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/archer/background-task")
@RequiredArgsConstructor
@Tag(name = "ArcherBackgroundTask", description = "Archer背调任务回调接口")
public class ArcherBackgroundTaskController {

    private final IArcherBackgroundTaskBizService archerBackgroundTaskBizService;

    @GetMapping("/pending")
    @Operation(summary = "获取最新的待处理公司背调任务")
    public BaseResult<CompanyBackgroundTaskVO> getPendingTask() {
        return BaseResult.success(archerBackgroundTaskBizService.getPendingTask());
    }

    @PostMapping("/result")
    @Operation(summary = "Archer回调任务执行结果")
    public BaseResult<Void> callbackTaskResult(@RequestBody @Valid ArcherTaskResultDTO dto) {
        archerBackgroundTaskBizService.handleTaskResult(dto);
        return BaseResult.success();
    }


    @PostMapping("/in-progress")
    @Operation(summary = "上报公司背调任务状态为处理中")
    public BaseResult<Boolean> markTaskInProgress(@RequestBody @Valid CompanyBackgroundTaskStatusUpdateDTO dto) {
        boolean result = archerBackgroundTaskBizService.markTaskInProgress(dto);
        return BaseResult.success(result);
    }
}