package com.yaowu.alpha.controller.v1.clue;

import com.freedom.objectstorage.annotation.FileSignHandle;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IClueBizService;
import com.yaowu.alpha.domain.proxy.biz.IUserBusinessAccountBizService;
import com.yaowu.alpha.model.dto.clue.ClueChatRecordsQueryPageDTO;
import com.yaowu.alpha.model.dto.clue.ClueEditDTO;
import com.yaowu.alpha.model.dto.clue.ClueFeedbackDTO;
import com.yaowu.alpha.model.dto.clue.ClueMarkReadDTO;
import com.yaowu.alpha.model.dto.clue.RemoteCluePageDTO;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.vo.clue.ClueDetailVO;
import com.yaowu.alpha.model.vo.clue.CluePageVO;
import com.yaowu.alpha.model.vo.friend.ChatMessageVO;
import com.yaowu.alpha.model.vo.proxy.ClueDecryptionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Valid
@RestController
@Api(value = "线索管理", tags = "线索管理接口")
@RequestMapping("/v1/clue")
@Slf4j
public class ClueController {

    @Autowired
    private IClueBizService clueBizService;

    @Autowired
    private IUserBusinessAccountBizService userBusinessAccountService;

    @ApiOperation(value = "线索分页")
    @PostMapping("/page")
    public BaseResult<BasePage<CluePageVO>> cluePage(@RequestBody RemoteCluePageDTO dto) {
        BasePage<CluePageVO> page = clueBizService.cluePage(dto);
        return BaseResult.success(page);
    }

    @ApiOperation(value = "线索详情")
    @GetMapping("/detail")
    public BaseResult<ClueDetailVO> clueDetail(@RequestParam("id") Long id) {
        ClueDetailVO detail = clueBizService.clueDetail(id);
        return BaseResult.success(detail);
    }

    @ApiOperation(value = "编辑线索")
    @PostMapping("/update")
    public BaseResult<Boolean> clueEdit(@RequestBody @Valid ClueEditDTO dto) {
        Boolean bool = clueBizService.clueEdit(dto);
        return BaseResult.success(bool);
    }

    /**
     * 线索反馈（赞/踩）
     */
    @ApiOperation(value = "线索反馈（赞/踩）")
    @PostMapping("/feedback")
    public BaseResult<Boolean> feedback(@RequestBody @Valid ClueFeedbackDTO dto) {
        Boolean result = clueBizService.feedback(dto);
        return BaseResult.success(result);
    }

    @PostMapping("/decryption")
    @Operation(summary = "线索解密", description = "线索解密")
    public BaseResult<ClueDecryptionVO> requirementDecryption(@RequestBody @Valid IdDTO dto) {
        return BaseResult.success(userBusinessAccountService.requirementDecryption(dto.getId()));
    }

    /**
     * 标记线索为已读
     */
    @ApiOperation(value = "标记线索为已读")
    @PostMapping("/markRead")
    public BaseResult<Boolean> markRead(@RequestBody @Valid ClueMarkReadDTO dto) {
        Boolean result = clueBizService.markRead(dto);
        return BaseResult.success(result);
    }

    /**
     * 查询线索对应的聊天记录
     */
    @ApiOperation(value = "查询线索对应的聊天记录")
    @PostMapping("/chat-records")
    @FileSignHandle
    public BaseResult<BasePage<ChatMessageVO>> pageChatRecords(@RequestBody @Valid ClueChatRecordsQueryPageDTO dto) {
        BasePage<ChatMessageVO> records = clueBizService.pageChatRecords(dto);
        return BaseResult.success(records);
    }

}


