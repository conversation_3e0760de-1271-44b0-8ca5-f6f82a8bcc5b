package com.yaowu.alpha.controller.v1.collection;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionInstructionBizService;
import com.yaowu.alpha.model.bo.collection.DataCollectionInstruction;
import com.yaowu.alpha.model.dto.collection.DataCollectionInstructionReportDTO;
import com.yaowu.alpha.model.dto.collection.PullControlInstructionDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据采集任务指令控制器
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Tag(name = "数据采集任务指令管理", description = "采集任务指令创建、拉取、状态上报等API")
@RestController
@RequestMapping("/v1/collection/instruction")
@RequiredArgsConstructor
@Slf4j
public class DataCollectionInstructionController {
    
    private final IDataCollectionInstructionBizService dataCollectionInstructionBizService;

    /**
     * 拉取待执行的GMap数据采集任务
     * 注意：此接口有分布式锁保护，确保多机部署时不重复执行
     */
    @Operation(summary = "拉取GMap数据采集任务", 
               description = "获取并处理一个待执行的GMap数据采集任务，有分布式锁保护避免重复执行")
    @PostMapping("/pull-task")
    public BaseResult<DataCollectionInstruction> pullOnePendingTask() {
        DataCollectionInstruction instruction = dataCollectionInstructionBizService.pullOnePendingGMapTask();
        return BaseResult.success(instruction);
    }

    /**
     * 拉取待执行的采集任务控制指令
     * 用于获取暂停、恢复、终止等控制指令
     */
    @Operation(summary = "拉取采集任务控制指令", 
               description = "获取并处理一个待执行的采集任务控制指令（暂停、恢复、终止等）")
    @PostMapping("/pull-control-instruction")
    public BaseResult<DataCollectionInstruction> pullPendingControlInstruction(@Validated @RequestBody PullControlInstructionDTO dto) {
        DataCollectionInstruction instruction = dataCollectionInstructionBizService.pullPendingControlInstruction(dto.getCollectionTaskId());
        return BaseResult.success(instruction);
    }

    /**
     * 上报指令执行结果
     * 客户端执行完指令后，通过此接口上报执行结果
     */
    @Operation(summary = "上报指令执行结果", 
               description = "客户端执行完指令后，上报执行结果，系统将更新任务状态并发布事件")
    @PostMapping("/report-result")
    public BaseResult<Void> reportInstructionResult(@Validated @RequestBody DataCollectionInstructionReportDTO reportDTO) {
        dataCollectionInstructionBizService.handleInstructionResult(reportDTO);
        return BaseResult.success();
    }
} 