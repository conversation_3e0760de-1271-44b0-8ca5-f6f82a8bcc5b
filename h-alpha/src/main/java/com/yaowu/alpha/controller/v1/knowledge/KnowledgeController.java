package com.yaowu.alpha.controller.v1.knowledge;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.knowledge.biz.IKnowledgeBizService;
import com.yaowu.alpha.model.dto.knowledge.*;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeDetailVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeFilePageVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgePageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


@Valid
@RestController
@Api(value = "知识库", tags = "知识库接口")
@RequestMapping("/v1/knowledge")
@Slf4j
public class KnowledgeController {

    @Autowired
    private IKnowledgeBizService knowledgeBizService;

    @ApiOperation(value = "创建知识库")
    @PostMapping("/create")
    public BaseResult<Long> createKnowledge(@RequestBody @Valid KnowledgeAddDTO dto) {
        Long id = knowledgeBizService.createKnowledge(dto);
        return BaseResult.success(id);
    }

    @ApiOperation(value = "更新知识库")
    @PostMapping("/update")
    public BaseResult<Boolean> updateKnowledge(@RequestBody @Valid KnowledgeEditDTO dto) {
        Boolean result = knowledgeBizService.updateKnowledge(dto);
        return BaseResult.success(result);
    }

    @ApiOperation(value = "删除知识库")
    @PostMapping("/delete")
    public BaseResult<Boolean> deleteKnowledge(@RequestBody @Valid KnowledgeDelDTO dto) {
        Boolean bool = knowledgeBizService.deleteKnowledge(dto.getId());
        return BaseResult.success(bool);
    }

    @ApiOperation(value = "知识库分页")
    @PostMapping("/page")
    public BaseResult<BasePage<KnowledgePageVO>> knowledgePage(@RequestBody KnowledgePageDTO dto) {
        BasePage<KnowledgePageVO> page = knowledgeBizService.knowledgePage(dto);
        return BaseResult.success(page);
    }

    @ApiOperation(value = "知识库详情")
    @GetMapping("/detail")
    public BaseResult<KnowledgeDetailVO> getKnowledgeDetail(@RequestParam("id") Long id) {
        KnowledgeDetailVO detail = knowledgeBizService.getKnowledgeDetail(id);
        return BaseResult.success(detail);
    }

    @ApiOperation(value = "知识库文件分页")
    @PostMapping("/file/page")
    public BaseResult<BasePage<KnowledgeFilePageVO>> filePage(@RequestBody KnowledgeFilePageDTO dto) {
        BasePage<KnowledgeFilePageVO> page = knowledgeBizService.filePage(dto);
        return BaseResult.success(page);
    }

    @ApiOperation(value = "创建知识库文件")
    @PostMapping("/file/create")
    public BaseResult<List<Long>> createKnowledgeFile(@RequestBody @Valid KnowledgeFileAddDTO dto) throws IOException {
        List<Long> ids = knowledgeBizService.createKnowledgeFile(dto);
        return BaseResult.success(ids);
    }

    @ApiOperation(value = "编辑知识库文件")
    @PostMapping("/file/update")
    public BaseResult<Boolean> updateKnowledgeFile(@RequestBody @Valid KnowledgeFileEditDTO dto) {
        Boolean bool = knowledgeBizService.editKnowledgeFile(dto);
        return BaseResult.success(bool);
    }

    @ApiOperation(value = "删除知识库文件")
    @PostMapping("/file/delete")
    public BaseResult<Boolean> deleteKnowledgeFile(@RequestBody @Valid KnowledgeFileDelDTO dto) {
        Boolean bool = knowledgeBizService.deleteKnowledgeFile(dto.getId());
        return BaseResult.success(bool);
    }
}