package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.ITagBizService;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.dto.common.TagBindingDTO;
import com.yaowu.alpha.model.dto.common.TagCreateDTO;
import com.yaowu.alpha.model.dto.common.TagQueryDTO;
import com.yaowu.alpha.model.vo.common.TagVO;
import com.yaowu.alpha.utils.common.TagBusinessCodeConstant;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签管理相关接口
 * <AUTHOR>
 * @version 1.0
 * @date 2024-05-26
 */
@Slf4j
@RestController
@RequestMapping("/v1/common/tags")
@Tag(name = "标签管理", description = "标签管理相关接口")
public class TagController {

    @Resource
    private ITagBizService tagBizService;

    /**
     * 创建标签
     * @param dto 创建标签请求参数
     * @return 标签ID
     */
    @Operation(summary = "创建标签")
    @PostMapping("/create")
    public BaseResult<Long> createTag(@RequestBody @Validated TagCreateDTO dto) {
        return BaseResult.success(tagBizService.createTag(dto, TagBusinessCodeConstant.FRIEND_TAG_CODE));
    }

    /**
     * 绑定标签到实例
     * @param dto 标签绑定请求参数
     * @return 结果
     */
    @Operation(summary = "绑定标签到实例")
    @PostMapping("/bind")
    public BaseResult<Void> bindTag(@RequestBody @Validated TagBindingDTO dto) {
        tagBizService.bindingTag(dto);
        return BaseResult.success();
    }

    /**
     * 解绑标签与实例的关联
     * @param dto 标签绑定信息
     * @return 结果
     */
    @Operation(summary = "解绑标签与实例")
    @PostMapping("/unbind")
    public BaseResult<Void> unbindTag(@RequestBody @Validated TagBindingDTO dto) {
        tagBizService.unbindTag(dto);
        return BaseResult.success();
    }

    /**
     * 查询标签列表
     * @param dto 查询参数
     * @return 标签列表
     */
    @Operation(summary = "查询标签列表")
    @PostMapping("/list")
    public BaseResult<List<TagVO>> listTags(@RequestBody TagQueryDTO dto) {
        return BaseResult.success(tagBizService.listTags(dto));
    }

    /**
     * 分页查询标签列表
     * @param dto 查询参数
     * @return 分页标签列表
     */
    @Operation(summary = "分页查询标签列表")
    @PostMapping("/page")
    public BaseResult<BasePage<TagVO>> pageTags(@RequestBody TagQueryDTO dto) {
        return BaseResult.success(tagBizService.pageTags(dto));
    }

    /**
     * 查询标签绑定的实例ID列表
     * @param tagId 标签ID
     * @return 实例ID列表
     */
    @Operation(summary = "查询标签绑定的实例ID列表")
    @GetMapping("/instance-ids")
    public BaseResult<List<String>> listTagInstanceIds(
            @Parameter(description = "标签ID") @RequestParam Long tagId) {
        return BaseResult.success(tagBizService.listTagInstanceIds(tagId));
    }
    
    /**
     * 根据实例ID查询绑定的标签列表
     * @param instanceId 实例ID
     * @return 标签列表
     */
    @Operation(summary = "根据实例ID查询绑定的标签列表")
    @GetMapping("/instance-tags")
    public BaseResult<List<TagVO>> listTagsByInstanceId(@Parameter(description = "实例ID") @RequestParam String instanceId) {
        return BaseResult.success(tagBizService.listTagsByInstanceId(instanceId, TagBusinessCodeConstant.FRIEND_TAG_CODE));
    }
    
    /**
     * 删除标签
     * @param dto
     * @return 是否删除成功
     */
    @Operation(summary = "删除标签")
    @PostMapping("/delete")
    public BaseResult<Void> deleteTag(@RequestBody @Validated IdDTO dto) {
        tagBizService.deleteTag(dto.getId());
        return BaseResult.success();
    }
}