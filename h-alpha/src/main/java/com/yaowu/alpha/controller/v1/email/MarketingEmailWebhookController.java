package com.yaowu.alpha.controller.v1.email;

import cn.hutool.core.util.StrUtil;
import com.yaowu.alpha.domain.email.biz.IMarketingEmailEventBizService;
import com.yaowu.alpha.model.dto.email.ResendWebhookEventDTO;
import com.yaowu.alpha.utils.email.resend.ResendWebhookJsonUtil;
import com.yaowu.alpha.utils.email.resend.ResendWebhookVerifier;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Resend Webhook控制器
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Tag(name = "Marketing Email Webhook", description = "营销邮件事件webhook接收接口")
@RestController
@RequestMapping("/v1/marketing-email/webhook")
@RequiredArgsConstructor
@Slf4j
public class MarketingEmailWebhookController {

    @Value("${resend.webhook.secret:}")
    private String webhookSecret;

    private final IMarketingEmailEventBizService marketingEmailEventBizService;

    @Operation(summary = "接收Resend邮件事件", description = "接收Resend发送的邮件事件webhook通知")
    @PostMapping("/resend/hook")
    public ResponseEntity<String> resendHook(@RequestBody String payload, HttpServletRequest request) {
        log.info("收到Resend webhook请求 payload: {}", payload);

        try {
            // 验证webhook配置
            ResponseEntity<String> configValidation = validateWebhookConfig();
            if (configValidation != null) return configValidation;

            // 提取并验证headers
            WebhookHeaders headers = extractWebhookHeaders(request);
            ResponseEntity<String> headerValidation = validateHeaders(headers);
            if (headerValidation != null) return headerValidation;

            // 验证webhook签名
            ResponseEntity<String> signatureValidation = verifyWebhookSignature(headers, payload);
            if (signatureValidation != null) return signatureValidation;

            log.info("Webhook签名验证成功，payload: {}", payload);

            // 处理webhook事件
            processWebhookEvent(payload);

            return new ResponseEntity<>("Webhook received", HttpStatus.OK);

        } catch (Exception e) {
            log.error("处理webhook请求时发生错误", e);
            return new ResponseEntity<>("Internal server error", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 处理webhook事件
     *
     * @param payload 事件payload
     */
    private void processWebhookEvent(String payload) {
        try {
            // 解析JSON为DTO对象
            ResendWebhookEventDTO eventDto = ResendWebhookJsonUtil.parseWebhookEvent(payload);
            
            // 只处理邮件相关事件
            if (!isEmailEvent(eventDto.getType())) {
                log.info("跳过非邮件事件: {}", eventDto.getType());
                return;
            }
            
            log.info("开始处理邮件事件: {}, 邮件ID: {}", 
                    eventDto.getType(), 
                    eventDto.getData() != null ? eventDto.getData().getEmail_id() : "unknown");
            
            // 调用业务服务处理事件
            marketingEmailEventBizService.processResendWebhookEvent(eventDto);
            
            log.info("邮件事件处理完成: {}", eventDto.getType());
            
        } catch (Exception e) {
            log.error("处理webhook事件失败，payload: {}, 错误: {}", payload, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断是否为邮件相关事件
     */
    private boolean isEmailEvent(String eventType) {
        if (StrUtil.isBlank(eventType)) {
            return false;
        }
        
        return eventType.startsWith("email.");
    }
    
    /**
     * 验证webhook配置
     */
    private ResponseEntity<String> validateWebhookConfig() {
        if (StrUtil.isBlank(webhookSecret)) {
            log.error("Webhook secret未配置");
            return new ResponseEntity<>("Webhook not configured", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return null;
    }

    /**
     * 提取webhook headers
     */
    private WebhookHeaders extractWebhookHeaders(HttpServletRequest request) {
        return new WebhookHeaders(
            request.getHeader("svix-id"),
            request.getHeader("svix-timestamp"),
            request.getHeader("svix-signature")
        );
    }

    /**
     * 验证headers完整性
     */
    private ResponseEntity<String> validateHeaders(WebhookHeaders headers) {
        if (StrUtil.hasBlank(headers.svixId, headers.svixTimestamp, headers.svixSignature)) {
            log.warn("缺少必要的webhook headers");
            return new ResponseEntity<>("Missing required headers", HttpStatus.BAD_REQUEST);
        }
        log.debug("Webhook headers - svix-id: {}, svix-timestamp: {}, svix-signature: {}",
                 headers.svixId, headers.svixTimestamp, headers.svixSignature);
        return null;
    }

    /**
     * 验证webhook签名
     */
    private ResponseEntity<String> verifyWebhookSignature(WebhookHeaders headers, String payload) {
        ResendWebhookVerifier verifier = new ResendWebhookVerifier(webhookSecret);
        ResendWebhookVerifier.VerificationResult result = verifier.verifyWithDetails(
            headers.svixId, headers.svixTimestamp, payload, headers.svixSignature);

        if (!result.isValid()) {
            log.warn("Webhook验证失败: {}", result.getMessage());
            return new ResponseEntity<>(result.getMessage(), HttpStatus.UNAUTHORIZED);
        }
        return null;
    }

    /**
     * Webhook Headers封装类
     */
    private static class WebhookHeaders {
        final String svixId;
        final String svixTimestamp;
        final String svixSignature;

        WebhookHeaders(String svixId, String svixTimestamp, String svixSignature) {
            this.svixId = svixId;
            this.svixTimestamp = svixTimestamp;
            this.svixSignature = svixSignature;
        }
    }
}