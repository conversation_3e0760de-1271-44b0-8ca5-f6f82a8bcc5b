package com.yaowu.alpha.controller.v1.customer;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerBizService;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowBizService;
import com.yaowu.alpha.model.dto.customer.FlowTerminateDTO;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerCreateDTO;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerFlowBatchCreateDTO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 培育客户控制器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@RestController
@RequestMapping("/v1/customer/nurture")
@Api(value = "培育客户管理", tags = "培育客户管理接口")
@RequiredArgsConstructor
@Slf4j
@Valid
public class NurtureCustomerController {
    
    private final INurtureCustomerBizService nurtureCustomerBizService;
    private final INurtureCustomerFlowBizService nurtureCustomerFlowBizService;
    
    /**
     * 新增培育客户
     *
     * @param dto 新增培育客户请求参数
     * @return 新增成功的客户ID
     */
    @PostMapping("/create")
    @Operation(summary = "新增培育客户", description = "创建新的培育客户")
    public BaseResult<Long> createNurtureCustomer(@Valid @RequestBody NurtureCustomerCreateDTO dto) {
        Long customerId = nurtureCustomerBizService.createNurtureCustomer(dto);
        return BaseResult.success(customerId);
    }

    /**
     * 批量创建培育客户流程
     *
     * @param dto 批量创建培育流程请求参数
     * @return 批量创建结果
     */
    @PostMapping("/flow/batch-create")
    @Operation(summary = "批量创建培育客户流程", description = "按联系方式类型分组，为客户批量创建培育流程并分配在线代理账号，支持指定流程预计开始执行时间")
    public BaseResult<Void> batchCreateNurtureFlow(@Valid @RequestBody NurtureCustomerFlowBatchCreateDTO dto) {
        nurtureCustomerFlowBizService.batchCreateNurtureFlow(dto);
        return BaseResult.success();
    }

    /**
     * 终止培育流程
     *
     * @param dto 终止流程请求参数
     * @return 终止结果
     */
    @PostMapping("/flow/terminate")
    @Operation(summary = "终止培育流程", description = "根据流程ID终止培育流程，将流程下的活跃节点设置为终止状态")
    public BaseResult<Void> terminateFlow(@Valid @RequestBody FlowTerminateDTO dto) {
        nurtureCustomerFlowBizService.terminateFlow(dto);
        return BaseResult.success();
    }
}
