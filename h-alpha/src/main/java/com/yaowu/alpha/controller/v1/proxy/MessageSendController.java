package com.yaowu.alpha.controller.v1.proxy;

import com.yaowu.alpha.model.dto.proxy.SendMessageDTO;
import com.yaowu.alpha.model.dto.proxy.UserInputMessageDTO;
import com.yaowu.alpha.model.dto.proxy.MessageSendResultVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyMessageSendBizService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息发送控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/proxy/message")
@Tag(name = "消息发送接口", description = "用于发送各类消息")
@RequiredArgsConstructor
@Slf4j
public class MessageSendController {

    /**
     * 消息发送业务服务
     */
    private final IProxyMessageSendBizService proxyMessageSendBizService;
    
    /**
     * 发送消息接口
     * 
     * @param request 消息发送请求对象
     * @return 发送结果
     */
    @PostMapping("/send")
    @Operation(summary = "发送消息", description = "根据消息类型发送不同类型的消息")
    public BaseResult<Void> sendMessage(@Valid @RequestBody SendMessageDTO request) {
        // 调用业务层发送消息
        proxyMessageSendBizService.sendMessage(request);
        
        // 记录日志
        log.info("消息发送成功，accountId={}，receiverId={}，messageType={}", 
                request.getAccountId(), request.getReceiverId(), request.getMessageType());
        
        return BaseResult.success();
    }

    /**
     * 根据用户输入发送消息
     * 
     * @param request 用户输入消息DTO
     * @return 消息发送结果
     */
    @PostMapping("/send-by-user-input")
    @Operation(summary = "根据用户输入发送消息", description = "根据用户输入的内容发送不同类型的消息")
    public BaseResult<MessageSendResultVO> sendMessageByUserInput(@Valid @RequestBody UserInputMessageDTO request) {
        return BaseResult.success(proxyMessageSendBizService.sendMessageByUserInput(request));
    }
} 