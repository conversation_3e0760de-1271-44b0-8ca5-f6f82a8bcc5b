package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyOrgManagementBizService;
import com.yaowu.alpha.model.dto.common.OrgListQueryDTO;
import com.yaowu.alpha.model.vo.common.OrgListVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 15:41
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "组织管理", tags = "组织管理")
@RequestMapping("/v1/org/")
@Slf4j
public class OrgManagementController {

    private final IProxyOrgManagementBizService proxyOrgManagementBizService;

    @PostMapping("/page")
    @Operation(summary = "查询组织列表")
    public BaseResult<List<OrgListVO>> getOrgList(@RequestBody @Valid OrgListQueryDTO dto) {
        return BaseResult.success(proxyOrgManagementBizService.getOrgList(dto));
    }
}
