package com.yaowu.alpha.controller.v1.facebook;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.facebook.biz.IFacebookSearchTaskBizService;
import com.yaowu.alpha.model.dto.facebook.*;
import com.yaowu.alpha.model.vo.facebook.FacebookKeywordTaskGroupPageVO;
import com.yaowu.alpha.model.vo.facebook.FacebookKeywordTaskMemberPageVO;
import com.yaowu.alpha.model.vo.facebook.FacebookKeywordTaskPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Facebook搜索任务控制器
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/v1/facebook/search-task")
@RequiredArgsConstructor
@Tag(name = "Facebook搜索任务", description = "Facebook搜索任务相关接口")
public class FacebookSearchTaskController {

    private final IFacebookSearchTaskBizService facebookSearchTaskBizService;

    @PostMapping("/create")
    @Operation(summary = "创建Facebook搜索任务")
    public BaseResult<Long> createTask(@Valid @RequestBody FacebookSearchTaskCreateDTO createDTO) {
        Long taskId = facebookSearchTaskBizService.createTask(createDTO);
        return BaseResult.success(taskId);
    }

    @PostMapping("/del")
    @Operation(summary = "删除Facebook搜索任务")
    public BaseResult<Boolean> delTask(@Valid @RequestBody FacebookSearchTaskDelDTO dto) {
        Boolean bool = facebookSearchTaskBizService.delTask(dto);
        return BaseResult.success(bool);
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询Facebook搜索任务")
    public BaseResult<BasePage<FacebookKeywordTaskPageVO>> taskPage(@RequestBody FacebookSearchTaskPageQueryDTO queryDTO) {
        return BaseResult.success(facebookSearchTaskBizService.taskPage(queryDTO));
    }

    @PostMapping("/group/page")
    @Operation(summary = "分页查询Facebook搜索任务群组数据")
    public BaseResult<BasePage<FacebookKeywordTaskGroupPageVO>> groupPage(@RequestBody FacebookSearchTaskGroupPageQueryDTO queryDTO) {
        return BaseResult.success(facebookSearchTaskBizService.groupPage(queryDTO));
    }

    @PostMapping("/member/page")
    @Operation(summary = "分页查询Facebook搜索任务群组成员数据")
    public BaseResult<BasePage<FacebookKeywordTaskMemberPageVO>> memberPage(@RequestBody FacebookSearchTaskMemberPageQueryDTO queryDTO) {
        return BaseResult.success(facebookSearchTaskBizService.memberPage(queryDTO));
    }
} 