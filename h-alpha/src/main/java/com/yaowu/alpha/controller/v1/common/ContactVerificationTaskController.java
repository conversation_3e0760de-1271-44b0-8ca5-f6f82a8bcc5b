package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.IContactVerificationTaskBizService;
import com.yaowu.alpha.model.dto.common.ContactVerificationCreateDTO;
import com.yaowu.alpha.model.dto.common.ContactVerificationQueryDTO;
import com.yaowu.alpha.model.vo.common.ContactVerificationCreateResultVO;
import com.yaowu.alpha.model.vo.common.ContactVerificationStatusVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 联系方式验证任务控制器
 * 
 * <AUTHOR>
 * @since 2025-01-24 20:30:00
 */
@RestController
@RequestMapping("/v1/common/contact-verification")
@RequiredArgsConstructor
@Tag(name = "联系方式验证任务", description = "联系方式验证任务相关接口")
public class ContactVerificationTaskController {

    private final IContactVerificationTaskBizService contactVerificationTaskBizService;

    /**
     * 创建验证任务
     * 批量创建联系方式验证任务，支持邮件和WhatsApp类型
     * 
     * @param dto 创建请求参数
     * @return 创建结果统计
     */
    @PostMapping("/create")
    @Operation(summary = "创建验证任务", description = "批量创建联系方式验证任务")
    public BaseResult<ContactVerificationCreateResultVO> createVerificationTasks(@Valid @RequestBody ContactVerificationCreateDTO dto) {
        ContactVerificationCreateResultVO result = contactVerificationTaskBizService.createVerificationTasks(dto);
        return BaseResult.success(result);
    }

    /**
     * 查询验证状态
     * 根据联系方式类型和联系方式列表查询验证状态
     * 
     * @param dto 查询请求参数
     * @return 验证任务列表
     */
    @PostMapping("/query-status")
    @Operation(summary = "查询验证状态", description = "根据联系方式类型和联系方式列表查询验证状态")
    public BaseResult<List<ContactVerificationStatusVO>> queryVerificationStatus(@Valid @RequestBody ContactVerificationQueryDTO dto) {
        List<ContactVerificationStatusVO> result = contactVerificationTaskBizService.queryVerificationStatus(dto);
        return BaseResult.success(result);
    }
}