package com.yaowu.alpha.controller.v1.ycloud;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.ycloud.hook.AbstractYCloudWeHookEventHandler;
import com.yaowu.alpha.utils.common.StreamUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * YCloud Webhook控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/15
 */
@RestController
@RequestMapping("/api/v1/ycloud/webhook")
@Tag(name = "YCloud Webhook", description = "YCloud Webhook接口")
@RequiredArgsConstructor
@Slf4j
public class YCloudWebHookController {

    private final List<AbstractYCloudWeHookEventHandler<?>> eventHandlers;

    /**
     * 处理YCloud Webhook事件
     * 
     * @param payload 事件载荷
     * @param signature YCloud签名头
     * @param request HTTP请求
     * @return 响应实体
     */
    @PostMapping("/hook")
    @Operation(summary = "处理YCloud Webhook事件")
    public ResponseEntity<String> hook(
            @RequestBody String payload,
            @RequestHeader(value = "YCloud-Signature", required = false) String signature,
            HttpServletRequest request) {
        
        log.info("Received YCloud webhook event from IP: {}", request.getRemoteAddr());
        try {
            // 验证请求参数
            ResponseEntity<String> validationResponse = validateRequest(payload);
            if (validationResponse != null) {
                return validationResponse;
            }
            // 提取事件类型
            Optional<String> eventTypeOpt = extractEventType(payload);
            if (eventTypeOpt.isEmpty()) {
                return new ResponseEntity<>("Invalid event format", HttpStatus.BAD_REQUEST);
            }
            String eventType = eventTypeOpt.get();
            log.info("Processing event type: {}", eventType);
            // 处理事件
            processEvent(payload, eventType);
            // 返回200 OK表示已接收
            return new ResponseEntity<>("Webhook received", HttpStatus.OK);
        } catch (Exception e) {
            log.error("Unexpected error processing webhook: {}", e.getMessage(), e);
            return new ResponseEntity<>("Internal server error", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 验证请求参数
     *
     * @param payload 请求载荷
     * @return 如果验证失败返回错误响应，否则返回null
     */
    private ResponseEntity<String> validateRequest(String payload) {
        if (!StringUtils.hasText(payload)) {
            log.warn("Empty payload received");
            return new ResponseEntity<>("Empty payload", HttpStatus.BAD_REQUEST);
        }
        return null;
    }
    
    /**
     * 处理事件
     *
     * @param payload 请求载荷
     * @param eventType 事件类型
     * @return 是否成功处理
     */
    private void processEvent(String payload, String eventType) {
        StreamUtil.of(eventHandlers)
                .filter(handler -> handler.support(eventType))
                .findAny()
                .ifPresent(handler -> handler.process(payload));

    }
    
    /**
     * 从载荷中提取事件类型
     *
     * @param payload 事件载荷
     * @return 事件类型
     */
    private Optional<String> extractEventType(String payload) {
        try {
            JSONObject jsonObj = JSONUtil.parseObj(payload);
            String eventType = jsonObj.getStr("type");
            
            if (!StringUtils.hasText(eventType)) {
                log.warn("Event type is empty in payload");
                return Optional.empty();
            }
            
            return Optional.of(eventType);
        } catch (JSONException e) {
            log.error("Failed to parse payload as JSON: {}", e.getMessage(), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to extract event type: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }
}
