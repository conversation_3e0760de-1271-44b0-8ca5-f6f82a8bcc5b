package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.IAreaBizService;
import com.yaowu.alpha.model.dto.common.CityQueryDTO;
import com.yaowu.alpha.model.dto.common.ParentAreaQueryDTO;
import com.yaowu.alpha.model.vo.common.AreaVO;
import com.yaowu.alpha.model.vo.common.CityVO;
import com.yaowu.alpha.model.vo.common.CountryVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地区信息Controller
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@RestController
@RequiredArgsConstructor
@Api(value = "地区信息", tags = "地区信息")
@RequestMapping("/v1/common/area")
@Slf4j
public class AreaController {

    private final IAreaBizService areaBizService;

    /**
     * 查询国家列表
     *
     * @return 国家信息列表
     */
    @Operation(summary = "查询国家列表")
    @GetMapping("/countries")
    public BaseResult<List<CountryVO>> listCountries() {
        List<CountryVO> countries = areaBizService.listCountries();
        return BaseResult.success(countries);
    }

    /**
     * 根据父区域查询子区域列表（推荐使用的统一接口）
     *
     * @param dto 父区域查询参数
     * @return 子区域信息列表
     */
    @Operation(summary = "根据父区域查询子区域列表")
    @PostMapping("/children")
    public BaseResult<List<AreaVO>> listChildrenByParent(@RequestBody @Valid ParentAreaQueryDTO dto) {
        List<AreaVO> children = areaBizService.listChildrenByParent(dto);
        return BaseResult.success(children);
    }

    /**
     * 根据国家查询城市列表
     *
     * @param dto 城市查询参数
     * @return 城市信息列表
     */
    @Operation(summary = "根据国家查询城市列表")
    @PostMapping("/cities")
    public BaseResult<List<CityVO>> listCitiesByCountry(@RequestBody @Valid CityQueryDTO dto) {
        List<CityVO> cities = areaBizService.listCitiesByCountry(dto);
        return BaseResult.success(cities);
    }
}
