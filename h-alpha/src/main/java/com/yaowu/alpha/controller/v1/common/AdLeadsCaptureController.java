package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.IAdLeadsCaptureBizService;
import com.yaowu.alpha.model.dto.common.SubmitAdLeadsCaptureDTO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "留资", tags = "留资")
@RequestMapping("/v1/ad/leads/capture/")
@Slf4j
public class AdLeadsCaptureController {

    private final IAdLeadsCaptureBizService adLeadsCaptureBizService;


    @PostMapping("/submit")
    @Operation(summary = "提交留资信息")
    public BaseResult<Void> submit(@RequestBody @Valid SubmitAdLeadsCaptureDTO dto) {
        adLeadsCaptureBizService.submit(dto);
        return BaseResult.success();
    }

}
