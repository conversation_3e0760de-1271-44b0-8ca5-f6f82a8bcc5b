package com.yaowu.alpha.controller.v1.user;

import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.config.exception.AlphaException;
import com.yaowu.alpha.domain.proxy.biz.IUserBusinessAccountBizService;
import com.yaowu.alpha.model.dto.proxy.OrderPurchasedBenefitsDTO;
import com.yaowu.alpha.model.dto.proxy.request.AccountFlowQueryRequest;
import com.yaowu.alpha.model.vo.proxy.UserBusinessAccountFlowVO;
import com.yaowu.alpha.model.vo.proxy.UserBusinessAccountVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/4/15-19:29
 */
@RestController
@RequestMapping("/api/v1/user/account")
@Tag(name = "用户业务账号", description = "用户业务账号")
@RequiredArgsConstructor
@Slf4j
public class UserBusinessAccountController {

    @Autowired
    private IUserBusinessAccountBizService userBusinessAccountService;

    @RequestMapping("/get-account")
    @Operation(summary = "获取业务账号", description = "获取业务账号")
    public BaseResult<UserBusinessAccountVO> getAccount() {
        return BaseResult.success(userBusinessAccountService.getUserAccountVO());
    }

    @PostMapping("/get-account-flow")
    @Operation(summary = "获取账号流水", description = "获取账号流水")
    public BaseResult<BasePage<UserBusinessAccountFlowVO>> pageAccountFlow(@RequestBody @Validated AccountFlowQueryRequest request) {
        return BaseResult.success(userBusinessAccountService.pageAccountFlow(request));
    }

    @GetMapping("/flow-detail")
    @Operation(summary = "流水详情", description = "流水详情")
    public BaseResult<UserBusinessAccountFlowVO> flowDetail(@RequestParam("id") Long id) {
        return BaseResult.success(userBusinessAccountService.flowDetail(id));
    }

    @PostMapping("/backdoor")
    @Operation(summary = "后门接口", description = "后门接口")
    public BaseResult<Boolean> backdoor(@RequestBody MockRequest request) {
        if (!"xx".equals(request.getCallToken())) {
            throw new AlphaException("非法调用");
        }
        if (request.getOrderId() != null) {
            OrderPurchasedBenefitsDTO dto = new OrderPurchasedBenefitsDTO();
            dto.setOrderId(request.getOrderId());
            // 订单权益数据写入
            boolean b = userBusinessAccountService.writeBenifitsByOrder(dto);
            return BaseResult.success(b);
        }
        if (request.getRequirementId() != null) {
            // 线索扣减
            boolean b = userBusinessAccountService.requirementTokenDeductions(request.getRequirementId());
            return BaseResult.success(b);
        }
        throw new AlphaException("参数为空");
    }

    @Data
    public static class MockRequest {
        private Long orderId;

        private Long requirementId;

        @NotNull(message = "调用token不能为空")
        private String callToken;
    }

}
