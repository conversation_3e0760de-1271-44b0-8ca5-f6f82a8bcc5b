package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.objectstorage.annotation.FileSignHandle;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyChatMessageBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.model.dto.group.GroupMessagePageDTO;
import com.yaowu.alpha.model.dto.group.GroupStatusUpdateDTO;
import com.yaowu.alpha.model.dto.group.RemoteGroupPageDTO;
import com.yaowu.alpha.model.vo.proxy.group.GroupDetailVO;
import com.yaowu.alpha.model.vo.proxy.group.GroupMessageVO;
import com.yaowu.alpha.model.vo.proxy.group.GroupPageVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2024/12/16 21:02
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "数字员工群聊管理", tags = "数字员工群聊管理")
@RequestMapping("/v1/agent/digital/group/")
@Slf4j
public class DigitalBotGroupController {

    private final IProxyGroupBizService proxyGroupBizService;

    private final IProxyChatMessageBizService proxyChatMessageBizService;

    @PostMapping("/page")
    @Operation(summary = "分页查询群列表", description = "根据条件分页查询群列表信息")
    public BaseResult<BasePage<GroupPageVO>> pageGroups(@RequestBody @Validated RemoteGroupPageDTO pageDTO) {
        return BaseResult.success(proxyGroupBizService.pageGroups(pageDTO));
    }
    @PostMapping("/update-status")
    @Operation(summary = "更新群状态", description = "切换群的状态（禁用/代理/人工）")
    public BaseResult<Boolean> updateGroupStatus(@RequestBody @Validated GroupStatusUpdateDTO dto) {
        return BaseResult.success(proxyGroupBizService.updateGroupStatus(dto));
    }

    @GetMapping("/detail/{groupId}")
    @Operation(summary = "获取群聊详情", description = "获取群聊基本信息和成员列表")
    @FileSignHandle
    public BaseResult<GroupDetailVO> getGroupDetail(@PathVariable("groupId") Long groupId) {
        return BaseResult.success(proxyGroupBizService.getGroupDetail(groupId));
    }

    @PostMapping("/messages/page")
    @Operation(summary = "分页查询群聊消息", description = "支持多条件筛选群聊消息记录")
    @FileSignHandle
    public BaseResult<BasePage<GroupMessageVO>> pageGroupMessages(@RequestBody @Validated GroupMessagePageDTO dto) {
        return BaseResult.success(proxyGroupBizService.pageGroupMessages(dto));
    }
}
