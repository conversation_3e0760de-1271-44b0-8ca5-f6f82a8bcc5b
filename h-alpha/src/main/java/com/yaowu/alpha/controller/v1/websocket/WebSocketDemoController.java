package com.yaowu.alpha.controller.v1.websocket;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Controller;

/**
 * WebSocket-测试接口
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Controller
@RequiredArgsConstructor
@Tag(name = "WebSocket-相关协议")
public class WebSocketDemoController {
//
//    private final SimpMessagingTemplate messagingTemplate;
//
//
//    /**
//     * 广播方法
//     */
//
//    @MessageMapping("/message")
//    @SendTo("/topic/messages")
//    public String handleMessage(String message) {
//        return "Received: " + message;
//    }
//
//    /**
//     * 定向推送方法
//     */
//    @MessageMapping("/private-message")
//    public void sendToUser(@Payload String message,
//                           @Header("simpSessionId") String sessionId,
//                           @Headers Map<String, Object> headers,
//                           Principal principal) {
//        // 1. 获取 nativeHeaders
//        Map<String, List<String>> nativeHeaders = (Map<String, List<String>>) headers.get("nativeHeaders");
//
//        // 2. 安全获取 username
//        if (nativeHeaders != null && nativeHeaders.containsKey("username")) {
//            List<String> usernameList = nativeHeaders.get("username");
//            if (!usernameList.isEmpty()) {
//                String username = usernameList.get(0); // 取第一个值
//                messagingTemplate.convertAndSendToUser(username, "/queue/messages", "Private: " + message);
//            }
//        }
//    }

}
