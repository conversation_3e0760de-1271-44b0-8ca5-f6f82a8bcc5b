package com.yaowu.alpha.controller.v1.user;

import com.freedom.objectstorage.annotation.FileSignHandle;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.user.biz.IUserShortCodeBizService;
import com.yaowu.alpha.enums.user.ChannelTypeEnum;
import com.yaowu.alpha.model.dto.proxy.GenerateFriendCodeDTO;
import com.yaowu.alpha.model.vo.user.UserShortCodeVO;
import com.yaowu.alpha.utils.CurrentUserUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yaowu.alpha.enums.user.ChannelTypeEnum.WECHAT;

/**
 * 用户短码控制器
 * 提供用户短码相关的接口服务，包括生成好友二维码和验证短码等功能
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "用户短码相关接口")
@RequestMapping("/v1/user/short-code")
public class UserShortCodeController {

    private final IUserShortCodeBizService userShortCodeBizService;

    /**
     * 获取好友二维码及验证短码
     * 生成用于添加好友的二维码和验证短码，用于好友验证
     *
     * @return 短码信息，包含二维码URL和验证短码
     */
    @PostMapping("/generate-friend-code")
    @Operation(summary = "获取好友二维码及验证短码", description = "生成用于添加好友的二维码和验证短码，用于好友验证")
    @FileSignHandle
    public BaseResult<UserShortCodeVO> generateFriendCode(@RequestBody @Valid GenerateFriendCodeDTO dto) {
        Long currentUserId = CurrentUserUtil.currentUserId();
        UserShortCodeVO shortCodeVO = userShortCodeBizService.generateAndBindShortCode(currentUserId, dto.getChannelType());
        return BaseResult.success(shortCodeVO);
    }

    /**
     * 查询当前用户短码绑定状态
     * 通过多次重试的方式查询用户短码是否已被绑定
     *
     * @return 短码绑定状态，true-已绑定，false-未绑定
     */
    @PostMapping("/check-bind-status")
    @Operation(summary = "查询短码绑定状态", description = "查询当前用户短码是否已被绑定，支持重试查询")
    public BaseResult<Boolean> checkBindStatus(@RequestBody @Valid GenerateFriendCodeDTO dto) {
        Long currentUserId = CurrentUserUtil.currentUserId();
        return BaseResult.success(userShortCodeBizService.checkShortCodeBindStatus(currentUserId,dto.getChannelType()));
    }

    /**
     * 获取当前账号绑定的体验账户渠道
     * 返回当前登录用户绑定的所有体验账号的渠道类型
     *
     * @return 渠道类型列表
     */
    @PostMapping("/get-bound-channels")
    @Operation(summary = "获取绑定的渠道类型", description = "获取当前账号绑定的所有体验账户渠道类型")
    public BaseResult<List<ChannelTypeEnum>> getBoundChannels() {
        Long currentUserId = CurrentUserUtil.currentUserId();
        List<ChannelTypeEnum> channelTypeEnums = userShortCodeBizService.listBoundChannelsByUserId(currentUserId);
        return BaseResult.success(channelTypeEnums);
    }
}