package com.yaowu.alpha.controller.v1.proxy;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IProxyCommonFacadeBizService;
import com.yaowu.alpha.domain.proxy.biz.IProxyInteractBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyBatchTaskBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTagBizService;
import com.yaowu.alpha.model.dto.proxy.DigitalBotCustomerRequirementExtractDTO;
import com.yaowu.alpha.model.dto.proxy.DigitalBotNewMsgRequestDTO;
import com.yaowu.alpha.model.dto.proxy.DigitalBotProxyIdGenerateDTO;
import com.yaowu.alpha.model.dto.proxy.DigitalBotPullTaskDTO;
import com.yaowu.alpha.model.dto.proxy.control.ProxyBatchMessageTaskCreateByTagsDTO;
import com.yaowu.alpha.model.dto.proxy.control.ProxyFriendMarkTagDTO;
import com.yaowu.alpha.model.vo.proxy.DigitalBotNewMsgReplyVO;
import com.yaowu.alpha.model.vo.proxy.DigitalBotProxyIdGenerateVO;
import com.yaowu.alpha.model.vo.proxy.DigitalBotSubmitNewMessageVO;
import com.yaowu.alpha.model.vo.proxy.common.CommonProxyPullTaskVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/16 21:02
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "数字员工智能体", tags = "数字员工智能体")
@RequestMapping("/v1/agent/digital-bot/")
@Slf4j
public class DigitalBotAgentController {

    private final IProxyCommonFacadeBizService proxyCommonFacadeBizService;

    private final IProxyInteractBizService proxyInteractBizService;

    private final IProxyBatchTaskBizService proxyBatchTaskBizService;

    private final IProxyTagBizService proxyTagBizService;


    @PostMapping("/message/submit-reply")
    public BaseResult<DigitalBotNewMsgReplyVO> submitNewMessageAndReply(@RequestBody @Valid DigitalBotNewMsgRequestDTO commonReportNewMsgRequestDTO) {
        return BaseResult.success(proxyCommonFacadeBizService.submitNewMessageAndReply(commonReportNewMsgRequestDTO));
    }

    @PostMapping("/message/submit")
    public BaseResult<DigitalBotSubmitNewMessageVO> submitNewMessage(@RequestBody @Valid DigitalBotNewMsgRequestDTO commonReportNewMsgRequestDTO) {
        return BaseResult.success(proxyCommonFacadeBizService.submitNewMessage(commonReportNewMsgRequestDTO));
    }

    @PostMapping("/tasks/pull")
    public BaseResult<List<CommonProxyPullTaskVO>> pullTask(@RequestBody @Valid DigitalBotPullTaskDTO digitalBotPullTaskDTO) {
        return BaseResult.success(proxyCommonFacadeBizService.pullTasks(digitalBotPullTaskDTO));
    }

    @Operation(summary = "好友打标")
    @PostMapping("/friend/tag/mark")
    public BaseResult<Void> markFriendTag(@RequestBody @Validated ProxyFriendMarkTagDTO dto) {
        proxyTagBizService.markFriendTag(dto.getProxyAccountId(), dto.getFriendProxyId(), dto.getTagName());
        return BaseResult.success();
    }

    @Operation(summary = "创建批量消息发送任务")
    @PostMapping("/batch-task/message/create")
    public BaseResult<Void> createBatchMessageTask(@RequestBody @Valid ProxyBatchMessageTaskCreateByTagsDTO dto) {
        proxyBatchTaskBizService.createBatchMessageTaskByTags(dto);
        return BaseResult.success();
    }

    @Operation(summary = "生成代理id")
    @PostMapping("/proxy-id/generate")
    public BaseResult<DigitalBotProxyIdGenerateVO> generateProxyId(@RequestBody @Valid DigitalBotProxyIdGenerateDTO dto) {
        return BaseResult.success(proxyCommonFacadeBizService.generateProxyId(dto));
    }

    @Operation(summary = "生成互动样本数据集")
    @PostMapping("/interaction/sample/generate")
    public BaseResult<Void> generateInteractionSample() {
        proxyInteractBizService.generateSampleDataSet();
        return BaseResult.success();
    }

    @Operation(summary = "客户需求提取")
    @PostMapping("/customer-requirement/extractor")
    public BaseResult<Void> extractCustomerRequirement(@RequestBody @Valid DigitalBotCustomerRequirementExtractDTO dto) {
        proxyCommonFacadeBizService.extractCustomerRequirement(dto);
        return BaseResult.success();
    }
}
