package com.yaowu.alpha.controller.v1.proxy;


import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.email.biz.IEmailBizService;
import com.yaowu.alpha.domain.proxy.biz.IProxyAccountAuthBizService;
import com.yaowu.alpha.domain.proxy.biz.IProxyAccountBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.enums.email.EmailTypeEnum;
import com.yaowu.alpha.model.dto.email.EmailAuthCallbackDTO;
import com.yaowu.alpha.model.dto.proxy.*;
import com.yaowu.alpha.model.vo.proxy.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/v1/proxy/accounts")
@Tag(name = "三方代理账户管理", description = "三方代理账户管理")
@Slf4j
public class ProxyAccountController {

    // todo  绑定spu账号要有一个绑定的过程
    @Autowired
    private IProxyAccountBizService proxyAccountBizService;

    @Autowired
    private IProxyAccountFriendBizService proxyAccountFriendBizService;

    @Autowired
    private IEmailBizService emailBizService;

    @Autowired
    private IProxyAccountAuthBizService authBizService;

    @Operation(summary = "创建三方代理账户")
    @PostMapping("create")
    public BaseResult<Long> createProxyAccount(@RequestBody @Validated ProxyAccountCreateDTO dto) {
        return BaseResult.success(proxyAccountBizService.createProxyAccount(dto));
    }

    @Operation(summary = "修改三方代理账户")
    @PostMapping("/update")
    public BaseResult<Boolean> updateProxyAccount(@RequestBody @Validated ProxyAccountUpdateDTO dto) {
        return BaseResult.success(proxyAccountBizService.updateProxyAccount(dto));
    }

    @Operation(summary = "三方代理账户操作")
    @PostMapping("/operate")
    public BaseResult<Boolean> operateProxyAccount(@RequestBody @Validated ProxyAccountOperationDTO dto) {
        return BaseResult.success(proxyAccountBizService.operateProxyAccount(dto));
    }

    @Operation(summary = "分页查询三方代理账户")
    @PostMapping("/page")
    public BaseResult<BasePage<ProxyAccountVO>> page(@RequestBody @Validated ProxyAccountRequestQueryDTO dto) {
        return BaseResult.success(proxyAccountBizService.page(dto));
    }

    @Operation(summary = "详情查询三方代理账户")
    @GetMapping("/detail")
    public BaseResult<ProxyAccountVO> detail(@Parameter(description = "账户ID", name = "id") Long id) {
        return BaseResult.success(proxyAccountBizService.detail(id));
    }

    @Operation(summary = "三方代理账户授权")
    @PostMapping("/auth")
    public BaseResult<ProxyAccountAuthVO> accountAuth(@RequestBody @Validated ProxyAccountAuthDTO dto,
                                                      HttpServletRequest request, HttpServletResponse response) throws IOException {
        ProxyAccountAuthVO auth = proxyAccountBizService.auth(dto);
        // response.sendRedirect(auth.getAuthUrl());
        return BaseResult.success(auth);
    }

    @Operation(summary = "三方代理账户授权回调")
    @GetMapping("/auth-callback")
    public BaseResult<Boolean> accountAuthCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Map<String, Object> param = new HashMap<>();
        // 打印request 里面所有得请求参数
        request.getParameterMap().forEach((k, v) -> {
            param.put(k, v.length == 1 ? v[0] : v);
            log.info("key:{},value:{}", k, v);
        });
        emailBizService.emailAuthCallback(EmailAuthCallbackDTO.builder()
                .emailType(EmailTypeEnum.GMAIL)
                .callbackParams(param)
                .build());
        return BaseResult.success(true);
    }

    @Operation(summary = "获取whatsApp账号授权二维码")
    @PostMapping("/get-account-auth-qrcode")
    public BaseResult<ProxyAccountAuthQrcodeVO> getAccountAuthQrcode(@RequestBody @Validated ProxyAccountAuthQrcodeDTO dto) {
        return BaseResult.success(proxyAccountBizService.getAccountAuthQrcode(dto));
    }

    @Operation(summary = "whatsApp账号授权二维码回调")
    @PostMapping("/account-auth-qrcode-callback")
    public BaseResult<Void> accountAuthQrcodeCallback(@RequestBody @Validated ProxyAccountAuthQrcodeCallbackDTO dto) {
        proxyAccountBizService.accountAuthQrcodeCallback(dto);
        return BaseResult.success();
    }

    @Operation(summary = "whatsApp账户授权")
    @PostMapping("/whatsapp-auth")
    public BaseResult<Boolean> whatsappAccountAuth(@RequestBody @Validated ProxyAccountWhatsAppAuthDTO dto){
        return BaseResult.success(authBizService.whatsAppAuth(dto));
    }

    @Operation(summary = "whatsapp二维码销毁")
    @PostMapping("/account-auth-qrcode-remove")
    public BaseResult<Boolean> accountAuthQrcodeRemove(@RequestBody @Validated ProxyAccountAuthQrcodeRemoveDTO dto) {
        proxyAccountBizService.accountAuthQrcodeRemove(dto);
        return BaseResult.success(true);
    }

    @Operation(summary = "解除体验账号绑定")
    @PostMapping("/unbind-trial-account")
    public BaseResult<Boolean> unbindTrialAccount(@RequestBody @Validated ProxyAccountUnbindDTO dto) {
        return BaseResult.success(proxyAccountBizService.unbindTrialAccount(dto));
    }
    @Operation(summary = "搜索陌生好友(需要完整手机号)")
    @PostMapping("/search-stranger")
    public BaseResult<ProxySearchStrangerVO> searchStranger(@RequestBody @Validated SearchStrangerDTO dto) {
        return BaseResult.success(proxyAccountFriendBizService.searchStrangerAndOpenNewSession(dto));
    }

    @Operation(summary = "whatsApp代理账号登出")
    @PostMapping("/logout")
    public BaseResult<Boolean> logout(@RequestBody @Validated ProxyAccountLogoutDTO dto) {
        return BaseResult.success(proxyAccountBizService.logout(dto));
    }

    @Operation(summary = "撤回WhatsApp消息")
    @PostMapping("/revoke-whatsapp-message")
    public BaseResult<RevokeWhatsappMessageStatusVO> revokeWhatsappMessage(@RequestBody @Validated RevokeWhatsappMessageDTO dto) {
        return BaseResult.success(proxyAccountBizService.revokeWhatsappMessage(dto));
    }

    @Operation(summary = "查询WhatsApp消息状态")
    @PostMapping("/query-whatsapp-message-status")
    public BaseResult<Void> queryWhatsappMessageStatus(@RequestBody @Validated QueryWhatsappMessageStatusDTO dto) {
        proxyAccountBizService.queryWhatsappMessageStatus(dto);
        return BaseResult.success();
    }

}
