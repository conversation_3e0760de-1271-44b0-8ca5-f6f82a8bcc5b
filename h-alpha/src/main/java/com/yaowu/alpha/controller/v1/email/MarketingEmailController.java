package com.yaowu.alpha.controller.v1.email;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.email.biz.IMarketingEmailBizService;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.dto.email.MarketingEmailQueryDTO;
import com.yaowu.alpha.model.dto.email.MarketingEmailStatsQueryDTO;
import com.yaowu.alpha.model.vo.email.MarketingEmailDetailVO;
import com.yaowu.alpha.model.vo.email.MarketingEmailListVO;
import com.yaowu.alpha.model.vo.email.MarketingEmailStatsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 营销邮件控制器
 *
 * <AUTHOR>
 * @since 2025-01-19
 */
@Tag(name = "Marketing Email", description = "营销邮件管理接口")
@RestController
@RequestMapping("/api/v1/marketing-email")
@RequiredArgsConstructor
@Slf4j
public class MarketingEmailController {

    private final IMarketingEmailBizService marketingEmailBizService;

    @Operation(summary = "分页查询营销邮件列表", 
               description = "支持按状态、时间、收发件人等条件筛选营销邮件")
    @PostMapping("/list")
    public BaseResult<BasePage<MarketingEmailListVO>> pageMarketingEmails(@RequestBody @Valid MarketingEmailQueryDTO queryDTO) {
        BasePage<MarketingEmailListVO> result = marketingEmailBizService.pageMarketingEmails(queryDTO);
        return BaseResult.success(result);
    }

    @Operation(summary = "查询营销邮件详情", 
               description = "获取营销邮件的完整信息和事件轨迹")
    @PostMapping("/detail")
    public BaseResult<MarketingEmailDetailVO> getMarketingEmailDetail(@RequestBody @Valid IdDTO idDTO) {
        MarketingEmailDetailVO result = marketingEmailBizService.getMarketingEmailDetail(idDTO.getId());
        return BaseResult.success(result);
    }

    @Operation(summary = "按租户和时间获取营销邮件统计指标", 
               description = "支持按租户和时间范围筛选统计营销邮件指标，内部租户可查看所有租户数据")
    @PostMapping("/stats")
    public BaseResult<MarketingEmailStatsVO> getMarketingEmailStatsByTenant(@RequestBody @Valid MarketingEmailStatsQueryDTO queryDTO) {
        MarketingEmailStatsVO result = marketingEmailBizService.getMarketingEmailStatsByTenant(queryDTO);
        return BaseResult.success(result);
    }
} 