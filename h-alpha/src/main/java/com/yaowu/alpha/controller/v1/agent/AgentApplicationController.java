package com.yaowu.alpha.controller.v1.agent;

import com.freedom.objectstorage.annotation.FileSignHandle;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.agent.IAgentApplicationBizService;
import com.yaowu.alpha.model.dto.agent.AgentApplicationQueryDTO;
import com.yaowu.alpha.model.dto.agent.ChatAgentEditDTO;
import com.yaowu.alpha.model.dto.agent.ExtractorAgentEditDTO;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.vo.agent.AgentApplicationCardVO;
import com.yaowu.alpha.model.vo.agent.ChatAgentDetailVO;
import com.yaowu.alpha.model.vo.agent.ExtractorAgentDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 14:01
 */
@RestController
@RequiredArgsConstructor
@Api(value = "智能体应用", tags = "智能体应用")
@RequestMapping("/v1/agent/application/")
@Slf4j
public class AgentApplicationController {

    private final IAgentApplicationBizService agentApplicationBizService;

    @PostMapping("chat-agent/create")
    @ApiOperation("创建对话智能体")
    public BaseResult<Long> createChatAgent(@RequestBody @Valid ChatAgentEditDTO dto) {
        Long agentId = agentApplicationBizService.createChatAgent(dto);
        return BaseResult.success(agentId);
    }

    @PostMapping("extractor-agent/create")
    @ApiOperation("创建提取器智能体")
    public BaseResult<Long> createExtractorAgent(@RequestBody @Valid ExtractorAgentEditDTO dto) {
        Long agentId = agentApplicationBizService.createExtractorAgent(dto);
        return BaseResult.success(agentId);
    }

    @PostMapping("/list")
    @ApiOperation("获取智能体应用列表")
    @FileSignHandle
    public BaseResult<List<AgentApplicationCardVO>> listAgentApplications(@RequestBody @Valid AgentApplicationQueryDTO dto) {
        return BaseResult.success(agentApplicationBizService.listAgentApplications(dto));
    }

    @PostMapping("chat-agent/update")
    @ApiOperation("更新对话智能体")
    public BaseResult<Boolean> updateChatAgent(@RequestBody @Valid ChatAgentEditDTO dto) {
        agentApplicationBizService.updateChatAgent(dto);
        return BaseResult.success();
    }
    
    @PostMapping("extractor-agent/update")
    @ApiOperation("更新提取器智能体")
    public BaseResult<Boolean> updateExtractorAgent(@RequestBody @Valid ExtractorAgentEditDTO dto) {
        agentApplicationBizService.updateExtractorAgent(dto);
        return BaseResult.success();
    }

    @PostMapping("chat-agent/detail")
    @ApiOperation("获取对话智能体详情")
    @FileSignHandle
    public BaseResult<ChatAgentDetailVO> getChatAgentDetail(@RequestBody @Valid IdDTO dto) {
        return BaseResult.success(agentApplicationBizService.getChatAgentDetail(dto.getId()));
    }
    
    @PostMapping("extractor-agent/detail")
    @ApiOperation("获取提取器智能体详情")
    @FileSignHandle
    public BaseResult<ExtractorAgentDetailVO> getExtractorAgentDetail(@RequestBody @Valid IdDTO dto) {
        return BaseResult.success(agentApplicationBizService.getExtractorAgentDetail(dto.getId()));
    }

    @PostMapping("/delete")
    @ApiOperation("删除智能体应用")
    public BaseResult<Boolean> deleteAgentApplication(@RequestBody @Valid IdDTO dto) {
        agentApplicationBizService.deleteAgentApplication(dto.getId());
        return BaseResult.success();
    }

}
