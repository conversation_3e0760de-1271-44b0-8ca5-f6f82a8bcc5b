package com.yaowu.alpha.controller.v1.common;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.ICommonTranslationBizService;
import com.yaowu.alpha.model.dto.common.CommonTranslateDTO;
import com.yaowu.alpha.model.vo.common.CommonTranslateVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通用翻译Controller
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/common/translation")
@RequiredArgsConstructor
@Validated
@Tag(name = "通用翻译", description = "通用翻译相关接口")
public class CommonTranslationController {

    private final ICommonTranslationBizService commonTranslationBizService;

    /**
     * 通用文本翻译
     *
     * @param dto 翻译请求参数
     * @return 翻译结果
     */
    @Operation(summary = "通用文本翻译", description = "支持根据国家名称或指定语言类型进行文本翻译")
    @PostMapping("/translate")
    public BaseResult<CommonTranslateVO> translateText(@RequestBody @Valid CommonTranslateDTO dto) {
        CommonTranslateVO result = commonTranslationBizService.translateText(dto);
        return BaseResult.success(result);
    }
} 