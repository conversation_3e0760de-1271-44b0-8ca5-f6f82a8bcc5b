package com.yaowu.alpha.controller.v1.collection;

import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionRawDataBizService;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionTaskBizService;
import com.yaowu.alpha.model.dto.collection.*;
import com.yaowu.alpha.model.dto.common.IdDTO;
import com.yaowu.alpha.model.vo.collection.DataCollectionRawDataVO;
import com.yaowu.alpha.model.vo.collection.DataCollectionTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 数据采集任务管理接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "数据采集任务", tags = "数据采集任务接口")
@RequestMapping("/v1/collection/task")
@Slf4j
@Validated
public class DataCollectionTaskController {
    
    private final IDataCollectionTaskBizService dataCollectionTaskBizService;
    private final IDataCollectionRawDataBizService dataCollectionRawDataBizService;
    
    /**
     * 分页查询数据采集任务
     */
    @ApiOperation(value = "分页查询数据采集任务")
    @PostMapping("/page")
    public BaseResult<BasePage<DataCollectionTaskVO>> pageDataCollectionTasks(@RequestBody @Valid DataCollectionTaskPageDTO dto) {
        BasePage<DataCollectionTaskVO> page = dataCollectionTaskBizService.pageDataCollectionTasks(dto);
        return BaseResult.success(page);
    }
    
    /**
     * 创建数据采集任务
     */
    @ApiOperation(value = "创建数据采集任务")
    @PostMapping("/create")
    public BaseResult<Long> createDataCollectionTask(@RequestBody @Valid DataCollectionTaskCreateDTO dto) {
        Long taskId = dataCollectionTaskBizService.createDataCollectionTask(dto);
        return BaseResult.success(taskId);
    }

    /**
     * 编辑数据采集任务
     */
    @ApiOperation(value = "编辑数据采集任务", notes = "编辑数据采集任务的关键字、国家、地区和预计执行时间（仅支持待启用状态的任务）")
    @PostMapping("/update")
    public BaseResult<Boolean> updateDataCollectionTask(@RequestBody @Valid DataCollectionTaskUpdateDTO dto) {
        Boolean result = dataCollectionTaskBizService.updateDataCollectionTask(dto);
        return BaseResult.success(result);
    }
    
    /**
     * 获取数据采集任务详情
     */
    @ApiOperation(value = "获取数据采集任务详情")
    @GetMapping("/detail")
    public BaseResult<DataCollectionTaskVO> getDataCollectionTaskDetail(@RequestParam("id") Long id) {
        DataCollectionTaskVO taskVO = dataCollectionTaskBizService.getDataCollectionTaskDetail(id);
        return BaseResult.success(taskVO);
    }
    
    /**
     * 启动数据采集任务
     */
    @ApiOperation(value = "启动数据采集任务", notes = "启动数据采集任务，只有待启用状态的任务才可启动")
    @PostMapping("/start")
    public BaseResult<Boolean> startDataCollectionTask(@RequestBody @Valid IdDTO dto) {
        Boolean result = dataCollectionTaskBizService.startDataCollectionTask(dto.getId());
        return BaseResult.success(result);
    }
    
    /**
     * 暂停数据采集任务
     */
    @ApiOperation(value = "暂停数据采集任务")
    @PostMapping("/pause")
    public BaseResult<Boolean> pauseDataCollectionTask(@RequestBody @Valid IdDTO dto) {
        Boolean result = dataCollectionTaskBizService.pauseDataCollectionTask(dto.getId());
        return BaseResult.success(result);
    }
    
    /**
     * 恢复数据采集任务
     */
    @ApiOperation(value = "恢复数据采集任务")
    @PostMapping("/resume")
    public BaseResult<Boolean> resumeDataCollectionTask(@RequestBody @Valid IdDTO dto) {
        Boolean result = dataCollectionTaskBizService.resumeDataCollectionTask(dto.getId());
        return BaseResult.success(result);
    }
    
    /**
     * 终止数据采集任务
     */
    @ApiOperation(value = "终止数据采集任务", notes = "终止数据采集任务，只有待执行和执行中的任务才可终止")
    @PostMapping("/terminate")
    public BaseResult<Boolean> terminateDataCollectionTask(@RequestBody @Valid IdDTO dto) {
        Boolean result = dataCollectionTaskBizService.terminateDataCollectionTask(dto.getId());
        return BaseResult.success(result);
    }
    
    /**
     * 完成数据采集任务
     */
    @ApiOperation(value = "完成数据采集任务", notes = "完成数据采集任务，只有待执行和执行中的任务才可完成")
    @PostMapping("/complete")
    public BaseResult<Boolean> completeDataCollectionTask(@RequestBody @Valid IdDTO dto) {
        Boolean result = dataCollectionTaskBizService.completeDataCollectionTask(dto.getId());
        return BaseResult.success(result);
    }
    
    /**
     * 分页查询数据采集原始数据
     */
    @ApiOperation(value = "分页查询数据采集原始数据")
    @PostMapping("/raw/page")
    public BaseResult<BasePage<DataCollectionRawDataVO>> pageDataCollectionRawData(@RequestBody @Valid DataCollectionRawDataPageDTO dto) {
        BasePage<DataCollectionRawDataVO> page = dataCollectionRawDataBizService.pageDataCollectionRawData(dto);
        return BaseResult.success(page);
    }
    
    /**
     * 数据上报接口
     */
    @ApiOperation(value = "数据上报", notes = "用于向系统上报采集到的原始数据")
    @PostMapping("/raw/report")
    public BaseResult<Long> reportData(@RequestBody @Valid DataCollectionRawReportDTO dto) {
        Long rawId = dataCollectionRawDataBizService.reportDataCollectionResult(dto);
        return BaseResult.success(rawId);
    }
} 