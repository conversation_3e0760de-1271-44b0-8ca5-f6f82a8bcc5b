package com.yaowu.alpha.controller.v1.translation;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.proxy.biz.IProxyChatTranslationBizService;
import com.yaowu.alpha.model.dto.translation.CancelTranslateChatMessageDTO;
import com.yaowu.alpha.model.dto.translation.TranslateChatMessageDTO;
import com.yaowu.alpha.model.dto.translation.TranslateMessageDTO;
import com.yaowu.alpha.model.vo.translation.LanguageTypeVO;
import com.yaowu.alpha.model.vo.translation.TranslationResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * <p>
 * 智能体聊天消息翻译快照表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@RestController
@RequestMapping("/api/v1/proxy/proxy-message-translation")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "聊天信息翻译")
public class ProxyMessageTranslationController {

    @Autowired
    private IProxyChatTranslationBizService proxyChatTranslationBizService;

    /**
     * 边写边译接口
     * 实时翻译用户输入的消息，不保存翻译记录
     *
     * @param dto 翻译请求参数
     * @return 翻译结果
     */
    @PostMapping("/translate-message")
    @Operation(summary = "边写边译", description = "实时翻译用户输入的消息，不保存翻译记录")
    public Flux<String> translateMessage(@RequestBody @Validated TranslateMessageDTO dto) {
        return proxyChatTranslationBizService.translateMessage(dto);
    }

    /**
     * 聊天消息翻译接口
     * 翻译指定消息ID的消息，并保存翻译记录
     *
     * @param dto 翻译请求参数
     * @return 翻译结果
     */
    @PostMapping("/translate-chat-message")
    @Operation(summary = "聊天消息翻译", description = "翻译指定消息ID的消息，并保存翻译记录")
    public BaseResult<TranslationResultVO> translateChatMessage(@RequestBody @Validated TranslateChatMessageDTO dto) {
        return BaseResult.success(proxyChatTranslationBizService.translateChatMessage(dto));
    }

    /**
     * 获取支持的翻译语言类型
     *
     * @return 支持的翻译语言类型列表
     */
    @GetMapping("/supported-languages")
    @Operation(summary = "获取支持的翻译语言类型", description = "返回系统支持的所有翻译语言类型")
    public BaseResult<List<LanguageTypeVO>> getSupportedLanguages() {
        return BaseResult.success(proxyChatTranslationBizService.getSupportedLanguages());
    }


    @PostMapping("/cancel-translate-chat-message")
    @Operation(summary = "取消翻译")
    public BaseResult<Boolean> cancelTranslateChatMessage(@RequestBody @Validated CancelTranslateChatMessageDTO dto) {
        return BaseResult.success(proxyChatTranslationBizService.cancelTranslateChatMessage(dto));
    }
}

