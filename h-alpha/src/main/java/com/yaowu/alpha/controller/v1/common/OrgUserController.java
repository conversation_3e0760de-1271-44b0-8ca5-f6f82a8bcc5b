package com.yaowu.alpha.controller.v1.common;

import com.freedom.mybatisplus.service.ITenantIdValueService;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.config.exception.ErrorCodeException;
import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.vo.common.TenantOrgInfoVO;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/18 15:41
 */
@Valid
@RestController
@RequiredArgsConstructor
@Api(value = "组织用户", tags = "组织用户")
@RequestMapping("/v1/org/user/")
@Slf4j
public class OrgUserController {

     private final ITenantIdValueService tenantIdValueService;

     private final CommonConfig commonConfig;

    @PostMapping("/check")
    @Operation(summary = "检查并获取组织信息")
    public BaseResult<TenantOrgInfoVO> checkUser() {
        Long tenantId = tenantIdValueService.getTenantId();
        if (tenantId == null) {
            throw new ErrorCodeException(ErrorCodeEnum.USER_ROLE_NOT_ENOUGH);
        }
        return BaseResult.success(TenantOrgInfoVO.builder()
                .tenantId(tenantId)
                .orgType(commonConfig.getOrgTypeByTenantId(tenantId).getValue())
                .build());
    }

}
