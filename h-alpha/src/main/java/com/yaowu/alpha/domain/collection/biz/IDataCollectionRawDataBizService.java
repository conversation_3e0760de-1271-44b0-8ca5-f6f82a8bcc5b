package com.yaowu.alpha.domain.collection.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawDataPageDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawReportDTO;
import com.yaowu.alpha.model.vo.collection.DataCollectionRawDataVO;

/**
 * 数据采集原始数据业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IDataCollectionRawDataBizService {
    
    /**
     * 分页查询数据采集原始数据
     * 
     * @param dto 查询条件
     * @return 分页结果
     */
    BasePage<DataCollectionRawDataVO> pageDataCollectionRawData(DataCollectionRawDataPageDTO dto);
    
    /**
     * 上报数据采集结果
     * 
     * @param dto 上报数据
     * @return 创建的原始数据ID
     */
    Long reportDataCollectionResult(DataCollectionRawReportDTO dto);
} 