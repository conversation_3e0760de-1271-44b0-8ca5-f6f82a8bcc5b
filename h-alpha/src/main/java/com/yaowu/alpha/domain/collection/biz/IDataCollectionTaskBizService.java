package com.yaowu.alpha.domain.collection.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskPageDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskUpdateDTO;
import com.yaowu.alpha.model.vo.collection.DataCollectionTaskVO;

/**
 * 数据采集任务业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IDataCollectionTaskBizService {
    
    /**
     * 分页查询数据采集任务
     * 
     * @param dto 查询条件
     * @return 分页结果
     */
    BasePage<DataCollectionTaskVO> pageDataCollectionTasks(DataCollectionTaskPageDTO dto);
    
    /**
     * 创建数据采集任务
     * 
     * @param dto 创建参数
     * @return 任务ID
     */
    Long createDataCollectionTask(DataCollectionTaskCreateDTO dto);
    
    /**
     * 获取数据采集任务详情
     * 
     * @param id 任务ID
     * @return 任务详情
     */
    DataCollectionTaskVO getDataCollectionTaskDetail(Long id);
    
    /**
     * 编辑数据采集任务
     * 只有在任务未启动（PENDING状态）时才可编辑
     * 
     * @param dto 编辑参数
     * @return 操作结果
     */
    Boolean updateDataCollectionTask(DataCollectionTaskUpdateDTO dto);
    
    /**
     * 暂停数据采集任务
     * 
     * @param id 任务ID
     * @return 操作结果
     */
    Boolean pauseDataCollectionTask(Long id);
    
    /**
     * 启动数据采集任务
     * 
     * @param id 任务ID
     * @return 操作结果
     */
    Boolean startDataCollectionTask(Long id);

    /**
     * 恢复数据采集任务
     * 
     * @param id 任务ID
     * @return 操作结果
     */
    Boolean resumeDataCollectionTask(Long id);
    
    /**
     * 终止数据采集任务
     * 
     * @param id 任务ID
     * @return 操作结果
     */
    Boolean terminateDataCollectionTask(Long id);
    
    /**
     * 完成数据采集任务
     * 
     * @param id 任务ID
     * @return 操作结果
     */
    Boolean completeDataCollectionTask(Long id);

    /**
     * 处理数据采集任务指令执行结果
     * <p>
     * 根据指令执行结果更新对应的采集任务状态：
     * <ul>
     *   <li><strong>指令执行成功</strong>：将采集任务状态从"待执行"变更为"执行中"</li>
     *   <li><strong>指令执行失败</strong>：将采集任务状态变更为"执行异常"并记录错误信息</li>
     * </ul>
     * 
     * @param collectionTaskId 采集任务ID
     * @param success 指令执行是否成功
     * @param errorReason 失败原因（仅在success为false时有效）
     */
    void handleCreateDataCollectionTaskResult(Long collectionTaskId, Boolean success, String errorReason);
} 