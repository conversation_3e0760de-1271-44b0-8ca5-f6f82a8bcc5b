package com.yaowu.alpha.domain.common.biz.impl;

import cn.hutool.core.collection.CollUtil;
import com.yaowu.alpha.domain.common.biz.IAreaBizService;
import com.yaowu.alpha.domain.common.service.batis.service.IAreaService;
import com.yaowu.alpha.domain.common.service.batis.service.ICountryService;
import com.yaowu.alpha.model.dto.common.CityQueryDTO;
import com.yaowu.alpha.model.dto.common.ParentAreaQueryDTO;
import com.yaowu.alpha.model.entity.common.Area;
import com.yaowu.alpha.model.entity.common.Country;
import com.yaowu.alpha.model.vo.common.AreaVO;
import com.yaowu.alpha.model.vo.common.CityVO;
import com.yaowu.alpha.model.vo.common.CountryVO;
import com.yaowu.alpha.model.vo.common.StateVO;
import com.yaowu.alpha.utils.common.StreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地区业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AreaBizServiceImpl implements IAreaBizService {

    private final ICountryService countryService;
    private final IAreaService areaService;

    @Override
    public List<CountryVO> listCountries() {
        List<Country> countries = countryService.list();
        
        if (CollUtil.isEmpty(countries)) {
            return CollUtil.newArrayList();
        }
        
        return StreamUtil.of(countries)
            .map(this::convertToCountryVO)
            .toList();
    }

    @Override
    public List<CityVO> listCitiesByCountry(CityQueryDTO dto) {
        // 通过两级子查询指定国家下的所有城市
        List<Area> cities = areaService.getCitiesByCountryAreaId(dto.getCountryAreaId());
        
        if (CollUtil.isEmpty(cities)) {
            return CollUtil.newArrayList();
        }
        
        return StreamUtil.of(cities)
            .map(this::convertToCityVO)
            .toList();
    }

    @Override
    public List<AreaVO> listChildrenByParent(ParentAreaQueryDTO dto) {
        // 通用查询方法：根据父区域ID查询子区域列表
        List<Area> children = areaService.getChildrenByParentAreaId(dto.getParentAreaId());
        
        if (CollUtil.isEmpty(children)) {
            return CollUtil.newArrayList();
        }
        
        return StreamUtil.of(children)
            .map(this::convertToAreaVO)
            .toList();
    }

    /**
     * 转换Country实体为CountryVO
     *
     * @param country 国家实体
     * @return 国家VO
     */
    private CountryVO convertToCountryVO(Country country) {
        return CountryVO.builder()
            .areaId(country.getAreaId())
            .countryZhName(country.getCountryZhName())
            .countryEnName(country.getCountryEnName())
            .countryFlag(country.getCountryFlag())
            .phoneCode(country.getPhoneCode())
            .countryIso(country.getCountryIso())
            .build();
    }

    /**
     * 转换Area实体为AreaVO（通用方法）
     *
     * @param area 区域实体
     * @return 区域VO
     */
    private AreaVO convertToAreaVO(Area area) {
        return AreaVO.builder()
            .areaId(area.getAreaId())
            .areaZhName(area.getChineseName())
            .areaEnName(area.getLocalLangName())
            .parentAreaId(area.getParentId())
            .parentEnName(area.getParentLocalName())
            .parentZhName(area.getParentName())
            .build();
    }

    /**
     * 转换Area实体为StateVO
     *
     * @param area 区域实体
     * @return 州省VO
     */
    private StateVO convertToStateVO(Area area) {
        return StateVO.builder()
            .areaId(area.getAreaId())
            .chineseName(area.getChineseName())
            .localLangName(area.getLocalLangName())
            .parentLocalName(area.getParentLocalName())
            .parentName(area.getParentName())
            .build();
    }

    /**
     * 转换Area实体为CityVO
     *
     * @param area 区域实体
     * @return 城市VO
     */
    private CityVO convertToCityVO(Area area) {
        return CityVO.builder()
            .areaId(area.getAreaId())
            .areaZhName(area.getChineseName())
            .areaEnName(area.getLocalLangName())
            .parentAreaId(area.getParentId())
            .parentEnName(area.getParentLocalName())
            .parentZhName(area.getParentName())
            .build();
    }
}
