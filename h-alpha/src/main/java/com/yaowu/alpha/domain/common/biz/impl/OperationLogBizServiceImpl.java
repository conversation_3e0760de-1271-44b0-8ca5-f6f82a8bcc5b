package com.yaowu.alpha.domain.common.biz.impl;

import cn.hutool.json.JSONUtil;
import com.freedom.security.common.SecurityContext;
import com.freedom.security.common.UserDetailsDto;
import com.yaowu.alpha.domain.common.biz.IOperationLogBizService;
import com.yaowu.alpha.domain.common.service.batis.service.IOperationLogService;
import com.yaowu.alpha.enums.common.SystemOperationLogBizCodeEnum;
import com.yaowu.alpha.model.bo.common.FriendStatusOperationContent;
import com.yaowu.alpha.model.bo.common.OperationContent;
import com.yaowu.alpha.model.bo.common.ProxyAccountDeleteContent;
import com.yaowu.alpha.model.bo.common.ProxyAccountOperationContent;
import com.yaowu.alpha.model.entity.common.OperationLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 操作日志业务服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogBizServiceImpl implements IOperationLogBizService {

    private final IOperationLogService operationLogService;

    @Override
    @Async
    public void recordOperationLog(String operateBizCode, String operateBizDesc, String operateBizId, String operateContent) {
        try {
            UserDetailsDto currentUser = SecurityContext.getCurrentUser();
            
            OperationLog operationLog = new OperationLog()
                    .setOperatorId(currentUser != null ? currentUser.getUserId() : 0L)
                    .setOperator(currentUser != null ? currentUser.getRealName() : "UNKNOWN")
                    .setOperateTime(LocalDateTime.now())
                    .setOperateBizCode(operateBizCode)
                    .setOperateBizDesc(operateBizDesc)
                    .setOperateBizId(operateBizId)
                    .setOperateContent(operateContent);
                    
            operationLogService.save(operationLog);
            log.info("操作日志记录成功: bizCode={}, bizId={}", operateBizCode, operateBizId);
        } catch (Exception e) {
            log.error("操作日志记录失败: bizCode={}, bizId={}", operateBizCode, operateBizId, e);
        }
    }

    @Override
    @Async
    public void recordOperationLog(SystemOperationLogBizCodeEnum operationBizCode, String operateBizId, OperationContent operationContent) {
        try {
            UserDetailsDto currentUser = SecurityContext.getCurrentUser();
            
            // 设置操作时间
            if (operationContent.getOperationTime() == null) {
                operationContent.setOperationTime(LocalDateTime.now());
            }
            
            OperationLog operationLog = new OperationLog()
                    .setOperatorId(currentUser != null ? currentUser.getUserId() : 0L)
                    .setOperator(currentUser != null ? currentUser.getRealName() : "UNKNOWN")
                    .setOperateTime(LocalDateTime.now())
                    .setOperateBizCode(operationBizCode.getCode())
                    .setOperateBizDesc(operationBizCode.getDesc())
                    .setOperateBizId(operateBizId)
                    .setOperateContent(JSONUtil.toJsonStr(operationContent));
                    
            operationLogService.save(operationLog);
            log.info("操作日志记录成功: bizCode={}, bizId={}", operationBizCode.getCode(), operateBizId);
        } catch (Exception e) {
            log.error("操作日志记录失败: bizCode={}, bizId={}", operationBizCode.getCode(), operateBizId, e);
        }
    }

    @Override
    public void recordProxyAccountStatusLog(Long accountId, Integer beforeStatus, Integer afterStatus) {
        OperationContent operationContent = new ProxyAccountOperationContent()
                .setAccountId(accountId)
                .setBeforeStatus(beforeStatus)
                .setAfterStatus(afterStatus)
                .setOperationType("STATUS_CHANGE")
                .setDescription("代理账号状态变更");
        
        recordOperationLog(
                SystemOperationLogBizCodeEnum.PROXY_ACCOUNT_STATUS,
                String.valueOf(accountId),
                operationContent
        );
    }

    @Override
    public void recordFriendStatusLog(Long friendId, Integer beforeStatus, Integer afterStatus) {
        OperationContent operationContent = new FriendStatusOperationContent()
                .setFriendId(friendId)
                .setBeforeStatus(beforeStatus)
                .setAfterStatus(afterStatus)
                .setOperationType("STATUS_CHANGE")
                .setDescription("好友状态变更");
        
        recordOperationLog(
                SystemOperationLogBizCodeEnum.FRIEND_STATUS,
                String.valueOf(friendId),
                operationContent
        );
    }

    @Override
    public void recordProxyAccountDeleteLog(Long accountId, Integer statusBeforeDelete, String deleteReason) {
        OperationContent operationContent = new ProxyAccountDeleteContent()
                .setAccountId(accountId)
                .setStatusBeforeDelete(statusBeforeDelete)
                .setDeleteReason(deleteReason)
                .setOperationType("ACCOUNT_DELETE")
                .setDescription("代理账号删除");
        
        recordOperationLog(
                SystemOperationLogBizCodeEnum.PROXY_ACCOUNT_DELETE,
                String.valueOf(accountId),
                operationContent
        );
    }

} 