package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportContactUpdateNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportContractUpdateRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinReportContactUpdateMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信通讯录上报
 */
@Component
public class WetoolReportContactUpdateAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportContractUpdateRequestDTO, ReportContactUpdateNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_CONTACT_UPDATE.equals(action);
    }

    @Override
    public ReportContactUpdateNoticeRequestDTO transferRequest(WetoolWexinReportContractUpdateRequestDTO input) {
        return WetoolWexinReportContactUpdateMapStruct.INSTANCE.toReportContactUpdateRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
