package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.RevokeWhatsappMsgNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinRevokeWhatsappMsgRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolRevokeWhatsappMsgMapStruct;
import org.springframework.stereotype.Component;

/**
 * 撤回WhatsApp消息适配器
 * <AUTHOR>
 */
@Component
public class WetoolRevokeWhatsappMsgAdapter extends AbstractWetoolActionAdapter<WetoolWexinRevokeWhatsappMsgRequestDTO, RevokeWhatsappMsgNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REVOKE_WHATSAPP_MSG.equals(action);
    }

    @Override
    public RevokeWhatsappMsgNoticeRequestDTO transferRequest(WetoolWexinRevokeWhatsappMsgRequestDTO input) {
        return WetoolRevokeWhatsappMsgMapStruct.INSTANCE.toRevokeWhatsappMsgRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }
}
