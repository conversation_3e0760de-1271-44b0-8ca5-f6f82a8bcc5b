package com.yaowu.alpha.domain.facebook.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.facebook.FacebookGroupMember;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook群组成员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookGroupMemberService extends IService<FacebookGroupMember> {

    Map<Long, Integer> getMemberStatisticMapByTaskIds(List<Long> taskIds);

    Map<Long, Integer> getMemberStatisticMapByGroupIds(List<Long> groupIds);
} 