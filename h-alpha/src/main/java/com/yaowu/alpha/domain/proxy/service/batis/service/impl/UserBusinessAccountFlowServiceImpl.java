package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.UserAccountBusinessFlowMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IUserBusinessAccountFlowService;
import com.yaowu.alpha.model.dto.proxy.control.ProxyAccountQueryDTO;
import com.yaowu.alpha.model.dto.proxy.request.AccountFlowQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.user.UserBusinessAccountFlow;
import com.yaowu.alpha.utils.common.StreamUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025/4/14-17:15
 */
@Service
public class UserBusinessAccountFlowServiceImpl extends ServiceImpl<UserAccountBusinessFlowMapper, UserBusinessAccountFlow>
        implements IUserBusinessAccountFlowService {

    @Override
    public BasePage<UserBusinessAccountFlow> pageByCondition(AccountFlowQueryDTO dto) {
        LambdaQueryWrapper<UserBusinessAccountFlow> wrapper = wrapper(dto);
        if (wrapper.isEmptyOfWhere()) {
            return new BasePage<>(dto.pageRequest());
        }
        Page<UserBusinessAccountFlow> page = page(dto.pageRequest(), wrapper);
        return BasePage.simpleConvert(page, Function.identity());
    }

    @Override
    public UserBusinessAccountFlow getByCondition(AccountFlowQueryDTO dto) {
        LambdaQueryWrapper<UserBusinessAccountFlow> wrapper = wrapper(dto);
        if (wrapper.isEmptyOfWhere()) {
            return null;
        }
        List<UserBusinessAccountFlow> list = list(wrapper);
        return StreamUtil.findFirst(list);
    }

    private LambdaQueryWrapper<UserBusinessAccountFlow> wrapper(AccountFlowQueryDTO queryDTO) {
        LambdaQueryWrapper<UserBusinessAccountFlow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(queryDTO.getAccountId()), UserBusinessAccountFlow::getAccountId, queryDTO.getAccountId());
        wrapper.eq(queryDTO.getFlowType() != null, UserBusinessAccountFlow::getFlowType, queryDTO.getFlowType());
        wrapper.and(CollUtil.isNotEmpty(queryDTO.getFlowTypes()), q -> q.in(UserBusinessAccountFlow::getFlowType, queryDTO.getFlowTypes()));
        // 只查询子流水
        wrapper.gt(queryDTO.getOnlySelectChild() != null && BooleanUtil.isTrue(queryDTO.getOnlySelectChild()), UserBusinessAccountFlow::getParentId, 0);
        wrapper.eq(queryDTO.getOnlySelectChild() != null && BooleanUtil.isFalse(queryDTO.getOnlySelectChild()), UserBusinessAccountFlow::getParentId, 0);
        wrapper.eq(queryDTO.getRelationBizId() != null, UserBusinessAccountFlow::getRelationBizId, queryDTO.getRelationBizId());
        wrapper.eq(queryDTO.getRelationBizType() != null, UserBusinessAccountFlow::getRelationBizType, queryDTO.getRelationBizType());
        wrapper.orderByDesc(UserBusinessAccountFlow::getCreateTime);
        return wrapper;
    }
}
