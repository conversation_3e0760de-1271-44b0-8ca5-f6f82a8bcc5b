package com.yaowu.alpha.domain.knowledge.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.freedom.mybatisplus.service.ITenantIdValueService;
import com.freedom.objectstorage.utils.CloudUploadUtil;
import com.freedom.security.common.SecurityContext;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.config.tenant.TenantIdValueServiceImpl;
import com.yaowu.alpha.domain.knowledge.biz.IKnowledgeBizService;
import com.yaowu.alpha.domain.knowledge.service.batis.service.IKnowledgeFileService;
import com.yaowu.alpha.domain.knowledge.service.batis.service.IKnowledgeService;
import com.yaowu.alpha.domain.llm.service.batis.service.IAgentApplicationService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyOrgManagementBizService;
import com.yaowu.alpha.model.bo.common.UploadFileModel;
import com.yaowu.alpha.model.dto.knowledge.*;
import com.yaowu.alpha.model.entity.agent.AgentApplication;
import com.yaowu.alpha.model.entity.knowledge.Knowledge;
import com.yaowu.alpha.model.entity.knowledge.KnowledgeFile;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeDetailVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeFilePageVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgePageVO;
import com.yaowu.alpha.utils.common.FileDownloadUtil;
import com.yaowu.alpha.utils.common.FileSizeUtil;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.convertor.knowledge.KnowledgeMapStruct;
import com.zhipu.oapi.ClientV4;
import com.zhipu.oapi.service.v4.api.DocumentClientApiService;
import com.zhipu.oapi.service.v4.api.KnowledgeClientApiService;
import com.zhipu.oapi.service.v4.knowledge.KnowledgeBaseParams;
import com.zhipu.oapi.service.v4.knowledge.KnowledgeEditResponse;
import com.zhipu.oapi.service.v4.knowledge.KnowledgeResponse;
import com.zhipu.oapi.service.v4.knowledge.document.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 知识库业务服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class KnowledgeBizServiceImpl implements IKnowledgeBizService {

    @Autowired
    private IKnowledgeService knowledgeService;

    @Autowired
    private IKnowledgeFileService knowledgeFileService;

    @Autowired
    private IAgentApplicationService agentApplicationService;

    @Autowired
    private ITenantIdValueService tenantIdValueService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private IProxyOrgManagementBizService proxyOrgManagementBizService;

    private ClientV4 client;


    @Autowired
    private TenantIdValueServiceImpl tenantIdValueServiceImpl;

    private synchronized ClientV4 getClient() {
        if (this.client == null) {
            String zhipuApiKey = commonConfig.getZhipuApiKey();
            if (StrUtil.isNotBlank(zhipuApiKey)) {
                this.client = new ClientV4.Builder(zhipuApiKey)
                        .enableTokenCache()
                        .networkConfig(300, 100, 100, 100, TimeUnit.SECONDS)
                        .connectionPool(new okhttp3.ConnectionPool(8, 1, TimeUnit.SECONDS))
                        .build();
            } else {
                log.error("Zhipu API key is not configured. The client will not be initialized.");
                throw new BusinessException("Zhipu API key is not configured");
            }
        }
        return this.client;
    }

    @Override
    public Long createKnowledge(KnowledgeAddDTO dto) {

        KnowledgeResponse apply = remoteCreateKnowledge(dto);
        Knowledge knowledge = KnowledgeMapStruct.INSTANCE.toKnowledge(dto);
        knowledge.setKnowledgeProvider(1);
        knowledge.setUserId(SecurityContext.getCurrentUserId());
        knowledge.setKnowledgeExternalId(apply.getData().getId());
        knowledge.setTenantId(dto.getTenantId());
        knowledgeService.save(knowledge);
        return knowledge.getId();
    }

    private KnowledgeResponse remoteCreateKnowledge(KnowledgeAddDTO dto) {
        KnowledgeBaseParams build = KnowledgeBaseParams.builder()
                .name(dto.getKnowledgeName())
                .description(dto.getKnowledgeDesc())
                .embeddingId(3).build();
        ClientV4 client = getClient();
        KnowledgeResponse apply = new KnowledgeClientApiService(
                client.getConfig().getHttpClient(),
                client.getConfig().getBaseUrl())
                .knowledgeCreate(build)
                .apply(client);
        log.info("knowledgeCreate result: {}", apply);
        if (!apply.isSuccess()) {
            log.error("知识库创建失败, {}", apply);
            throw new BusinessException("知识库创建失败");
        }
        return apply;
    }

    @Override
    public BasePage<KnowledgePageVO> knowledgePage(KnowledgePageDTO dto) {
        Long tenantId = tenantIdValueServiceImpl.getCurTenantIdByOrgType(dto.getTenantId());
        dto.setTenantId(tenantId);
        Page<Knowledge> page = knowledgeService.pageByDto(dto);
        List<Knowledge> knowledgeList = page.getRecords();
        List<Long> idList = knowledgeList.stream().map(Knowledge::getId).toList();
        Map<Long, List<KnowledgeFile>> knowledgeFileMap = knowledgeFileService.getByKnowledgeIds(idList);
        Map<Long, List<AgentApplication>> agentMap = agentApplicationService.getByKnowledgeIds(idList);

        Set<Long> tenantIds = StreamUtil.toSet(page.getRecords(), Knowledge::getTenantId);
        Map<Long, String> orgId2OrgName = proxyOrgManagementBizService.getOrgId2OrgName(tenantIds);
        List<KnowledgePageVO> voList = knowledgeList.stream().map(knowledge -> {
            KnowledgePageVO knowledgePageVO = KnowledgeMapStruct.INSTANCE.toKnowledgePageVO(knowledge,orgId2OrgName.get(knowledge.getTenantId()));
            List<KnowledgeFile> knowledgeFiles = knowledgeFileMap.getOrDefault(knowledge.getId(), new ArrayList<>());
            knowledgePageVO.setFileNum(knowledgeFiles.size());
            List<AgentApplication> agents = agentMap.get(knowledge.getId());
            if (CollUtil.isNotEmpty(agents)) {
                knowledgePageVO.setAssociatedAgents(agents.stream().map(AgentApplication::getAppName).toList());
            }
            return knowledgePageVO;
        }).toList();

        return new BasePage<>(page, voList);
    }

    @Override
    public KnowledgeDetailVO getKnowledgeDetail(Long id) {
        Knowledge knowledge = getKnowledge(id);
        List<KnowledgeFile> knowledgeFileList = knowledgeFileService.getByKnowledgeId(id);
        KnowledgeDetailVO vo = KnowledgeMapStruct.INSTANCE.toKnowledgeDetailVO(knowledge);
        Map<Long, List<AgentApplication>> agentMap = agentApplicationService.getByKnowledgeIds(List.of(id));
        vo.setFileNum(knowledgeFileList.size());
        vo.setAssociatedAgents(agentMap.getOrDefault(id, new ArrayList<>()).stream().map(AgentApplication::getAppName).toList());
        return vo;
    }

    @Override
    public Boolean updateKnowledge(KnowledgeEditDTO dto) {
        Knowledge knowledge = getKnowledge(dto.getId());
        KnowledgeMapStruct.INSTANCE.toKnowledge(dto, knowledge);
        remoteUpdateKnowledge(knowledge);
        return knowledgeService.updateById(knowledge);
    }

    private void remoteUpdateKnowledge(Knowledge knowledge) {
        if (StrUtil.isBlank(knowledge.getKnowledgeExternalId())) {
            return;
        }
        KnowledgeBaseParams build = KnowledgeBaseParams.builder()
                .knowledgeId(knowledge.getKnowledgeExternalId())
                .embeddingId(3)
                .name(knowledge.getKnowledgeName())
                .description(knowledge.getKnowledgeDesc())
                .build();
        ClientV4 client = getClient();
        KnowledgeEditResponse apply = new KnowledgeClientApiService(
                client.getConfig().getHttpClient(), client.getConfig().getBaseUrl())
                .knowledgeModify(build)
                .apply(client);
        log.info("knowledgeModify result: {}", apply);
        if (!apply.isSuccess()) {
            log.error("知识库修改失败, {}", apply);
            throw new BusinessException("知识库修改失败");
        }
    }

    @Override
    public Boolean deleteKnowledge(Long id) {
        Knowledge knowledge = getKnowledge(id);
        List<KnowledgeFile> knowledgeFileList = knowledgeFileService.getByKnowledgeId(id);
        if (CollUtil.isNotEmpty(knowledgeFileList)) {
            throw new BusinessException("还存在文件, 请先删除文件");
        }
        Map<Long, List<AgentApplication>> knowledgeMap = agentApplicationService.getByKnowledgeIds(List.of(id));
        if (CollUtil.isNotEmpty(knowledgeMap.get(id))) {
            throw new BusinessException("还存在智能体应用, 请先删除智能体应用");
        }
        remoteDeleteKnowledge(knowledge);
        return knowledgeService.removeById(knowledge);
    }

    private void remoteDeleteKnowledge(Knowledge knowledge) {
        if (StrUtil.isBlank(knowledge.getKnowledgeExternalId())) {
            return;
        }
        KnowledgeBaseParams build = KnowledgeBaseParams.builder()
                .knowledgeId(knowledge.getKnowledgeExternalId())
                .build();
        ClientV4 client = getClient();
        // 设置params的相关属性
        KnowledgeEditResponse apply = new KnowledgeClientApiService(
                client.getConfig().getHttpClient(),
                client.getConfig().getBaseUrl())
                .knowledgeDelete(build)
                .apply(client);
        log.info("knowledgeDelete result: {}", apply);
        if (!apply.isSuccess()) {
            log.error("知识库删除失败, {}", apply);
            throw new BusinessException("知识库删除失败");
        }
    }

    @Override
    public BasePage<KnowledgeFilePageVO> filePage(KnowledgeFilePageDTO dto) {
        Page<KnowledgeFile> page = knowledgeFileService.pageByDto(dto);
        List<KnowledgeFile> knowledgeFileList = page.getRecords();
        List<KnowledgeFilePageVO> voList = new ArrayList<>();
        for (KnowledgeFile knowledgeFile : knowledgeFileList) {
            KnowledgeFilePageVO vo = KnowledgeMapStruct.INSTANCE.toKnowledgeFilePageVO(knowledgeFile);
            vo.setFileUrl(CloudUploadUtil.generateSignedUrl(knowledgeFile.getFileObjectKey()));
            vo.setFileSizeDesc(FileSizeUtil.formatFileSize(knowledgeFile.getFileSize()));
            voList.add(vo);
        }
        return new BasePage<>(page, voList);
    }

    @Override
    public List<Long> createKnowledgeFile(KnowledgeFileAddDTO dto) throws IOException {
        Knowledge knowledge = getKnowledge(dto.getKnowledgeId());
        List<Path> tempFileList = new ArrayList<>();
        List<KnowledgeFile> knowledgeFiles = new ArrayList<>();
        dto.getFiles().forEach(file -> {
            UploadFileModel fileModel = file.getFile();
            String url = CloudUploadUtil.generateSignedUrl(fileModel.getUrl());
            Path path = FileDownloadUtil.downloadTempFile(fileModel.getFileName(), url);
            tempFileList.add(path);
            DocumentSuccessInfo successInfo = remoteCreateKnowledgeFile(path, knowledge);
            String documentId = successInfo.getDocumentId();
            KnowledgeFile knowledgeFile = saveKnowledgeFile(dto, file, fileModel, documentId);
            knowledgeFiles.add(knowledgeFile);
        });
        // 删除临时文件
        for (Path path : tempFileList) {
            Files.deleteIfExists(path);
        }
        knowledgeFileService.saveBatch(knowledgeFiles);
        return knowledgeFiles.stream().map(KnowledgeFile::getId).toList();
    }

    private KnowledgeFile saveKnowledgeFile(KnowledgeFileAddDTO dto, KnowledgeFileAddDTO.KnowledgeFileDTO file, UploadFileModel fileModel, String documentId) {
        KnowledgeFile knowledgeFile = new KnowledgeFile();
        knowledgeFile.setKnowledgeId(dto.getKnowledgeId());
        knowledgeFile.setFileName(fileModel.getFileName());
        knowledgeFile.setFileSize(Long.parseLong(fileModel.getFileSize()));
        knowledgeFile.setFileObjectKey(fileModel.getUrl());
        knowledgeFile.setSliceRule(file.getSliceRule());
        knowledgeFile.setExternalId(documentId);
        return knowledgeFile;
    }

    private DocumentSuccessInfo remoteCreateKnowledgeFile(Path path, Knowledge knowledge) {
        DocumentCreateParams build = DocumentCreateParams.builder()
                .filePath(path.toString())
                .knowledgeId(knowledge.getKnowledgeExternalId())
                .purpose("retrieval")
                .build();
        DocumentObjectResponse apply;
        try {
            ClientV4 client = getClient();
            apply = new DocumentClientApiService(
                    client.getConfig().getHttpClient(),
                    client.getConfig().getBaseUrl())
                    .createDocument(build)
                    .apply(client);
            log.info("createDocument result: {}", apply);
            if (!apply.isSuccess()) {
                log.error("创建知识库文件失败, {}", apply);
                throw new BusinessException("创建知识库文件失败");
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return apply.getData().getSuccessInfos().get(0);
    }

    @Override
    public Boolean editKnowledgeFile(KnowledgeFileEditDTO dto) {
        KnowledgeFile knowledgeFile = getKnowledgeFile(dto.getId());
        if (Objects.equals(knowledgeFile.getSliceRule(), dto.getSliceRule())) {
            return true;
        }
        remoteEditKnowledgeFile(dto, knowledgeFile);
        knowledgeFile.setSliceRule(dto.getSliceRule());
        return knowledgeFileService.updateById(knowledgeFile);
    }

    private void remoteEditKnowledgeFile(KnowledgeFileEditDTO dto, KnowledgeFile knowledgeFile) {
        DocumentEditParams build = DocumentEditParams.builder()
                .id(knowledgeFile.getExternalId())
                .knowledgeType(dto.getSliceRule())
                .build();
        ClientV4 client = getClient();
        // 设置params的相关属性
        DocumentEditResponse apply = new DocumentClientApiService(
                client.getConfig().getHttpClient(),
                client.getConfig().getBaseUrl())
                .modifyDocument(build)
                .apply(client);
        log.info("modifyDocument result: {}", apply);
        if (!apply.isSuccess()) {
            log.error("修改知识库文件失败, {}", apply);
            throw new BusinessException("修改知识库文件失败");
        }
    }

    @Override
    public Boolean deleteKnowledgeFile(Long id) {
        KnowledgeFile knowledgeFile = getKnowledgeFile(id);
        if (StrUtil.isBlank(knowledgeFile.getExternalId())) {
            return true;
        }
        remoteDeleteKnowledgeFile(knowledgeFile);
        return knowledgeFileService.removeById(knowledgeFile);
    }

    private void remoteDeleteKnowledgeFile(KnowledgeFile knowledgeFile) {
        DocumentEditParams build = DocumentEditParams.builder()
                .id(knowledgeFile.getExternalId())
                .build();
        ClientV4 client = getClient();
        // 设置params的相关属性
        DocumentEditResponse apply = new DocumentClientApiService(
                client.getConfig().getHttpClient(),
                client.getConfig().getBaseUrl())
                .deleteDocument(build)
                .apply(client);
        log.info("deleteDocument result: {}", apply);
        if (!apply.isSuccess()) {
            log.error("删除知识库文件失败, {}", apply);
            throw new BusinessException("删除知识库文件失败");
        }
    }

    private Knowledge getKnowledge(Long dto) {
        Knowledge knowledge = knowledgeService.getById(dto);
        if (knowledge == null) {
            throw new BusinessException("知识库不存在");
        }
        return knowledge;
    }

    private KnowledgeFile getKnowledgeFile(Long id) {
        KnowledgeFile file = knowledgeFileService.getById(id);
        if (file == null) {
            throw new BusinessException("文件不存在");
        }
        return file;
    }
}
