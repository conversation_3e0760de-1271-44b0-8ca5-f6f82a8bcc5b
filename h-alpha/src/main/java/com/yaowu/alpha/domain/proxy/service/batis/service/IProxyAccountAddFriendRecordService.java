package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.proxy.control.AddFriendRecordRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountAddFriendRecord;

import java.util.List;


/**
 * <p>
 * 托管账户好友列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyAccountAddFriendRecordService extends IService<ProxyAccountAddFriendRecord> {

    /**
     * 查询添加好友记录
     *
     * @param proxyAccountId 代理账户配置项id
     * @param friendProxyId  好友微信id
     */
    ProxyAccountAddFriendRecord selectAddRecord(Long proxyAccountId, String friendProxyId);

    /**
     * 查询添加好友记录
     * 根据条件查询
     *
     * @param requestDTO 代理账户配置项id
     */
    List<ProxyAccountAddFriendRecord> queryByCondition(AddFriendRecordRequestDTO requestDTO);
}
