package com.yaowu.alpha.domain.common.processor;

import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.alpha.domain.common.service.batis.service.IContactVerificationTaskService;
import com.yaowu.alpha.enums.common.ContactVerificationStatusEnum;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 联系方式验证处理器抽象基类
 * 使用模板方法模式统一验证流程
 * 
 * <AUTHOR>
 * @since 2025-01-24 20:30:00
 */
@Slf4j
public abstract class AbstractContactVerificationProcessor implements IContactVerificationProcessor {

    @Autowired
    protected IContactVerificationTaskService contactVerificationTaskService;
    
    @Autowired
    protected IWarnNoticeBizService warnNoticeBizService;
    
    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 模板方法：统一的验证处理流程
     * @param task 验证任务
     * @return 验证结果
     */
    @Override
    public final void process(ContactVerificationTask task) {
        try {
            doVerifyProcess(task);
            updateTaskToDatabase(task);
            logVerificationResult(task);
        } catch (Exception e) {
            log.error("验证处理异常: contact={}, type={}, error={}",
                    task.getContact(), task.getContactType(), e.getMessage(), e);
            handleProcessorException(task, e);
        }
    }

    /**
     * 抽象方法：具体的验证逻辑
     * 子类必须实现此方法来定义具体的验证行为
     * @param task 验证任务
     * @return 验证结果
     */
    protected abstract void doVerifyProcess(ContactVerificationTask task);

    /**
     * 处理处理器执行异常
     */
    private void handleProcessorException(ContactVerificationTask task, Exception e) {
        log.error("验证处理器执行异常: contact={}, type={}, error={}",
                task.getContact(), task.getContactType(), e.getMessage(), e);

        String errorMessage = "处理器执行异常: " + e.getMessage();
        updateTaskAsFailed(task, errorMessage);
        
        // 发送企微通知
        sendFailureNotification(task, errorMessage);
    }

    /**
     * 更新任务为失败状态
     */
    private void updateTaskAsFailed(ContactVerificationTask task, String errorMessage) {
        task.setValidationStatus(ContactVerificationStatusEnum.FAILED)
                .setErrorMessage(errorMessage);
        updateTaskToDatabase(task);
        
        // 发送企微通知
        sendFailureNotification(task, errorMessage);
    }

    /**
     * 更新任务状态到数据库
     */
    private void updateTaskToDatabase(ContactVerificationTask task) {
        contactVerificationTaskService.updateById(task);
    }

    /**
     * 记录验证结果日志
     */
    private void logVerificationResult(ContactVerificationTask task) {
        log.info("验证任务处理完成: contact={}, type={}, status={}",
                task.getContact(), task.getContactType(), task.getValidationStatus());
    }
    
    /**
     * 发送验证失败通知
     * 
     * @param task 验证任务
     * @param errorMsg 错误信息
     */
    protected void sendFailureNotification(ContactVerificationTask task, String errorMsg) {
        try {
            String message = String.format("联系方式验证失败通知\n" +
                    "联系方式: %s\n" +
                    "验证类型: %s\n" +
                    "任务ID: %s\n" +
                    "失败原因: %s",
                    task.getContact(),
                    task.getContactType().getDesc(),
                    task.getId(),
                    errorMsg != null ? errorMsg : "未知错误");
            
            warnNoticeBizService.sendNoticeToWechat(message, commonConfig.getSystemWarnNoticeBotKey(), null);
            log.info("联系方式验证失败通知已发送: contact={}, type={}", task.getContact(), task.getContactType());
        } catch (Exception e) {
            log.error("发送联系方式验证失败通知异常: contact={}, type={}", task.getContact(), task.getContactType(), e);
        }
    }
}