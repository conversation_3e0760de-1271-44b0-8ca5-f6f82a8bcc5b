package com.yaowu.alpha.domain.facebook.service.batis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaowu.alpha.model.entity.facebook.FacebookGroup;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook群组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookGroupMapper extends BaseMapper<FacebookGroup> {

    /**
     * 根据任务ID列表统计每个任务的群组数量
     *
     * @param taskIds 任务ID列表
     * @return 任务ID -> 群组数量的映射
     */
    @MapKey("taskId")
    List<Map<String, Object>> getGroupStatisticByTaskIds(@Param("taskIds") List<Long> taskIds);

} 