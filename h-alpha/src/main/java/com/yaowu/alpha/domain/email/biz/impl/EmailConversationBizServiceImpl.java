package com.yaowu.alpha.domain.email.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.stream.StreamUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.objectstorage.utils.CloudUploadUtil;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.email.biz.IEmailConversationBizService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyChatMessageService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyConversationService;
import com.yaowu.alpha.enums.proxy.RequirementReadStatusEnum;
import com.yaowu.alpha.model.bo.proxy.ProxyChatMsgQuery;
import com.yaowu.alpha.model.dto.email.MailAttachment;
import com.yaowu.alpha.model.dto.email.ProxyEmailChatMessageContent;
import com.yaowu.alpha.model.dto.mail.MailConversationQueryDTO;
import com.yaowu.alpha.model.dto.mail.MailDetailQueryDTO;
import com.yaowu.alpha.model.dto.mail.MailMarkReadDTO;
import com.yaowu.alpha.model.dto.proxy.ProxyConversationQueryDTO;
import com.yaowu.alpha.model.dto.proxy.UnreadCountDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;
import com.yaowu.alpha.model.entity.proxy.ProxyConversation;
import com.yaowu.alpha.model.vo.email.EmailAttachmentVO;
import com.yaowu.alpha.model.vo.email.EmailConversationVO;
import com.yaowu.alpha.model.vo.email.EmailDetailVO;
import com.yaowu.alpha.utils.convertor.email.EmailConversationMapper;
import com.yaowu.alpha.utils.email.EmailContentUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件会话业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailConversationBizServiceImpl implements IEmailConversationBizService {

    private final IProxyConversationService proxyConversationService;
    private final IProxyChatMessageService proxyChatMessageService;
    private final IProxyAccountConfigService proxyAccountConfigService;

    /**
     * 分页获取邮件会话列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Override
    public BasePage<EmailConversationVO> pageConversations(MailConversationQueryDTO queryDTO) {
        // 转换查询条件为ProxyConversationQueryDTO
        ProxyConversationQueryDTO proxyQuery = convertToProxyQuery(queryDTO);
        if (proxyQuery == null) {
            return new BasePage<>(queryDTO.pageRequest());
        }
        
        // 执行分页查询
        Page<ProxyConversation> page = proxyConversationService.pageByCondition(proxyQuery);

        // 构建并返回分页结果
        return BasePage.convert(page, this::convertToVOList);
    }

    /**
     * 获取会话中的所有邮件
     *
     * @param dto 查询条件
     * @return 邮件详情列表
     */
    @Override
    public List<EmailDetailVO> getConversationMails(MailDetailQueryDTO dto) {
        resolveConversationId(dto);
        
        ProxyConversation conversation = getValidConversation(dto);
        if (conversation == null) {
            return CollUtil.newArrayList();
        }
        
        ProxyAccount account = getValidAccount(dto, conversation);
        if (account == null) {
            return CollUtil.newArrayList();
        }
        
        return queryAndConvertMessages(dto, conversation, account.getProxyId());
    }

    /**
     * 标记会话中的消息为已读
     * @param dto
     */
    @Override
    public void markMessagesAsRead(MailMarkReadDTO dto) {
        proxyChatMessageService.markMessageAsReadByConversationId(dto.getAccountId(), dto.getConversationId());
    }

    /**
     * 解析会话ID（如果通过mailId查询）
     *
     * @param dto 查询条件
     */
    private void resolveConversationId(MailDetailQueryDTO dto) {
        if (dto.getMailId() != null && dto.getConversationId() == null) {
            ProxyChatMessage message = proxyChatMessageService.getById(dto.getMailId());
            if (message != null) {
                dto.setConversationId(message.getConversationId());
            }
        }
    }

    /**
     * 获取有效的会话信息
     *
     * @param dto 查询条件
     * @return 会话信息
     */
    private ProxyConversation getValidConversation(MailDetailQueryDTO dto) {
        return proxyConversationService.getById(dto.getConversationId());
    }

    /**
     * 获取有效的账号信息
     *
     * @param dto 查询条件
     * @param conversation 会话信息
     * @return 账号信息
     */
    private ProxyAccount getValidAccount(MailDetailQueryDTO dto, ProxyConversation conversation) {
        if (dto.getMailAccountId() == null) {
            dto.setMailAccountId(conversation.getAccountId());
        }
        return proxyAccountConfigService.getById(dto.getMailAccountId());
    }

    /**
     * 查询并转换消息列表
     *
     * @param dto 查询条件
     * @param conversation 会话信息
     * @param accountProxyId 代理账户代理ID
     * @return 邮件详情列表
     */
    private List<EmailDetailVO> queryAndConvertMessages(MailDetailQueryDTO dto, ProxyConversation conversation, String accountProxyId) {
        List<ProxyChatMessage> messages = queryMessages(dto);
        
        if (CollUtil.isEmpty(messages)) {
            return CollUtil.newArrayList();
        }
        
        return StreamUtil.of(messages)
                .map(message -> convertToMailDetailVO(message, conversation, accountProxyId))
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 填充会话元数据信息（仅处理附件信息，未读消息数通过实时查询获取）
     *
     * @param vo VO对象
     * @param conversationMeta 会话元数据字符串
     */
    private void fillConversationMetaInfo(EmailConversationVO vo, String conversationMeta) {
        if (StringUtils.isBlank(conversationMeta)) {
            vo.setHasAttachment(false);
            return;
        }

        try {
            // 解析JSON字符串为Map
            Map<String, Object> meta = JSONUtil.toBean(conversationMeta, Map.class);
            if (meta == null || meta.isEmpty()) {
                vo.setHasAttachment(false);
                return;
            }

            // 设置是否有附件
            extractHasAttachment(meta).ifPresent(vo::setHasAttachment);
            if (vo.getHasAttachment() == null) {
                vo.setHasAttachment(false);
            }
        } catch (Exception e) {
            log.debug("解析会话元数据失败: {}", conversationMeta, e);
            vo.setHasAttachment(false);
        }
    }

    /**
     * 填充会话元数据信息（仅处理附件信息，未读消息数通过实时查询获取）
     *
     * @param vo VO对象
     * @param conversationMeta 会话元数据Map
     */
    private void fillConversationMetaInfo(EmailConversationVO vo, Map<String, Object> conversationMeta) {
        if (conversationMeta == null || conversationMeta.isEmpty()) {
            vo.setHasAttachment(false);
            return;
        }

        try {
            // 设置是否有附件
            extractHasAttachment(conversationMeta).ifPresent(vo::setHasAttachment);
            if (vo.getHasAttachment() == null) {
                vo.setHasAttachment(false);
            }
        } catch (Exception e) {
            log.debug("解析会话元数据失败: {}", conversationMeta, e);
            vo.setHasAttachment(false);
        }
    }

    /**
     * 批量获取未读消息数
     *
     * @param conversations 会话列表
     * @return 会话ID到未读消息数的映射
     */
    private List<UnreadCountDTO> batchGetUnreadCount(List<ProxyConversation> conversations) {
        if (CollUtil.isEmpty(conversations)) {
            return Collections.emptyList();
        }
        Set<Long> conversationIds = StreamTools.toSet(conversations, ProxyConversation::getId);
        return proxyChatMessageService.batchGetUnreadCountByConversationIds(conversationIds);
    }

    /**
     * 从元数据中提取是否有附件
     *
     * @param meta 元数据
     * @return 是否有附件
     */
    private Optional<Boolean> extractHasAttachment(Map<String, Object> meta) {
        Object hasAttachment = meta.get("hasAttachment");
        if (hasAttachment instanceof Boolean) {
            return Optional.of((Boolean) hasAttachment);
        }
        // 也可以检查 attachments 字段
        Object attachments = meta.get("attachments");
        if (attachments instanceof List) {
            List<?> attachmentList = (List<?>) attachments;
            return Optional.of(!attachmentList.isEmpty());
        }
        return Optional.empty();
    }

    /**
     * 将查询DTO转换为代理会话查询DTO
     * @param queryDTO 邮件会话查询DTO
     * @return 如果是关键字搜索且没有结果，返回null；否则返回查询DTO
     */
    private ProxyConversationQueryDTO convertToProxyQuery(MailConversationQueryDTO queryDTO) {
        ProxyConversationQueryDTO proxyDTO = new ProxyConversationQueryDTO();

        // 设置账号ID相关条件
        proxyDTO.setAccountId(queryDTO.getAccountId());

        // 设置时间范围查询条件
        proxyDTO.setLatestMessageTimeStart(queryDTO.getLatestMessageTimeStart());
        proxyDTO.setLatestMessageTimeEnd(queryDTO.getLatestMessageTimeEnd());

        // 处理关键字查询
//        Set<Long> keywordConversationIds = processKeywordQuery(queryDTO.getAccountId(), queryDTO.getKeyword());
//        if (CollUtil.isNotEmpty(keywordConversationIds)) {
//            proxyDTO.setIds(keywordConversationIds);
//        }

        // 设置分页参数
        proxyDTO.setPage(queryDTO.getPage());
        proxyDTO.setSize(queryDTO.getSize());

        // 根据最新消息时间倒序排序
        proxyDTO.setIsOrderByLatestMessageTimeDesc(true);
        return proxyDTO;
    }


    /**
     * 处理关键字查询，返回匹配的会话ID集合
     *
     * @param keyword 关键字
     * @return 匹配的会话ID集合，null表示无关键字查询条件
     */
    private Set<Long> processKeywordQuery(Long accountId, String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return null;
        }
        return searchConversationIdsByKeyword(accountId, keyword);
    }

    /**
     * 根据关键字搜索消息内容，返回匹配的会话ID集合
     *
     * @param keyword 关键字
     * @return 匹配的会话ID集合
     */
    private Set<Long> searchConversationIdsByKeyword(Long accountId, String keyword) {
        try {
            // 构建消息查询条件
            ProxyChatMsgQuery query = new ProxyChatMsgQuery();
            query.setAccountId(accountId);
            query.setContentLike(keyword.trim());
            
            // 查询匹配的消息
            List<ProxyChatMessage> messages = proxyChatMessageService.listByCondition(query);

            // 提取会话ID，限制结果数量避免性能问题
            return messages.stream()
                    .map(ProxyChatMessage::getConversationId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("根据关键字搜索会话ID失败, keyword: {}", keyword, e);
            return Collections.emptySet();
        }
    }

    /**
     * 批量将会话列表转换为VO列表，并填充最新消息信息
     *
     * @param conversations 会话列表
     * @return VO列表
     */
    private List<EmailConversationVO> convertToVOList(List<ProxyConversation> conversations) {
        if (conversations == null || conversations.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 提取所有最新消息ID
        List<Long> latestMessageIds = conversations.stream()
                .map(ProxyConversation::getLatestMessageId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 2. 批量查询最新消息
        Map<Long, ProxyChatMessage> messageMap = buildMessageMap(latestMessageIds);

        // 3. 批量查询未读消息数
        List<UnreadCountDTO> unreadCountList = batchGetUnreadCount(conversations);

        // 4. 转换会话为VO并填充信息
        return conversations.stream()
                .map(conversation -> convertToVO(conversation, messageMap.get(conversation.getLatestMessageId()), unreadCountList))
                .collect(Collectors.toList());
    }

    /**
     * 构建消息ID到消息对象的映射
     *
     * @param messageIds 消息ID列表
     * @return 消息映射
     */
    private Map<Long, ProxyChatMessage> buildMessageMap(List<Long> messageIds) {
        if (messageIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<ProxyChatMessage> latestMessages = proxyChatMessageService.listByIds(messageIds);
        return latestMessages.stream()
                .collect(Collectors.toMap(ProxyChatMessage::getId, message -> message, (a, b) -> a));
    }

    /**
     * 将代理会话实体转换为VO对象，并填充最新消息信息
     *
     * @param conversation 会话实体
     * @param latestMessage 最新消息
     * @return VO对象
     */
    private EmailConversationVO convertToVO(ProxyConversation conversation, ProxyChatMessage latestMessage, List<UnreadCountDTO> unreadCountList) {
        // 使用MapStruct进行基础转换
        EmailConversationVO vo = EmailConversationMapper.INSTANCE.toMailConversationVO(conversation);

        // 转换conversationId为字符串
        vo.setConversationId(conversation.getId().toString());

        // 填充最新消息信息
        fillLatestMessageInfo(vo, latestMessage);

        // 填充会话元数据信息（仅处理附件信息）
        fillConversationMetaInfo(vo, conversation.getConversationMeta());

        // 设置实时查询的未读消息数
        Long unreadCount = StreamUtil.of(unreadCountList)
                .filter(it -> Objects.equals(it.getId(), String.valueOf(conversation.getId())))
                .map(UnreadCountDTO::getUnreadCount)
                .findFirst()
                .orElse(0L);

        vo.setUnreadCount(unreadCount.intValue());

        return vo;
    }

    /**
     * 填充最新消息信息
     *
     * @param vo VO对象
     * @param latestMessage 最新消息
     */
    private void fillLatestMessageInfo(EmailConversationVO vo, ProxyChatMessage latestMessage) {
        if (latestMessage == null) {
            return;
        }

        // 设置发件人信息（这里需要根据实际业务逻辑获取发件人名称和邮箱）
        vo.setSenderName(latestMessage.getChatUserName());
        vo.setSenderEmail(latestMessage.getChatUserProxyId());

        // 使用工具类设置预览内容
        String preview = EmailContentUtils.extractPreviewContent(latestMessage.getContent());
        vo.setPreview(preview);
    }

    /**
     * 查询邮件消息
     */
    private List<ProxyChatMessage> queryMessages(MailDetailQueryDTO dto) {
        // 构建消息查询条件
        ProxyChatMsgQuery msgQuery = new ProxyChatMsgQuery();
        msgQuery.setConversationId(dto.getConversationId());
        msgQuery.setAccountId(dto.getMailAccountId());
        msgQuery.setOrderByIdAsc(true); // 按ID升序

        // 直接在数据库查询阶段过滤早于指定时间的消息
        if (dto.getFilterBeforeTime() != null) {
            msgQuery.setStartTime(dto.getFilterBeforeTime());
        }

        // 查询会话中的所有消息
        return proxyChatMessageService.listByCondition(msgQuery);
    }

    /**
     * 转换消息为邮件详情VO（带会话和用户邮箱信息）
     *
     * @param message 消息对象
     * @param conversation 会话对象
     * @param accountProxyId 代理账户代理ID
     * @return 邮件详情VO
     */
    private EmailDetailVO convertToMailDetailVO(ProxyChatMessage message, ProxyConversation conversation, String accountProxyId) {
        EmailDetailVO vo = new EmailDetailVO();
        
        setBasicInfo(vo, message, accountProxyId);
        setSenderAndRecipientInfo(vo, message);
        setMessageDirection(vo, message, accountProxyId);
        processContentAndAttachments(vo, message);
        
        return vo;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(EmailDetailVO vo, ProxyChatMessage message, String accountProxyId) {
        vo.setMailId(message.getId());
        vo.setConversationId(message.getConversationId());
        vo.setSendTime(message.getCreateTime());
        vo.setIsRead(Objects.equals(message.getReadStatus(), RequirementReadStatusEnum.READ.getValue()));
        vo.setExternalMessageId(message.getExternalMessageId());
        
        Boolean isAiReply = Objects.equals(message.getChatUserProxyId(), accountProxyId);
        vo.setIsAiReply(isAiReply);
    }

    /**
     * 设置发件人和收件人信息
     */
    private void setSenderAndRecipientInfo(EmailDetailVO vo, ProxyChatMessage message) {
        // 设置发件人信息
        vo.setSenderName(message.getChatUserName());
        vo.setSenderEmail(message.getChatUserProxyId());

        // 设置收件人信息
        vo.setRecipientName(message.getToUserName());
        vo.setRecipientEmail(message.getToUserProxyId());
    }

    /**
     * 设置消息方向
     */
    private void setMessageDirection(EmailDetailVO vo, ProxyChatMessage message, String accountProxyId) {
        boolean isSent = StrUtil.equals(accountProxyId, message.getChatUserProxyId());
        vo.setIsSentByCurrentUser(isSent);
    }

    /**
     * 处理邮件内容和附件
     */
    private void processContentAndAttachments(EmailDetailVO vo, ProxyChatMessage message) {
        String content = message.getContent();
        if (StrUtil.isBlank(content)) {
            return;
        }
        try {
            ProxyEmailChatMessageContent emailContent = JSONUtil.toBean(content, ProxyEmailChatMessageContent.class);
            setEmailContent(vo, emailContent, content);
        } catch (Exception e) {
            log.warn("解析邮件内容失败: messageId={}, error={}", 
                message.getId(), e.getMessage());
            vo.setContent(content);
        }
     }

    /**
     * 设置邮件内容
     */
    private void setEmailContent(EmailDetailVO vo, ProxyEmailChatMessageContent emailContent, String messageRawContent) {
        vo.setSubject(emailContent.getSubject());
        vo.setContent(processHtmlContent(emailContent.getHtmlContent(), messageRawContent));

        // 处理附件
        processAttachments(vo, emailContent);
    }

    /**
     * 处理邮件附件
     */
    private void processAttachments(EmailDetailVO vo, ProxyEmailChatMessageContent emailContent) {
        // 处理普通附件
        processRegularAttachments(vo, emailContent.getAttachments());
        // 处理内联附件
        processInlineAttachments(vo, emailContent.getInlineAttachments());
    }

    /**
     * 处理普通附件
     */
    private void processRegularAttachments(EmailDetailVO vo, List<MailAttachment> attachments) {
        Optional.ofNullable(attachments)
                .filter(CollUtil::isNotEmpty)
                .ifPresent(atts -> vo.setAttachments(convertAttachmentsToVOs(atts)));
    }

    /**
     * 处理内联附件
     */
    private void processInlineAttachments(EmailDetailVO vo, List<MailAttachment> inlineAttachments) {
        Optional.ofNullable(inlineAttachments)
                .filter(CollUtil::isNotEmpty)
                .ifPresent(atts -> {
                    replaceHtmlCidReferences(vo, atts);
                    vo.setInlineAttachments(convertAttachmentsToVOs(atts));
                });
    }

    /**
     * 替换HTML内容中的CID引用
     */
    /**
     * 替换HTML内容中的CID引用为签名URL
     *
     * @param vo 邮件详情VO
     * @param inlineAttachments 内联附件列表
     */
    private void replaceHtmlCidReferences(EmailDetailVO vo, List<MailAttachment> inlineAttachments) {
        String htmlContent = vo.getContent();
        if (StrUtil.isBlank(htmlContent) || CollUtil.isEmpty(inlineAttachments)) {
            return;
        }
        
        String updatedContent = replaceCidWithSignedUrls(htmlContent, inlineAttachments);
        vo.setContent(updatedContent);
    }
    
    /**
     * 将HTML内容中的CID引用替换为签名URL
     *
     * @param htmlContent HTML内容
     * @param inlineAttachments 内联附件列表
     * @return 替换后的HTML内容
     */
    private String replaceCidWithSignedUrls(String htmlContent, List<MailAttachment> inlineAttachments) {
        return inlineAttachments.stream()
                .reduce(htmlContent, 
                       this::replaceSingleCidReference, 
                       (content1, content2) -> content1);
    }
    
    /**
     * 替换单个CID引用
     *
     * @param content HTML内容
     * @param attachment 附件信息
     * @return 替换后的内容
     */
    private String replaceSingleCidReference(String content, MailAttachment attachment) {
        String cidReference = "cid:" + attachment.getId();
        String signedUrl = CloudUploadUtil.generateSignedUrl(attachment.getId());
        return content.replace(cidReference, signedUrl);
    }

    /**
     * 转换附件为VO对象
     */
    private List<EmailAttachmentVO> convertAttachmentsToVOs(List<MailAttachment> attachments) {
        return attachments.stream()
                .map(this::convertToAttachmentVO)
                .collect(Collectors.toList());
    }

    /**
     * 处理HTML内容
     */
    private String processHtmlContent(String htmlContent, String messageRawContent) {
        if (StringUtils.isNotBlank(htmlContent)) {
            try {
                org.jsoup.nodes.Document doc = org.jsoup.Jsoup.parse(htmlContent);
                org.jsoup.nodes.Element body = doc.body();
                // 获取body的HTML内容
                htmlContent = body.outerHtml();
            } catch (Exception e) {
                log.warn("解析HTML内容失败，使用原始内容", e);
            }
        }
        return StringUtils.isBlank(htmlContent) ? messageRawContent : htmlContent;
    }

    /**
     * 转换单个附件
     */
    private EmailAttachmentVO convertToAttachmentVO(MailAttachment attachment) {
        EmailAttachmentVO vo = new EmailAttachmentVO();
        vo.setAttachmentId(attachment.getId());
        vo.setName(attachment.getName());
        vo.setSize(attachment.getSize());
        // 使用CloudUploadUtil生成带签名的URL
        String signedUrl = CloudUploadUtil.generateSignedUrl(attachment.getUrl());
        vo.setDownloadUrl(signedUrl);
        vo.setPreviewUrl(signedUrl);
        vo.setContentType(attachment.getContentType());
        return vo;
    }


}