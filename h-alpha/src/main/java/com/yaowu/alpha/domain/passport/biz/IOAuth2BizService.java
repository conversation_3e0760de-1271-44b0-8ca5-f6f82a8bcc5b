package com.yaowu.alpha.domain.passport.biz;

import com.yaowu.alpha.model.vo.passport.TokenVO;

import java.text.ParseException;

/**
 * <p>
 * Token服务
 * </p>
 *
 * <AUTHOR> 2022-11-22
 */

public interface IOAuth2BizService {

    /**
     * 生成token（密码）
     */
    TokenVO oauthTokenByPwd(Long userId, String username, String password);

    /**
     * 生成token（短信验证码）
     */
    TokenVO oauthTokenBySms(Long userId, String phone, String smsCode);

    /**
     * 生成token（邮箱验证码）
     */
    TokenVO oauthTokenByEmail(Long userId, String email, String captcha);

    /**
     * 刷新token
     */
    TokenVO refreshToken(String refreshToken);


}
