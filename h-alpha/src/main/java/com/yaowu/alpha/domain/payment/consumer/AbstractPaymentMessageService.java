package com.yaowu.alpha.domain.payment.consumer;

import com.yaowu.alpha.domain.mq.AbstractMessageConsumer;
import com.yaowu.alpha.utils.common.MqConstants;
import com.yaowu.settle.api.model.mtl.pojo.message.PaymentBusinessMqMessage;
import com.yaowu.settle.api.model.mtl.pojo.message.RemotePaymentNotificationResultMessage;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2025/4/7-19:33
 */
public abstract class AbstractPaymentMessageService extends AbstractMessageConsumer<RemotePaymentNotificationResultMessage> {

    @Override
    protected MqConstants.MqRoute route() {
        return MqConstants.THIRD_PARTY_PAYMENT_MESSAGE;
    }

    @Override
    protected final void processMessage(RemotePaymentNotificationResultMessage message) {
        if (support(message)) {
            innerProcessMessage(message);
        }
    }

    @Override
    protected Type classType() {
        return RemotePaymentNotificationResultMessage.class;
    }

    protected abstract boolean support(RemotePaymentNotificationResultMessage message);

    protected abstract void innerProcessMessage(RemotePaymentNotificationResultMessage message);
}
