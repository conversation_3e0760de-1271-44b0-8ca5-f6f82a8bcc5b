package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportNewRoomNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportNewRoomRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 上报群成员更新
 *
 * <AUTHOR>
 */
@Component
public class WetoolNewRoomAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportNewRoomRequestDTO, ReportNewRoomNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_NEW_ROOM.equals(action);
    }

    @Override
    public ReportNewRoomNoticeRequestDTO transferRequest(WetoolWexinReportNewRoomRequestDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toNewRoomRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
