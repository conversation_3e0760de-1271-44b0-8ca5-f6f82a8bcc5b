package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.util.StrUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.yaowu.alpha.domain.common.remote.ICustomerServiceRemoteBizService;
import com.yaowu.customerserviceapi.feign.customer.IRemoteCustomerPushFeign;
import com.yaowu.customerserviceapi.feign.customer.IRemoteCustomerServiceMerchantCustomerFeign;
import com.yaowu.customerserviceapi.feign.customer.IRemoteOnlineSaleCustomerInfoFeign;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteCustomerPushDTO;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteMerchantCustomerAuthorizerCheckDTO;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteOnlineSalesCustomerAddDTO;
import com.yaowu.customerserviceapi.model.vo.customer.RemoteCustomerOpVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/16 17:05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CustomerServiceRemoteBizServiceImpl implements ICustomerServiceRemoteBizService {

    private final IRemoteCustomerServiceMerchantCustomerFeign merchantCustomerFeign;

    private final IRemoteOnlineSaleCustomerInfoFeign onlineSaleCustomerInfoFeign;

    private final IRemoteCustomerPushFeign remoteCustomerPushFeign;

    @Override
    public Boolean pushCustomerMsgToWxBoot(RemoteCustomerPushDTO remoteDTO) {
        return FeignInvokeUtils.convert(remoteCustomerPushFeign.pushTmkBd(remoteDTO), Boolean.class);
    }

    @Override
    public Boolean checkAuthorizerNameIdNumber(RemoteMerchantCustomerAuthorizerCheckDTO remoteDTO) {
        if (StrUtil.isBlank(remoteDTO.getAuthorizerIdNumber())) {
            return true;
        }
        return FeignInvokeUtils.convert(merchantCustomerFeign.checkAuthorizerNameIdNumber(remoteDTO), Boolean.class);
    }

    @Override
    public RemoteCustomerOpVO addOnlineSalesCustomer(RemoteOnlineSalesCustomerAddDTO remoteDTO) {
        return FeignInvokeUtils.convert(onlineSaleCustomerInfoFeign.add(remoteDTO), RemoteCustomerOpVO.class);
    }
}
