package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriendExtension;

import java.util.List;

/**
 * <p>
 * 好友信息扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
public interface IProxyAccountFriendExtensionService extends IService<ProxyAccountFriendExtension> {

    /**
     * 根据好友ID获取并校验扩展信息
     * @param friendId 好友ID
     * @return 好友扩展信息
     */
    ProxyAccountFriendExtension getAndCheckByFriendId(Long friendId);

    /**
     * 根据好友ID获取扩展信息
     * @param friendId 好友ID
     * @return 好友扩展信息
     */
    ProxyAccountFriendExtension getByFriendId(Long friendId);

    /**
     * 根据好友ID列表获取扩展信息
     * @param friendIds 好友ID列表
     * @return 好友扩展信息列表
     */
    List<ProxyAccountFriendExtension> listByFriendIds(List<Long> friendIds);
} 