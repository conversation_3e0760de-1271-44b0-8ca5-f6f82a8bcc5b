package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyInteractionReferenceMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyInteractionSampleService;
import com.yaowu.alpha.enums.proxy.ProxyInteractionSampleStatusEnum;
import com.yaowu.alpha.model.entity.proxy.ProxyInteractionSample;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 互动对照数据集
 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Service
public class ProxyInteractionSampleServiceImpl extends ServiceImpl<ProxyInteractionReferenceMapper, ProxyInteractionSample> implements IProxyInteractionSampleService {

    /**
     * 获取启用对照数据集列表
     * @return
     */
    @Override
    public List<ProxyInteractionSample> listEnableInteractionSample() {
        return this.lambdaQuery()
                .eq(ProxyInteractionSample::getDataStatus, ProxyInteractionSampleStatusEnum.ENABLE)
                .list();
    }

}
