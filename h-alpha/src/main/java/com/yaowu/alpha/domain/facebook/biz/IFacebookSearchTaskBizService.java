package com.yaowu.alpha.domain.facebook.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.facebook.*;
import com.yaowu.alpha.model.vo.facebook.*;

/**
 * Facebook搜索任务业务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IFacebookSearchTaskBizService {

    /**
     * 创建Facebook搜索任务
     *
     * @param createDTO 创建任务DTO
     * @return 任务ID
     */
    Long createTask(FacebookSearchTaskCreateDTO createDTO);

    Boolean delTask(FacebookSearchTaskDelDTO dto);

    BasePage<FacebookKeywordTaskPageVO> taskPage(FacebookSearchTaskPageQueryDTO dto);

    BasePage<FacebookKeywordTaskGroupPageVO> groupPage(FacebookSearchTaskGroupPageQueryDTO dto);

    BasePage<FacebookKeywordTaskMemberPageVO> memberPage(FacebookSearchTaskMemberPageQueryDTO dto);

    // ==================== Python项目调用接口 ====================

    /**
     * 获取关键字搜索任务
     * 用于根据关键字搜索对应的群组
     *
     * @return 关键字搜索任务
     */
    FacebookKeywordTaskVO getKeywordTask();

    /**
     * 获取群组成员爬取任务
     * 用于爬取群组成员信息
     *
     * @return 群组成员爬取任务
     */
    FacebookGroupCrawlTaskVO getGroupCrawlTask();

    /**
     * 群组信息上报
     * 用于保存python项目爬取的群组信息
     *
     * @param reportDTO 群组信息上报DTO
     */
    void reportGroups(FacebookGroupReportDTO reportDTO);

    /**
     * 群成员信息上报
     * 用于保存python项目爬取的群成员信息
     *
     * @param reportDTO 群成员信息上报DTO
     */
    void reportMembers(FacebookMemberReportDTO reportDTO);

    /**
     * 更新关键字任务状态
     * 用于更新该关键字爬取群组数据达到了预期值或已经没有更多的群组信息了
     *
     * @param updateDTO 关键字状态更新DTO
     */
    void updateKeywordStatus(FacebookKeywordStatusUpdateDTO updateDTO);

    /**
     * 更新群组状态
     * 用于更新群组爬取状态，判断群组成员已经达到预期的群成员数或已经没有更多的成员
     * 注意群组状态最终要影响FacebookSearchTask表的状态，当所有群组的状态都完时FacebookSearchTask的状态变为执行成功
     *
     * @param updateDTO 群组状态更新DTO
     */
    void updateGroupStatus(FacebookGroupStatusUpdateDTO updateDTO);

    /**
     * 获取成员联系方式分析任务
     *
     * @return
     */
    FacebookGroupMemberContactAnalysisTaskVO getMemberContactAnalysisTask();

    /**
     * 更新联系方式分析任务结果
     *
     * @param dto
     * @return
     */
    Boolean reportContactAnalysisTaskResult(FacebookMemberContactAnalysisTaskResultReportDTO dto);

} 