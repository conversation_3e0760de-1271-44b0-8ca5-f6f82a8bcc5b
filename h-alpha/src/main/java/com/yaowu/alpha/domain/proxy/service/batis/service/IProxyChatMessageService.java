package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.bo.proxy.ProxyChatMessagePageDTO;
import com.yaowu.alpha.model.bo.proxy.ProxyChatMsgQuery;
import com.yaowu.alpha.model.dto.proxy.ChatMessageCursorQueryDTO;
import com.yaowu.alpha.model.dto.proxy.UnreadCountDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 智能体聊天信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyChatMessageService extends IService<ProxyChatMessage> {

    /**
     * 根据文件索引获取聊天信息
     *
     * @param fileIndex 文件索引
     */
    ProxyChatMessage getByFileIndex(String fileIndex);

    /**
     * 根据代理会话ID获取聊天信息
     */
    List<ProxyChatMessage> listRecentlyByAgentSessionId(Long agentSessionId, String chatUserProxyId, Long limit);

    /**
     * 通过查询条件获取数量
     */
    int countByCondition(ProxyChatMsgQuery query);

    /**
     * 更新被识破标识
     */
    boolean updateRumbled(Long id, Integer rumbled);

    /**
     * 分页查询
     * @param pageDTO 分页查询参数
     * @return 分页结果
     */
    Page<ProxyChatMessage> pageByCondition(ProxyChatMessagePageDTO pageDTO);

    /**
     * 批量查询好友的最后一条消息
     * @param friends 好友列表
     * @return 每个好友的最后一条消息
     */
    List<ProxyChatMessage> listLastMessagesByFriends(List<ProxyAccountFriend> friends);

    /**
     * 批量标记消息为已读
     */
    Boolean markMessagesAsRead(Long accountId, String friendProxyId);

    /**
     * 标记会话消息为已读
     * @param accountId
     * @param conversationId
     * @return
     */
    Boolean markMessageAsReadByConversationId (Long accountId, Long conversationId);

    List<UnreadCountDTO> batchGetUnreadCount(Long accountId, List<String> friendProxyIds);

    /**
     * 批量获取邮件会话未读消息数量
     *
     * @param conversationIds 会话ID列表
     * @return 未读消息数量列表
     */
    List<UnreadCountDTO> batchGetUnreadCountByConversationIds(Set<Long> conversationIds);
    
    /**
     * 基于游标的分页查询，避免消息断层问题
     * 
     * @param dto 查询条件，包含lastMessageId作为游标
     * @return 分页结果
     */
    List<ProxyChatMessage> listByCondition(ChatMessageCursorQueryDTO dto);



    /**
     * 统计比指定ID更新的消息数量
     *
     * @param accountId 代理账号ID
     * @param friendProxyId 好友代理ID
     * @param messageId 消息ID基准点
     * @return 新消息数量
     */
    Long countNewerMessages(Long accountId, String friendProxyId, Long messageId);

    List<ProxyChatMessage> listByCondition(ProxyChatMsgQuery proxyChatMsgQuery);

    ProxyChatMessage getByOriginMessageId(String msgId);

    List<ProxyChatMessage> listWhatsAppUnReadMessage(Long accountId, String sessionProxyId);

    /**
     * 查询最新一条聊天记录
     */
    ProxyChatMessage getLastChatMessage(Long accountId, String sessionProxyId);
}
