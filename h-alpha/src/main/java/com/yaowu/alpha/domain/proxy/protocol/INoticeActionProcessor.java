package com.yaowu.alpha.domain.proxy.protocol;


import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25 11:36
 */
public interface INoticeActionProcessor<T extends BaseNoticeRequestDTO, R extends NoticeBaseResponseVO> {

    boolean supports(BaseNoticeRequestDTO request);

    R process(T request);
}
