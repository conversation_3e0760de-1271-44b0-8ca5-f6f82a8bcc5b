package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.model.dto.common.CommonTranslateDTO;
import com.yaowu.alpha.model.vo.common.CommonTranslateVO;

/**
 * 通用翻译业务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface ICommonTranslationBizService {

    /**
     * 通用文本翻译
     * 支持根据国家名称或指定语言类型进行翻译
     *
     * @param dto 翻译请求参数
     * @return 翻译结果
     */
    CommonTranslateVO translateText(CommonTranslateDTO dto);
} 