package com.yaowu.alpha.domain.passport.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.passport.service.batis.mapper.UserMapper;
import com.yaowu.alpha.domain.passport.service.batis.service.IUserService;
import com.yaowu.alpha.model.entity.passport.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    private static final PasswordEncoder PASSWORD_ENCODER = PasswordEncoderFactories.createDelegatingPasswordEncoder();

    @Override
    public User findByUsername(String username) {
        List<User> list = this.lambdaQuery().eq(User::getUsername, username).list();
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public User findByPhone(String crownCode, String phone) {
        List<User> list = this.lambdaQuery()
                .eq(User::getCrownCode, crownCode)
                .eq(User::getPhone, phone).list();
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public User findByEmail(String email) {
        List<User> list = this.lambdaQuery().eq(User::getEmail, email).list();
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }
}
