package com.yaowu.alpha.domain.statistics.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yaowu.alpha.domain.statistics.service.IStatisticsCommonService;
import com.yaowu.alpha.domain.statistics.service.batis.mapper.StatisticsCommonMapper;
import com.yaowu.alpha.model.dto.statistics.StatisticsCommonDTO;
import com.yaowu.alpha.model.vo.statistics.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 统计通用服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsCommonServiceImpl implements IStatisticsCommonService {

    private final StatisticsCommonMapper statisticsCommonMapper;

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final static DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public EmailStatisticsVO getEmailStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询邮件统计信息，查询条件：{}", queryDTO);
        EmailStatisticsVO result = statisticsCommonMapper.selectEmailStatistics(queryDTO);
        log.info("邮件统计查询完成，结果{}", result);
        return result;
    }

    @Override
    public List<DailyEmailStatisticsVO> getDailyEmailStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询每日邮件统计信息，查询条件：{}", queryDTO);
        List<DailyEmailStatisticsVO> result = statisticsCommonMapper.selectDailyEmailStatistics(queryDTO);
        log.info("每日邮件统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<ChatStatisticsVO> getChatStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询渠道对话统计信息，查询条件：{}", queryDTO);
        List<ChatStatisticsVO> result = statisticsCommonMapper.selectChatStatistics(queryDTO);
        log.info("渠道对话统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<ChatStatisticsVO> getChatReplyStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询渠道对话统计信息（包含proxy_id条件），查询条件：{}", queryDTO);
        List<ChatStatisticsVO> result = statisticsCommonMapper.selectChatReplyStatistics(queryDTO);
        log.info("渠道对话统计查询（包含proxy_id条件）完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<DailyChatStatisticsVO> getDailyChatStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询每日对话客户数统计信息，查询条件：{}", queryDTO);
        List<DailyChatStatisticsVO> result = statisticsCommonMapper.selectDailyChatStatistics(queryDTO);
        log.info("每日对话客户数统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<DailyChatStatisticsVO> getDailyChatReplyStatistics(StatisticsCommonDTO queryDTO) {
        log.info("查询每日回复客户数统计信息，查询条件：{}", queryDTO);
        List<DailyChatStatisticsVO> result = statisticsCommonMapper.selectDailyChatReplyStatistics(queryDTO);
        log.info("每日回复客户数统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<GreetingTaskStatisticsVO> getGreetingTaskStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询打招呼各渠道任务统计信息，查询条件：{}", queryDTO);
        List<GreetingTaskStatisticsVO> result = statisticsCommonMapper.selectGreetingTaskStatistics(queryDTO);
        log.info("打招呼各渠道任务统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<DailyGreetingTaskStatisticsVO> getDailyGreetingTaskStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询每日打招呼任务执行情况统计信息，查询条件：{}", queryDTO);
        List<DailyGreetingTaskStatisticsVO> result = statisticsCommonMapper.selectDailyGreetingTaskStatistics(queryDTO);
        log.info("每日打招呼任务执行情况统计查询完成，共查询到{}条统计记录", result.size());
        return result;
    }

    @Override
    public List<CustomerNurtureFlowStatisticsVO> getCustomerNurtureFlowStatistics(StatisticsCommonDTO queryDTO) {
        handleQueryDTO(queryDTO);
        log.info("查询客户培育流程统计信息，查询条件：{}", queryDTO);
        List<CustomerNurtureFlowStatisticsVO> result = statisticsCommonMapper.selectCustomerNurtureFlowStatistics(queryDTO);
        log.info("客户培育流程统计查询完成，结果：{}", result);
        return result;
    }

    private void handleQueryDTO(StatisticsCommonDTO dto) {
        if (dto == null) {
            return;
        }
        String startTime = dto.getStartTime();
        if (StrUtil.isNotBlank(startTime)) {
            LocalDate date = LocalDate.parse(startTime, DATE_FORMATTER);
            dto.setStartTime(LocalDateTime.of(date, LocalTime.MIN).format(DATE_TIME_FORMATTER));
        }
        String endTime = dto.getEndTime();
        if (StrUtil.isNotBlank(endTime)) {
            LocalDate date = LocalDate.parse(endTime, DATE_FORMATTER);
            dto.setEndTime(LocalDateTime.of(date, LocalTime.MAX).format(DATE_TIME_FORMATTER));
        }
    }
}
