package com.yaowu.alpha.domain.ycloud.hook.whatapp;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.proxy.biz.IYCloudWhatsAppFacadeBizService;
import com.yaowu.alpha.domain.ycloud.hook.AbstractYCloudWeHookEventHandler;
import com.yaowu.alpha.model.dto.webhook.whatsapp.WhatsAppInboundMessage;
import com.yaowu.alpha.model.dto.webhook.whatsapp.WhatsAppInboundMessageEvent;
import com.yaowu.alpha.utils.contants.YCloudEndpointEventConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * WhatsApp入站消息接收事件处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/15
 */
@Component
@Slf4j
public class WhatAppInBoundMessageReceivedEventHandler extends AbstractYCloudWeHookEventHandler<WhatsAppInboundMessageEvent> {

    @Resource
    private IYCloudWhatsAppFacadeBizService yCloudWhatsAppFacadeBizService;

    @Override
    public boolean support(String eventType) {
        return Objects.equals(eventType, YCloudEndpointEventConstant.WHATSAPP_INBOUND_MESSAGE_RECEIVED);
    }

    @Override
    protected void handleEvent(WhatsAppInboundMessageEvent event) {
        if (event == null) {
            log.warn("WhatsApp inbound message event is null");
            return;
        }

        WhatsAppInboundMessage message = event.getWhatsappInboundMessage();
        if (message == null) {
            log.warn("WhatsApp inbound message is null in event: {}", event.getId());
            return;
        }

        logMessageInfo(message);
        processMessageByType(message);
    }
    
    /**
     * 记录消息信息
     *
     * @param message WhatsApp入站消息
     */
    private void logMessageInfo(WhatsAppInboundMessage message) {
        log.info("Received WhatsApp message - ID: {}, From: {}, To: {}, Type: {}, Time: {}", 
                message.getId(), message.getFrom(), message.getTo(), message.getType(), message.getSendTime());
        
        // 记录消息上下文信息（如果存在）
        Optional.ofNullable(message.getContext())
                .ifPresent(context -> log.info("Message context - From: {}, ID: {}", context.getFrom(), context.getId()));
        
        // 记录客户资料信息（如果存在）
        Optional.ofNullable(message.getCustomerProfile())
                .ifPresent(profile -> log.info("Customer profile - Name: {}", profile.getName()));
                
        // 记录完整消息内容（调试级别）
        log.debug("Complete message: {}", JSONUtil.toJsonStr(message));
    }
    
    /**
     * 根据消息类型处理消息
     *
     * @param message WhatsApp入站消息
     */
    private void processMessageByType(WhatsAppInboundMessage message) {
        try {
            switch (message.getType()) {
                case "text" -> handleTextMessage(message);
                case "button" -> handleButtonMessage(message);
                default -> log.warn("Unsupported WhatsApp message type: {}", message.getType());
            }
        } catch (Exception e) {
            log.error("Error handling WhatsApp message: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理文本消息
     *
     * @param message WhatsApp入站消息
     */
    private void handleTextMessage(WhatsAppInboundMessage message) {
        if (message.getText() == null) {
            log.warn("Text message has no content object");
            return;
        }
        
        String text = message.getText().getBody();
        if (StrUtil.isBlank(text)) {
            log.warn("Text message has empty content");
            return;
        }

        log.info("Processing text message: {}", text);
        String from = message.getFrom();
        String customerProfileName = Optional.ofNullable(message.getCustomerProfile())
                .map(WhatsAppInboundMessage.CustomerProfile::getName)
                .orElse("");
        String to = message.getTo();
        handlerReceivedMessage(from, to, customerProfileName, text);
    }

    /**
     * 处理按钮消息
     *
     * @param message WhatsApp入站消息
     */
    private void handleButtonMessage(WhatsAppInboundMessage message) {
        if (message.getButton() == null) {
            log.warn("Button message has no content object");
            return;
        }

        String payload = message.getButton().getPayload();
        String text = message.getButton().getText();
        
        if (StrUtil.isBlank(text) && StrUtil.isBlank(payload)) {
            log.warn("Button message has empty content");
            return;
        }

        log.info("Processing button message - Text: {}, Payload: {}", text, payload);
        String from = message.getFrom();
        String customerProfileName = Optional.ofNullable(message.getCustomerProfile())
                .map(WhatsAppInboundMessage.CustomerProfile::getName)
                .orElse("");
        String to = message.getTo();
        String reply = StrUtil.isBlank(text) ? payload : text;
        handlerReceivedMessage(from, to, customerProfileName, reply);
    }
    
    /**
     * 处理接收到的消息
     * @param from 发送方
     * @param to 接收方
     * @param customerName 客户名称
     * @param replyMessage 消息内容
     */
    private void handlerReceivedMessage(String from, String to, String customerName, String replyMessage) {
        yCloudWhatsAppFacadeBizService.processReceivedMessage(from, to, customerName, replyMessage);
    }
}
