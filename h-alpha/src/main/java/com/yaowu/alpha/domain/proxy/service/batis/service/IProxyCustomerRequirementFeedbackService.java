package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.yaowu.alpha.model.entity.proxy.ProxyCustomerReqFeedback;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 代理平台用户需求反馈表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
public interface IProxyCustomerRequirementFeedbackService extends IService<ProxyCustomerReqFeedback> {

    Map<Long, ProxyCustomerReqFeedback> getByRequirementIds(Set<Long> requirementIds);

    ProxyCustomerReqFeedback getByRequirementId(Long requirementId);

    Boolean removeByRequirementId(Long requirementId);
}
