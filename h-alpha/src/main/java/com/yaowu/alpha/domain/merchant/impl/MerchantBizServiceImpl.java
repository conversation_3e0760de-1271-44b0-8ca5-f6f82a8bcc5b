package com.yaowu.alpha.domain.merchant.impl;

import com.yaowu.alpha.domain.common.remote.IMelinaRemoteBizService;
import com.yaowu.alpha.domain.merchant.IMerchantBizService;
import com.yaowu.melinaapi.model.vo.merchant.RemoteMerchantInfoSimpleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class MerchantBizServiceImpl implements IMerchantBizService {

    private final IMelinaRemoteBizService melinaRemoteBizService;

    /**
     * 根据用户id获取商户id
     *
     * @param currentUserId
     * @return
     */
    @Override
    public Long getMerchantIdByUserId(Long currentUserId) {
        return null;
    }

    /**
     * 获取商户详情
     *
     * @param merchantId
     * @return
     */
    @Override
    public RemoteMerchantInfoSimpleVO getById(Long merchantId) {
        return melinaRemoteBizService.getMerchantById(merchantId);
    }
}
