package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.util.StrUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BaseResult;
import com.genlian.thirdparty.api.feign.dict.IThirdPartyDictFeign;
import com.genlian.thirdparty.api.feign.lbs.IRemoteLbsGeoFeign;
import com.genlian.thirdparty.api.model.dto.dict.ThirdPartyQueryDictDTO;
import com.genlian.thirdparty.api.model.dto.lbs.RemoteSearchAddressDTO;
import com.genlian.thirdparty.api.model.vo.dict.ThirdPartyDictListVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteAreaAddressModelVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteLbsAddressVO;
import com.yaowu.alpha.domain.common.remote.IThirdPartyRemoteBizService;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 地理位置-远程调用
 */
@Service
@Slf4j
public class ThirdPartyRemoteBizServiceImpl implements IThirdPartyRemoteBizService {

    @Resource
    private IRemoteLbsGeoFeign remoteLbsGeoFeign;

    @Resource
    private IThirdPartyDictFeign thirdPartyDictFeign;
    /**
     * 根据经纬度查询地址
     *
     * @param lng    经度
     * @param lat    纬度
     * @param radius 半径，非必传
     */
    @Override
    public RemoteLbsAddressVO searchAddress(String lng, String lat, Integer radius) {
        if (!StrUtil.isAllNotEmpty(lng, lat)) {
            return null;
        }
        final RemoteSearchAddressDTO searchAddressDTO = RemoteSearchAddressDTO.builder().lng(lng).lat(lat).radius(radius).build();
        final BaseResult<RemoteLbsAddressVO> baseResult = remoteLbsGeoFeign.searchAddress(searchAddressDTO);
        return FeignInvokeUtils.convert(baseResult, RemoteLbsAddressVO.class);
    }

    @Override
    public RemoteAreaAddressModelVO searchGeoAddressByAddressText(String address) {
        if (StringUtil.isBlank(address)) {
            return null;
        }
        return FeignInvokeUtils.convert(remoteLbsGeoFeign.searchGeoAddressByAddressText(address), RemoteAreaAddressModelVO.class);
    }

    @Override
    public Map<String, String> getSourceCodeToNameDictName(String dictType) {
        ThirdPartyQueryDictDTO dto = new ThirdPartyQueryDictDTO();
        dto.setDictType(dictType);
        List<ThirdPartyDictListVO> dictList = FeignInvokeUtils.convertList(thirdPartyDictFeign.getDictList(dto), ThirdPartyDictListVO.class);
        return StreamUtil.toMap(dictList, ThirdPartyDictListVO::getCode, ThirdPartyDictListVO::getName);
    }


    @Override
    public List<ThirdPartyDictListVO> getDictList(String dictType) {
        ThirdPartyQueryDictDTO dto = new ThirdPartyQueryDictDTO();
        dto.setDictType(dictType);
        BaseResult<List<ThirdPartyDictListVO>> dictList = thirdPartyDictFeign.getDictList(dto);
        return FeignInvokeUtils.convertList(dictList, ThirdPartyDictListVO.class);
    }

}