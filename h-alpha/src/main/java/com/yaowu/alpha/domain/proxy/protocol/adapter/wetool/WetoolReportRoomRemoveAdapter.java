package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportRoomRemoveNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportRoomRemoveDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 群减少
 */
@Component
public class WetoolReportRoomRemoveAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportRoomRemoveDTO, ReportRoomRemoveNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_ROOM_REMOVED.equals(action);
    }

    @Override
    public ReportRoomRemoveNoticeRequestDTO transferRequest(WetoolWexinReportRoomRemoveDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportRoomRemoveRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
