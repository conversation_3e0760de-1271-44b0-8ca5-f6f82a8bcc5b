package com.yaowu.alpha.domain.common.biz.impl;

import com.freedom.mybatisplus.service.ITenantIdValueService;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.config.exception.AlphaException;
import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.domain.common.biz.ITagBizService;
import com.yaowu.alpha.domain.common.service.batis.service.ITagInstanceService;
import com.yaowu.alpha.domain.common.service.batis.service.ITagService;
import com.yaowu.alpha.enums.common.OrgTypeEnum;
import com.yaowu.alpha.model.dto.common.TagBindingDTO;
import com.yaowu.alpha.model.dto.common.TagCreateDTO;
import com.yaowu.alpha.model.dto.common.TagQueryDTO;
import com.yaowu.alpha.model.entity.common.Tag;
import com.yaowu.alpha.model.entity.common.TagInstance;
import com.yaowu.alpha.model.vo.common.TagVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;


@RequiredArgsConstructor
@Slf4j
@Service
public class TagBizServiceImpl implements ITagBizService {

    private final ITagService tagService;
    private final ITagInstanceService tagInstanceService;
    private final ITenantIdValueService tenantIdValueService;
    private final CommonConfig commonConfig;

    /**
     * create tag
     * @param tagCreateDTO
     * @param tagBusinessCode
     * @return
     */
    @Override
    public Long createTag(TagCreateDTO tagCreateDTO, String tagBusinessCode) {
        Long tenantId = tenantIdValueService.getTenantId();
        OrgTypeEnum orgTypeEnum = commonConfig.getOrgTypeByTenantId(tenantId);
        if (OrgTypeEnum.INNER.equals(orgTypeEnum)) {
            throw new AlphaException("inner org can not create tag");
        }

        Tag tag = tagService.findTag(tenantId, tagBusinessCode, tagCreateDTO.getTagName());
        if (tag != null) {
            throw new AlphaException("tag existed");
        }
        Tag newTag = new Tag();
        newTag.setTenantId(tenantId);
        newTag.setBusinessCode(tagBusinessCode);
        newTag.setTagName(tagCreateDTO.getTagName());
        tagService.save(newTag);
        return newTag.getId();
    }

    /**
     * 绑定标签到实例
     * @param dto 标签绑定请求参数
     * @return 标签实例ID
     */
    @Override
    public void bindingTag(TagBindingDTO dto) {
        // 验证标签是否存在
        Tag tag = tagService.getById(dto.getTagId());
        if (tag == null) {
            throw new AlphaException("标签不存在");
        }
        // 检查实例是否已绑定该标签
        TagInstance existingInstance = tagInstanceService.findByTagIdAndInstanceId(dto.getTagId(), dto.getInstanceId());
        if (existingInstance != null) {
            log.info("该实例已绑定此标签");
            return;
        }
        // 创建标签实例关联
        TagInstance tagInstance = new TagInstance();
        tagInstance.setTagId(dto.getTagId());
        tagInstance.setInstanceId(dto.getInstanceId());
        
        // 保存标签实例关联
        tagInstanceService.save(tagInstance);
    }

    /**
     * 解绑标签与实例的关联
     * @param dto 标签绑定信息
     */
    @Override
    public void unbindTag(TagBindingDTO dto) {
        // 查找标签实例关联
        TagInstance existingInstance = tagInstanceService.findByTagIdAndInstanceId(dto.getTagId(), dto.getInstanceId());
        if (existingInstance == null) {
            log.info("该标签未绑定此实例");
            return;
        }
        
        // 删除标签实例关联
        tagInstanceService.removeById(existingInstance.getId());
    }

    /**
     * 查询标签列表
     * @param dto 查询参数
     * @return 标签列表
     */
    @Override
    public List<TagVO> listTags(TagQueryDTO dto) {
        Long businessTenantId = getQueryTagsBusinessTenantId(dto);
        // 设置当前租户ID（如果未指定）
        dto.setTenantId(businessTenantId);
        // 调用DAO层方法查询标签列表
        List<Tag> tagList = tagService.listByCondition(dto);
        
        // 转换为VO对象
        return convertToTagVOList(tagList);
    }

    /**
     * 分页查询标签列表
     * @param dto 查询参数
     * @return 分页标签列表
     */
    @Override
    public BasePage<TagVO> pageTags(TagQueryDTO dto) {
        Long businessTenantId = getQueryTagsBusinessTenantId(dto);
        // 设置当前租户ID（如果未指定）
        dto.setTenantId(businessTenantId);
        
        // 调用DAO层方法分页查询标签列表
        BasePage<Tag> tagPage = tagService.pageByCondition(dto);
        
        // 转换为VO对象
        List<TagVO> voList = convertToTagVOList(tagPage.getRecords());
        
        // 使用构造器创建新的分页对象返回
        return new BasePage<>(tagPage, voList);
    }

    /**
     * 查询标签绑定的实例ID列表
     * @param tagId 标签ID
     * @return 实例ID列表
     */
    @Override
    public List<String> listTagInstanceIds(Long tagId) {
        // 验证标签是否存在
        Tag tag = tagService.getById(tagId);
        if (tag == null) {
            return List.of();
        }
        
        // 调用DAO层方法查询实例ID列表
        return tagInstanceService.listInstanceIdsByTagId(tagId);
    }
    
    /**
     * 根据实例ID查询绑定的标签列表
     * @param instanceId 实例ID
     * @param businessCode 业务编码
     * @return 标签列表
     */
    @Override
    public List<TagVO> listTagsByInstanceId(String instanceId, String businessCode) {
        // 调用DAO层方法查询标签列表
        List<Tag> tagList = tagInstanceService.listTagsByInstanceId(instanceId, businessCode);
        
        // 转换为VO对象
        return convertToTagVOList(tagList);
    }
    
    /**
     * 删除标签
     * @param tagId 标签ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTag(Long tagId) {
        // 验证标签是否存在
        Tag tag = tagService.getById(tagId);
        if (tag == null) {
            log.warn("标签不存在，tagId: {}", tagId);
            return;
        }
        
        // 检查当前组织类型
        Long tenantId = tenantIdValueService.getTenantId();
        OrgTypeEnum orgTypeEnum = commonConfig.getOrgTypeByTenantId(tenantId);
        
        // 内部组织不能删除标签
        if (OrgTypeEnum.INNER.equals(orgTypeEnum)) {
            log.warn("inner org can not delete tag, tagId: {}, tenantId: {}", tagId, tenantId);
            throw new AlphaException("can not delete tag");
        }
        
        // 检查标签归属
        if (!Objects.equals(tag.getTenantId(), tenantId)) {
            log.warn("can not delete other tenant's tag, tagId: {}, tagTenantId: {}, currentTenantId: {}", 
                    tagId, tag.getTenantId(), tenantId);
            throw new AlphaException("no permission");
        }
        
        // 删除标签关联的所有实例
        boolean instancesDeleted = tagInstanceService.lambdaUpdate()
                .eq(TagInstance::getTagId, tagId)
                .remove();
        
        if (!instancesDeleted) {
            log.warn("删除标签实例关联失败，tagId: {}", tagId);
        }
        
        // 删除标签
        tagService.removeById(tagId);
    }

    private Long getQueryTagsBusinessTenantId(TagQueryDTO dto) {
        Long currentTenantId = tenantIdValueService.getTenantId();
        OrgTypeEnum orgTypeEnum = commonConfig.getOrgTypeByTenantId(currentTenantId);
        Long businessTenantId = currentTenantId;
        if (orgTypeEnum == OrgTypeEnum.INNER) {
            if (Objects.isNull(dto.getTenantId())) {
                throw new AlphaException("tenant id can not be null");
            }
            businessTenantId = dto.getTenantId();
        }
        return businessTenantId;
    }
    
    /**
     * 将Tag实体列表转换为TagVO列表
     * @param tagList 标签实体列表
     * @return 标签VO列表
     */
    private List<TagVO> convertToTagVOList(List<Tag> tagList) {
        return tagList.stream()
                .map(tag -> TagVO.builder()
                        .id(tag.getId())
                        .tenantId(tag.getTenantId())
                        .businessCode(tag.getBusinessCode())
                        .tagName(tag.getTagName())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 批量查询多个标签绑定的实例ID列表并取交集
     * @param tagIds 标签ID列表
     * @return 满足所有标签条件的实例ID列表
     */
    @Override
    public List<String> listTagInstanceIdsIntersection(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取有效的标签ID列表
        List<Long> existingTagIds = getExistingTagIds(tagIds);
        if (existingTagIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 如果只有一个标签，直接返回该标签的实例ID列表
        if (existingTagIds.size() == 1) {
            return tagInstanceService.listInstanceIdsByTagId(existingTagIds.get(0));
        }
        
        // 获取所有标签实例并按标签ID分组
        Map<Long, List<String>> tagInstanceMap = getTagInstanceMap(existingTagIds);
        
        // 计算交集
        return calculateIntersection(existingTagIds, tagInstanceMap);
    }
    
    /**
     * 获取存在的标签ID列表
     * @param tagIds 请求的标签ID列表
     * @return 存在的标签ID列表
     */
    private List<Long> getExistingTagIds(List<Long> tagIds) {
        // 验证标签是否存在并只获取存在的标签
        List<Tag> existingTags = tagService.listByIds(tagIds);
        if (existingTags.isEmpty()) {
            log.warn("没有找到任何请求的标签，请求的标签ID: {}", tagIds);
            return Collections.emptyList();
        }
        
        // 提取存在的标签ID，过滤掉null值
        List<Long> existingTagIds = existingTags.stream()
                .map(Tag::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        // 记录不存在的标签
        if (existingTagIds.size() < tagIds.size()) {
            List<Long> missingTagIds = tagIds.stream()
                    .filter(id -> !existingTagIds.contains(id))
                    .collect(Collectors.toList());
            log.warn("部分标签不存在，请求的标签ID: {}, 不存在的标签ID: {}", tagIds, missingTagIds);
        }
        
        return existingTagIds;
    }
    
    /**
     * 获取标签实例映射
     * @param tagIds 标签ID列表
     * @return 标签ID到实例ID列表的映射
     */
    private Map<Long, List<String>> getTagInstanceMap(List<Long> tagIds) {
        log.info("开始获取标签实例，标签ID列表: {}", tagIds);
        
        // 一次性获取所有标签关联的实例
        List<TagInstance> allTagInstances = tagInstanceService.listByTagIds(tagIds);
        
        if (allTagInstances.isEmpty()) {
            log.info("没有找到任何标签实例");
            return Collections.emptyMap();
        }
        
        // 按标签ID分组实例ID
        return allTagInstances.stream()
                .collect(Collectors.groupingBy(
                        TagInstance::getTagId,
                        Collectors.mapping(TagInstance::getInstanceId, Collectors.toList())
                ));
    }
    
    /**
     * 计算标签实例的交集
     * @param tagIds 标签ID列表
     * @param tagInstanceMap 标签ID到实例ID列表的映射
     * @return 交集结果
     */
    private List<String> calculateIntersection(List<Long> tagIds, Map<Long, List<String>> tagInstanceMap) {
        // 检查每个标签是否都有关联实例
        for (Long tagId : tagIds) {
            List<String> instanceIds = tagInstanceMap.getOrDefault(tagId, Collections.emptyList());
            log.info("标签 {} 关联了 {} 个实例", tagId, instanceIds.size());
            
            if (instanceIds.isEmpty()) {
                log.info("标签 {} 没有关联实例，交集为空", tagId);
                return Collections.emptyList();
            }
        }
        
        // 使用第一个标签的实例ID列表作为基准结果集
        Long firstTagId = tagIds.get(0);
        List<String> result = new ArrayList<>(tagInstanceMap.get(firstTagId));
        
        // 遍历剩余标签，逐个取交集
        for (int i = 1; i < tagIds.size(); i++) {
            Long tagId = tagIds.get(i);
            List<String> instanceIds = tagInstanceMap.get(tagId);
            
            // 计算交集
            result.retainAll(instanceIds);
            log.info("与标签 {} 取交集后，剩余 {} 个实例", tagId, result.size());
            
            // 如果交集已经为空，提前返回
            if (result.isEmpty()) {
                log.info("交集已为空");
                return Collections.emptyList();
            }
        }
        
        log.info("最终交集结果包含 {} 个实例", result.size());
        return result;
    }
}
