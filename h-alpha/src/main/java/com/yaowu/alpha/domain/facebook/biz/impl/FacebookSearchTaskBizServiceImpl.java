package com.yaowu.alpha.domain.facebook.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.facebook.biz.IFacebookSearchTaskBizService;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookGroupMemberService;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookGroupService;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookSearchTaskKeywordService;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookSearchTaskService;
import com.yaowu.alpha.enums.facebook.*;
import com.yaowu.alpha.model.dto.facebook.*;
import com.yaowu.alpha.model.entity.facebook.FacebookGroup;
import com.yaowu.alpha.model.entity.facebook.FacebookGroupMember;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTask;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTaskKeyword;
import com.yaowu.alpha.model.vo.facebook.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Facebook搜索任务业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class FacebookSearchTaskBizServiceImpl implements IFacebookSearchTaskBizService {

    @Autowired
    private FacebookSearchTaskService facebookSearchTaskService;

    @Autowired
    private FacebookSearchTaskKeywordService facebookSearchTaskKeywordService;

    @Autowired
    private FacebookGroupService facebookGroupService;

    @Autowired
    private FacebookGroupMemberService facebookGroupMemberService;

    @Override
    public Long createTask(FacebookSearchTaskCreateDTO createDTO) {
        // 创建主任务
        FacebookSearchTask task = new FacebookSearchTask();
        task.setTaskName(createDTO.getTaskName());
        task.setTaskType(FacebookTaskTypeEnum.GROUP_MEMBER_INFO);
        task.setTaskStatus(FacebookTaskStatusEnum.WAIT);
        task.setMaxGroupCount(createDTO.getMaxGroupCount());
        task.setMaxMemberCount(createDTO.getMaxMemberCount());

        facebookSearchTaskService.save(task);

        // 创建关键字任务
        List<FacebookSearchTaskKeyword> keywords = createDTO.getKeywords().stream()
                .map(keyword -> {
                    FacebookSearchTaskKeyword keywordTask = new FacebookSearchTaskKeyword();
                    keywordTask.setTaskId(task.getId());
                    keywordTask.setKeyword(keyword);
                    return keywordTask;
                })
                .collect(Collectors.toList());

        facebookSearchTaskKeywordService.saveBatch(keywords);

        return task.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean delTask(FacebookSearchTaskDelDTO dto) {
        FacebookSearchTask task = facebookSearchTaskService.getById(dto.getId());
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        if (task.getTaskStatus() != FacebookTaskStatusEnum.WAIT) {
            throw new BusinessException("当前状态下不允许删除");
        }
        facebookSearchTaskKeywordService.delByTaskId(task.getId());
        facebookSearchTaskService.removeById(task);
        return true;
    }

    @Override
    public BasePage<FacebookKeywordTaskPageVO> taskPage(FacebookSearchTaskPageQueryDTO dto) {
        LambdaQueryChainWrapper<FacebookSearchTask> query = buildTaskQuery(dto);
        Page<FacebookSearchTask> page = query.page(dto.pageRequest());
        List<FacebookSearchTask> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new BasePage<>(page, new ArrayList<>());
        }
        List<FacebookKeywordTaskPageVO> voList = toTaskVoList(records);
        return new BasePage<>(page, voList);
    }

    @Override
    public BasePage<FacebookKeywordTaskGroupPageVO> groupPage(FacebookSearchTaskGroupPageQueryDTO dto) {

        LambdaQueryChainWrapper<FacebookGroup> query = buildGroupQuery(dto);
        Page<FacebookGroup> page = query.page(dto.pageRequest());
        List<FacebookGroup> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new BasePage<>(page, new ArrayList<>());
        }
        List<FacebookKeywordTaskGroupPageVO> voList = toGroupVoList(records);
        return new BasePage<>(page, voList);
    }

    @Override
    public BasePage<FacebookKeywordTaskMemberPageVO> memberPage(FacebookSearchTaskMemberPageQueryDTO dto) {
        LambdaQueryChainWrapper<FacebookGroupMember> query = buildGroupQuery(dto);
        Page<FacebookGroupMember> page = query.page(dto.pageRequest());
        List<FacebookGroupMember> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new BasePage<>(page, new ArrayList<>());
        }
        List<FacebookKeywordTaskMemberPageVO> voList = toMemberVoList(records);
        return new BasePage<>(page, voList);
    }

    // ==================== Python项目调用接口实现 ====================

    @Override
    public FacebookKeywordTaskVO getKeywordTask() {
        // 查询待搜索的关键字任务
        LambdaQueryWrapper<FacebookSearchTaskKeyword> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacebookSearchTaskKeyword::getSearchStatus, FacebookSearchStatusEnum.WAIT);
        wrapper.orderByAsc(FacebookSearchTaskKeyword::getCreateTime);
        wrapper.last(" limit 1");

        List<FacebookSearchTaskKeyword> keywords = facebookSearchTaskKeywordService.list(wrapper);
        if (CollUtil.isEmpty(keywords)) {
            return null;
        }
        FacebookSearchTaskKeyword keyword = keywords.get(0);
        FacebookKeywordTaskVO vo = new FacebookKeywordTaskVO();
        vo.setKeywordId(keyword.getId());
        vo.setTaskId(keyword.getTaskId());
        vo.setKeyword(keyword.getKeyword());

        // 获取任务的最大群组数量
        FacebookSearchTask task = facebookSearchTaskService.getById(keyword.getTaskId());
        if (task != null) {
            vo.setMaxGroupCount(task.getMaxGroupCount());
        }
        return vo;
    }

    @Override
    public FacebookGroupCrawlTaskVO getGroupCrawlTask() {
        // 查询待爬取的群组任务
        LambdaQueryWrapper<FacebookGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacebookGroup::getGroupStatus, FacebookGroupStatusEnum.WAIT);
        wrapper.orderByAsc(FacebookGroup::getCreateTime);
        wrapper.last(" limit 1");

        List<FacebookGroup> groups = facebookGroupService.list(wrapper);
        if (CollUtil.isEmpty(groups)) {
            return null;
        }
        FacebookGroup group = groups.get(0);
        FacebookGroupCrawlTaskVO vo = new FacebookGroupCrawlTaskVO();
        vo.setGroupId(group.getId());
        vo.setTaskId(group.getTaskId());
        vo.setKeywordId(group.getKeywordId());
        vo.setFacebookGroupId(group.getGroupId());
        vo.setGroupName(group.getGroupName());
        vo.setGroupLink(group.getGroupLink());

        // 获取任务的最大成员数量
        FacebookSearchTask task = facebookSearchTaskService.getById(group.getTaskId());
        if (task != null) {
            vo.setMaxMemberCount(task.getMaxMemberCount());
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reportGroups(FacebookGroupReportDTO reportDTO) {
        // 获取关键字信息
        FacebookSearchTaskKeyword keyword = facebookSearchTaskKeywordService.getById(reportDTO.getKeywordId());
        if (keyword == null) {
            throw new RuntimeException("facebook关键字不存在");
        }

        // 幂等性处理：先查出所有上报的groupId集合
        List<String> groupIdList = reportDTO.getGroups().stream()
                .map(FacebookGroupReportDTO.FacebookGroupInfoDTO::getFacebookGroupId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (groupIdList.isEmpty()) {
            log.info("facebook群组信息上报为空，关键字ID: {}", reportDTO.getKeywordId());
            return;
        }
        // 查询已存在的groupId对应的群组
        List<FacebookGroup> existGroups = facebookGroupService.lambdaQuery()
                .eq(FacebookGroup::getTaskId, keyword.getTaskId())
                .eq(FacebookGroup::getKeywordId, reportDTO.getKeywordId())
                .in(FacebookGroup::getGroupId, groupIdList)
                .list();
        Map<String, FacebookGroup> existGroupMap = existGroups.stream()
                .collect(Collectors.toMap(FacebookGroup::getGroupId, g -> g));

        List<FacebookGroup> toSave = new ArrayList<>();
        List<FacebookGroup> toUpdate = new ArrayList<>();

        for (FacebookGroupReportDTO.FacebookGroupInfoDTO groupInfo : reportDTO.getGroups()) {
            String groupId = groupInfo.getFacebookGroupId();
            if (existGroupMap.containsKey(groupId)) {
                // 已存在则更新
                FacebookGroup exist = existGroupMap.get(groupId);
                boolean needUpdate = false;
                if (!Objects.equals(exist.getGroupName(), groupInfo.getGroupName())) {
                    exist.setGroupName(groupInfo.getGroupName());
                    needUpdate = true;
                }
                if (!Objects.equals(exist.getGroupLink(), groupInfo.getGroupLink())) {
                    exist.setGroupLink(groupInfo.getGroupLink());
                    needUpdate = true;
                }
                // 只在状态为FAILURE时重置为WAIT，避免已完成的被重置
                if (exist.getGroupStatus() == FacebookGroupStatusEnum.FAILURE) {
                    exist.setGroupStatus(FacebookGroupStatusEnum.WAIT);
                    needUpdate = true;
                }
                if (needUpdate) {
                    toUpdate.add(exist);
                }
            } else {
                // 不存在则新增
                FacebookGroup group = new FacebookGroup();
                group.setTaskId(keyword.getTaskId());
                group.setKeywordId(reportDTO.getKeywordId());
                group.setGroupStatus(FacebookGroupStatusEnum.WAIT);
                group.setGroupId(groupInfo.getFacebookGroupId());
                group.setGroupName(groupInfo.getGroupName());
                group.setGroupLink(groupInfo.getGroupLink());
                toSave.add(group);
            }
        }

        if (!toSave.isEmpty()) {
            facebookGroupService.saveBatch(toSave);
        }
        if (!toUpdate.isEmpty()) {
            facebookGroupService.updateBatchById(toUpdate);
        }

        log.info("facebook群组信息上报成功，关键字ID: {}, 上报群组数量: {}, 新增群组数量: {}, 更新群组数量: {}",
                reportDTO.getKeywordId(), groupIdList.size(), toSave.size(), toUpdate.size());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reportMembers(FacebookMemberReportDTO reportDTO) {
        // 获取群组信息
        FacebookGroup group = facebookGroupService.getById(reportDTO.getGroupId());
        if (group == null) {
            throw new RuntimeException("群组不存在");
        }

        // 幂等性处理：先查出所有上报的memberId集合
        List<String> memberIdList = reportDTO.getMembers().stream()
                .map(FacebookMemberReportDTO.FacebookMemberInfoDTO::getFacebookMemberId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (memberIdList.isEmpty()) {
            log.info("facebook群成员信息上报为空，群组ID: {}", reportDTO.getGroupId());
            return;
        }
        List<FacebookGroupMember> existMembers = facebookGroupMemberService.lambdaQuery()
                .eq(FacebookGroupMember::getTaskId, group.getTaskId())
                .eq(FacebookGroupMember::getKeywordId, group.getKeywordId())
                .eq(FacebookGroupMember::getGroupId, group.getGroupId())
                .in(FacebookGroupMember::getMemberId, memberIdList)
                .list();
        Map<String, FacebookGroupMember> existMemberMap = existMembers.stream()
                .collect(Collectors.toMap(FacebookGroupMember::getMemberId, m -> m));

        List<FacebookGroupMember> toSave = new ArrayList<>();
        List<FacebookGroupMember> toUpdate = new ArrayList<>();

        for (FacebookMemberReportDTO.FacebookMemberInfoDTO memberInfo : reportDTO.getMembers()) {
            String memberId = memberInfo.getFacebookMemberId();
            if (existMemberMap.containsKey(memberId)) {
                // 已存在则更新
                FacebookGroupMember exist = existMemberMap.get(memberId);
                boolean needUpdate = false;
                if (!Objects.equals(exist.getMemberName(), memberInfo.getMemberName())) {
                    exist.setMemberName(memberInfo.getMemberName());
                    needUpdate = true;
                }
                if (!Objects.equals(exist.getMemberLink(), memberInfo.getMemberLink())) {
                    exist.setMemberLink(memberInfo.getMemberLink());
                    needUpdate = true;
                }
                if (!Objects.equals(exist.getHasContactInfo(), memberInfo.getHasContactInfo())) {
                    exist.setHasContactInfo(memberInfo.getHasContactInfo());
                    needUpdate = true;
                }
                if (!Objects.equals(exist.getContactInfo(), memberInfo.getContactInfo())) {
                    exist.setContactInfo(memberInfo.getContactInfo());
                    needUpdate = true;
                }
                if (needUpdate) {
                    toUpdate.add(exist);
                }
            } else {
                // 不存在则新增
                FacebookGroupMember member = buildFacebookGroupMember(memberInfo, group);
                toSave.add(member);
            }
        }

        if (!toSave.isEmpty()) {
            facebookGroupMemberService.saveBatch(toSave);
        }
        if (!toUpdate.isEmpty()) {
            facebookGroupMemberService.updateBatchById(toUpdate);
        }

        log.info("群成员信息上报成功，群组ID: {}, 上报成员数量: {}, 新增成员数量: {}, 更新成员数量: {}",
                reportDTO.getGroupId(), memberIdList.size(), toSave.size(), toUpdate.size());
    }


    private FacebookGroupMember buildFacebookGroupMember(FacebookMemberReportDTO.FacebookMemberInfoDTO memberInfo, FacebookGroup group) {
        FacebookGroupMember member = new FacebookGroupMember();
        member.setTaskId(group.getTaskId());
        member.setKeywordId(group.getKeywordId());
        member.setGroupId(group.getId());
        member.setMemberId(memberInfo.getFacebookMemberId());
        member.setMemberName(memberInfo.getMemberName());
        member.setMemberLink(memberInfo.getMemberLink());
        member.setHasContactInfo(memberInfo.getHasContactInfo());
        member.setContactInfo(memberInfo.getContactInfo());
        return member;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateKeywordStatus(FacebookKeywordStatusUpdateDTO updateDTO) {
        FacebookSearchTaskKeyword keyword = facebookSearchTaskKeywordService.getById(updateDTO.getKeywordId());
        if (keyword == null) {
            throw new RuntimeException("关键字不存在");
        }

        keyword.setSearchStatus(updateDTO.getSearchStatus());
        if (updateDTO.getSearchStatus() == FacebookSearchStatusEnum.SEARCHING) {
            keyword.setSearchStartTime(LocalDateTime.now());
        } else if (updateDTO.getSearchStatus() == FacebookSearchStatusEnum.SUCCESS ||
                updateDTO.getSearchStatus() == FacebookSearchStatusEnum.FAILURE) {
            keyword.setSearchEndTime(LocalDateTime.now());
        }

        if (StringUtils.hasText(updateDTO.getErrorMsg())) {
            keyword.setErrorMsg(updateDTO.getErrorMsg());
        }

        facebookSearchTaskKeywordService.updateById(keyword);

        // 更新任务状态
        FacebookSearchTask task = facebookSearchTaskService.getById(keyword.getTaskId());
        if (task != null && task.getTaskStatus() == FacebookTaskStatusEnum.WAIT) {
            task.setTaskStatus(FacebookTaskStatusEnum.EXECUTING);
            task.setExecuteStartTime(LocalDateTime.now());
            facebookSearchTaskService.updateById(task);
        }

        log.info("关键字状态更新成功，关键字ID: {}, 状态: {}", updateDTO.getKeywordId(), updateDTO.getSearchStatus());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroupStatus(FacebookGroupStatusUpdateDTO updateDTO) {
        FacebookGroup group = facebookGroupService.getById(updateDTO.getGroupId());
        if (group == null) {
            throw new RuntimeException("群组不存在");
        }

        group.setGroupStatus(updateDTO.getGroupStatus());
        if (StringUtils.hasText(updateDTO.getErrorMsg())) {
            group.setErrorMsg(updateDTO.getErrorMsg());
        }

        facebookGroupService.updateById(group);

        // 当该群组关联的关键字的所有群组状态都为终态时，更新关键字的状态
        checkAndUpdateKeywordStatus(group);


        // 检查是否所有群组都已完成，如果是则更新主任务状态
        checkAndUpdateTaskStatus(group.getTaskId());

        log.info("群组状态更新成功，群组ID: {}, 状态: {}", updateDTO.getGroupId(), updateDTO.getGroupStatus());
    }

    @Override
    public FacebookGroupMemberContactAnalysisTaskVO getMemberContactAnalysisTask() {
        List<FacebookGroupMember> list = facebookGroupMemberService.lambdaQuery()
                .eq(FacebookGroupMember::getContactAnalysisStatus, FacebookContactAnalysisStatusEnum.PENDING)
                .last("limit 1")
                .orderByAsc(FacebookGroupMember::getCreateTime).list();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        FacebookGroupMember facebookGroupMember = list.get(0);
        FacebookGroupMemberContactAnalysisTaskVO vo = new FacebookGroupMemberContactAnalysisTaskVO();
        vo.setId(facebookGroupMember.getId());
        vo.setMemberId(facebookGroupMember.getMemberId());
        vo.setMemberName(facebookGroupMember.getMemberName());
        vo.setMemberLink(facebookGroupMember.getMemberLink());
        vo.setContactAnalysisStatus(facebookGroupMember.getContactAnalysisStatus());
        return vo;
    }

    @Override
    public Boolean reportContactAnalysisTaskResult(FacebookMemberContactAnalysisTaskResultReportDTO dto) {
        FacebookGroupMember member = facebookGroupMemberService.getById(dto.getId());
        if (member == null) {
            return true;
        }
        member.setContactAnalysisStatus(dto.getContactAnalysisStatus());
        member.setContactAnalysisErrorMsg(dto.getErrorMsg());
        if (dto.getContactAnalysisStatus() == FacebookContactAnalysisStatusEnum.COMPLETED) {
            member.setHasContactInfo(dto.getHasContactInfo());
            member.setContactInfo(dto.getContactInfo());
        }
        return facebookGroupMemberService.updateById(member);
    }

    private LambdaQueryChainWrapper<FacebookSearchTask> buildTaskQuery(FacebookSearchTaskPageQueryDTO dto) {
        LambdaQueryChainWrapper<FacebookSearchTask> query = facebookSearchTaskService.lambdaQuery();
        if (StrUtil.isNotBlank(dto.getTaskName())) {
            query.like(FacebookSearchTask::getTaskName, dto.getTaskName());
        }
        if (dto.getTaskStatus() != null) {
            query.eq(FacebookSearchTask::getTaskStatus, dto.getTaskStatus());
        }
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<FacebookSearchTaskKeyword> keywords = facebookSearchTaskKeywordService.likeByKeyword(dto.getKeyword());
            if (CollUtil.isEmpty(keywords)) {
                query.eq(FacebookSearchTask::getId, -1L);
            } else {
                List<Long> taskIds = keywords.stream().map(FacebookSearchTaskKeyword::getTaskId).toList();
                query.in(FacebookSearchTask::getId, taskIds);
            }
        }
        query.orderByDesc(FacebookSearchTask::getCreateTime);
        return query;
    }

    private LambdaQueryChainWrapper<FacebookGroup> buildGroupQuery(FacebookSearchTaskGroupPageQueryDTO dto) {
        LambdaQueryChainWrapper<FacebookGroup> query = facebookGroupService.lambdaQuery();
        if (StrUtil.isNotBlank(dto.getTaskName())) {
            List<Long> taskIds = facebookSearchTaskService.listIdsByLikeTaskName(dto.getTaskName());
            if (CollUtil.isEmpty(taskIds)) {
                query.eq(FacebookGroup::getId, -1L);
            } else {
                query.in(FacebookGroup::getTaskId, taskIds);
            }
        }
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<Long> keywordIds = facebookSearchTaskKeywordService.listIdsByLikeByKeyword(dto.getKeyword());
            if (CollUtil.isEmpty(keywordIds)) {
                query.eq(FacebookGroup::getId, -1L);
            } else {
                query.in(FacebookGroup::getKeywordId, keywordIds);
            }
        }
        if (StrUtil.isNotBlank(dto.getGroupName())) {
            query.like(FacebookGroup::getGroupName, dto.getGroupName());
        }
        query.orderByDesc(FacebookGroup::getCreateTime);
        return query;
    }

    private LambdaQueryChainWrapper<FacebookGroupMember> buildGroupQuery(FacebookSearchTaskMemberPageQueryDTO dto) {
        LambdaQueryChainWrapper<FacebookGroupMember> query = facebookGroupMemberService.lambdaQuery();
        if (StrUtil.isNotBlank(dto.getTaskName())) {
            List<Long> taskIds = facebookSearchTaskService.listIdsByLikeTaskName(dto.getTaskName());
            if (CollUtil.isEmpty(taskIds)) {
                query.eq(FacebookGroupMember::getId, -1L);
            } else {
                query.in(FacebookGroupMember::getTaskId, taskIds);
            }
        }
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<Long> keywordIds = facebookSearchTaskKeywordService.listIdsByLikeByKeyword(dto.getKeyword());
            if (CollUtil.isEmpty(keywordIds)) {
                query.eq(FacebookGroupMember::getId, -1L);
            } else {
                query.in(FacebookGroupMember::getKeywordId, keywordIds);
            }
        }
        if (StrUtil.isNotBlank(dto.getGroupName())) {
            List<Long> groupIds = facebookGroupService.listIdsByLikeGroupName(dto.getGroupName());
            if (CollUtil.isEmpty(groupIds)) {
                query.eq(FacebookGroupMember::getId, -1L);
            } else {
                query.in(FacebookGroupMember::getGroupId, groupIds);
            }
        }
        if (StrUtil.isNotBlank(dto.getMemberName())) {
            query.like(FacebookGroupMember::getMemberName, dto.getMemberName());
        }
        if (dto.getHasContactInfo() != null) {
            query.eq(FacebookGroupMember::getHasContactInfo, dto.getHasContactInfo());
        }
        if (dto.getAnalysisStatus() != null) {
            query.eq(FacebookGroupMember::getContactAnalysisStatus, dto.getAnalysisStatus());
        }
        query.orderByDesc(FacebookGroupMember::getCreateTime);
        return query;
    }

    private List<FacebookKeywordTaskPageVO> toTaskVoList(List<FacebookSearchTask> records) {
        List<Long> taskIds = records.stream().map(FacebookSearchTask::getId).toList();
        Map<Long, Integer> groupStatisticMap = facebookGroupService.getGroupStatisticMap(taskIds);
        Map<Long, Integer> memberStatisticMap = facebookGroupMemberService.getMemberStatisticMapByTaskIds(taskIds);
        Map<Long, List<String>> keywordsMap = facebookSearchTaskKeywordService.getKeywordsMap(taskIds);
        List<FacebookKeywordTaskPageVO> voList = new ArrayList<>();
        for (FacebookSearchTask task : records) {
            FacebookKeywordTaskPageVO vo = new FacebookKeywordTaskPageVO();
            vo.setId(task.getId());
            vo.setTaskName(task.getTaskName());
            vo.setTaskStatus(task.getTaskStatus().getValue());
            vo.setTaskStatusDesc(task.getTaskStatus().getDesc());
            vo.setKeywords(keywordsMap.get(task.getId()));
            vo.setExecuteStartTime(task.getExecuteStartTime());
            vo.setExecuteEndTime(task.getExecuteEndTime());
            vo.setMaxGroupCount(task.getMaxGroupCount());
            vo.setMaxMemberCount(task.getMaxMemberCount());
            vo.setGroupCount(groupStatisticMap.get(task.getId()));
            vo.setMemberCount(memberStatisticMap.get(task.getId()));
            vo.setCreateTime(task.getCreateTime());
            vo.setCreator(task.getCreator());
            vo.setErrorMsg(task.getErrorMsg());
            voList.add(vo);
        }
        return voList;
    }

    private List<FacebookKeywordTaskGroupPageVO> toGroupVoList(List<FacebookGroup> records) {
        List<Long> ids = records.stream().map(FacebookGroup::getId).toList();
        List<Long> taskIds = records.stream().map(FacebookGroup::getTaskId).distinct().toList();
        List<Long> keywordIds = records.stream().map(FacebookGroup::getKeywordId).distinct().toList();
        Map<Long, FacebookSearchTask> taskMap = facebookSearchTaskService.getTaskMap(taskIds);
        Map<Long, Integer> memberStatisticMap = facebookGroupMemberService.getMemberStatisticMapByGroupIds(ids);
        Map<Long, FacebookSearchTaskKeyword> keywordMap = facebookSearchTaskKeywordService.getKeywordMap(keywordIds);
        List<FacebookKeywordTaskGroupPageVO> voList = new ArrayList<>();
        for (FacebookGroup group : records) {
            FacebookKeywordTaskGroupPageVO vo = new FacebookKeywordTaskGroupPageVO();
            vo.setId(group.getId());
            FacebookSearchTask task = taskMap.get(group.getTaskId());
            vo.setTaskName(task.getTaskName());
            vo.setKeyword(keywordMap.get(group.getKeywordId()) != null ? keywordMap.get(group.getKeywordId()).getKeyword() : null);
            vo.setGroupName(group.getGroupName());
            vo.setGroupLink(group.getGroupLink());
            vo.setMaxMemberCount(task.getMaxMemberCount());
            vo.setMemberCount(memberStatisticMap.getOrDefault(group.getId(), 0));
            vo.setCreateTime(group.getCreateTime());
            voList.add(vo);
        }
        return voList;
    }

    private List<FacebookKeywordTaskMemberPageVO> toMemberVoList(List<FacebookGroupMember> records) {
        List<Long> taskIds = records.stream().map(FacebookGroupMember::getTaskId).distinct().toList();
        List<Long> keywordIds = records.stream().map(FacebookGroupMember::getKeywordId).distinct().toList();
        List<Long> groupIds = records.stream().map(FacebookGroupMember::getGroupId).distinct().toList();
        Map<Long, FacebookGroup> groupMap = facebookGroupService.getGroupMap(groupIds);
        Map<Long, FacebookSearchTask> taskMap = facebookSearchTaskService.getTaskMap(taskIds);
        Map<Long, FacebookSearchTaskKeyword> keywordMap = facebookSearchTaskKeywordService.getKeywordMap(keywordIds);
        List<FacebookKeywordTaskMemberPageVO> voList = new ArrayList<>();
        for (FacebookGroupMember member : records) {
            FacebookKeywordTaskMemberPageVO vo = new FacebookKeywordTaskMemberPageVO();
            vo.setId(member.getId());
            vo.setTaskName(taskMap.get(member.getTaskId()) != null ? taskMap.get(member.getTaskId()).getTaskName() : null);
            vo.setKeyword(keywordMap.get(member.getKeywordId()) != null ? keywordMap.get(member.getKeywordId()).getKeyword() : null);
            FacebookGroup group = groupMap.get(member.getGroupId());
            vo.setGroupName(group.getGroupName());
            vo.setGroupLink(group.getGroupLink());
            vo.setMemberLink(member.getMemberLink());
            vo.setMemberName(member.getMemberName());
            vo.setAnalysisStatus(member.getContactAnalysisStatus().getCode());
            vo.setAnalysisStatusDesc(member.getContactAnalysisStatus().getDescription());
            vo.setHasContactInfo(member.getHasContactInfo());
            vo.setContactInfo(member.getContactInfo());
            vo.setCreateTime(member.getCreateTime());
            voList.add(vo);
        }
        return voList;
    }

    private void checkAndUpdateKeywordStatus(FacebookGroup group) {
        // 查询该关键字下所有群组
        LambdaQueryWrapper<FacebookGroup> groupWrapper = new LambdaQueryWrapper<>();
        groupWrapper.eq(FacebookGroup::getKeywordId, group.getKeywordId());
        List<FacebookGroup> keywordGroups = facebookGroupService.list(groupWrapper);

        // 判断所有群组是否都为终态（COMPLETED 或 FAILURE）
        boolean allGroupFinished = keywordGroups.stream().allMatch(g ->
                g.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED ||
                        g.getGroupStatus() == FacebookGroupStatusEnum.FAILURE);

        if (!allGroupFinished) {
            return;
        }
        // 获取关键字实体
        FacebookSearchTaskKeyword keyword = facebookSearchTaskKeywordService.getById(group.getKeywordId());

        // 判断是否所有群组都成功
        boolean allSuccess = keywordGroups.stream().allMatch(g ->
                g.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED);
        // 判断是否有失败
        boolean anyFailure = keywordGroups.stream().anyMatch(g ->
                g.getGroupStatus() == FacebookGroupStatusEnum.FAILURE);

        if (allSuccess) {
            keyword.setSearchStatus(FacebookSearchStatusEnum.SUCCESS);
            keyword.setSearchEndTime(LocalDateTime.now());
            keyword.setErrorMsg(null);
        } else if (anyFailure) {
            // 区分部分完成
            boolean anySuccess = keywordGroups.stream().anyMatch(g ->
                    g.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED);
            if (anySuccess) {
                keyword.setSearchStatus(FacebookSearchStatusEnum.PARTIAL_SUCCESS);
                keyword.setSearchEndTime(LocalDateTime.now());
            } else {
                keyword.setSearchStatus(FacebookSearchStatusEnum.FAILURE);
                keyword.setSearchEndTime(LocalDateTime.now());
            }
        }
        facebookSearchTaskKeywordService.updateById(keyword);
        log.info("关键字状态自动更新，关键字ID: {}, 状态: {}", group.getKeywordId(), keyword.getSearchStatus());
    }

    /**
     * 检查并更新任务状态
     * 当所有群组的状态都完成时，FacebookSearchTask的状态变为执行成功
     */
    private void checkAndUpdateTaskStatus(Long taskId) {
        // 查询该任务下的所有群组
        LambdaQueryWrapper<FacebookGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FacebookGroup::getTaskId, taskId);
        List<FacebookGroup> groups = facebookGroupService.list(wrapper);

        if (groups.isEmpty()) {
            return;
        }

        // 检查是否所有群组都已完成（爬取完成或爬取失败）
        boolean allCompleted = groups.stream().allMatch(group ->
                group.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED ||
                        group.getGroupStatus() == FacebookGroupStatusEnum.FAILURE);
        // 并且所有FacebookSearchTaskKeyword都为终态
        // 终态：搜索成功(2) 或 搜索失败(3)
        LambdaQueryWrapper<FacebookSearchTaskKeyword> keywordWrapper = new LambdaQueryWrapper<>();
        keywordWrapper.eq(FacebookSearchTaskKeyword::getTaskId, taskId);
        List<FacebookSearchTaskKeyword> keywords = facebookSearchTaskKeywordService.list(keywordWrapper);
        if (keywords.isEmpty()) {
            return;
        }
        boolean allKeywordFinished = keywords.stream().allMatch(keyword ->
                keyword.getSearchStatus() == FacebookSearchStatusEnum.SUCCESS ||
                        keyword.getSearchStatus() == FacebookSearchStatusEnum.FAILURE);

        if (allCompleted && allKeywordFinished) {
            // 更新主任务状态为执行成功
            FacebookSearchTask task = facebookSearchTaskService.getById(taskId);
            if (task != null && task.getTaskStatus() == FacebookTaskStatusEnum.EXECUTING) {
                // 优化任务状态，区分执行成功、执行失败、部分成功
                boolean allGroupSuccess = groups.stream().allMatch(group ->
                        group.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED);
                boolean allKeywordSuccess = keywords.stream().allMatch(keyword ->
                        keyword.getSearchStatus() == FacebookSearchStatusEnum.SUCCESS);

                boolean anyGroupFailure = groups.stream().anyMatch(group ->
                        group.getGroupStatus() == FacebookGroupStatusEnum.FAILURE);
                boolean anyKeywordFailure = keywords.stream().anyMatch(keyword ->
                        keyword.getSearchStatus() == FacebookSearchStatusEnum.FAILURE);

                if (allGroupSuccess && allKeywordSuccess) {
                    task.setTaskStatus(FacebookTaskStatusEnum.SUCCESS);
                } else if (anyGroupFailure || anyKeywordFailure) {
                    // 存在失败，判断是否部分成功
                    if (groups.stream().anyMatch(group -> group.getGroupStatus() == FacebookGroupStatusEnum.COMPLETED)
                            || keywords.stream().anyMatch(keyword -> keyword.getSearchStatus() == FacebookSearchStatusEnum.SUCCESS)) {
                        task.setTaskStatus(FacebookTaskStatusEnum.PARTIAL_SUCCESS);
                    } else {
                        task.setTaskStatus(FacebookTaskStatusEnum.FAILURE);
                    }
                } else {
                    // 理论上不会走到这里
                    task.setTaskStatus(FacebookTaskStatusEnum.FAILURE);
                }
                task.setTaskStatus(FacebookTaskStatusEnum.SUCCESS);
                task.setExecuteEndTime(LocalDateTime.now());
                facebookSearchTaskService.updateById(task);
                log.info("任务执行完成，任务ID: {}", taskId);
            }
        }
    }
}