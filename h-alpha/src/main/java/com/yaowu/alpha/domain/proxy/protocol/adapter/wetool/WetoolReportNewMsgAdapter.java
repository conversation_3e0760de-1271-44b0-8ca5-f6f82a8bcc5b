package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportNewMsgNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportNewMsgRequestDTO;
import com.yaowu.alpha.model.vo.proxy.ReportNewMsgAckVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinReportNewMsgAckResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinReportNewMsgMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信消息通知
 */
@Component
public class WetoolReportNewMsgAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportNewMsgRequestDTO, ReportNewMsgNoticeRequestDTO, ReportNewMsgAckVO, WetoolWexinReportNewMsgAckResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_NEW_MSG.equals(action);
    }

    @Override
    public ReportNewMsgNoticeRequestDTO transferRequest(WetoolWexinReportNewMsgRequestDTO input) {
        ReportNewMsgNoticeRequestDTO requestDTO = WetoolWexinReportNewMsgMapStruct.INSTANCE.toReportNewMsgRequestDTO(input);
        requestDTO.setAsync(true);
        return requestDTO;
    }

    @Override
    public WetoolWexinReportNewMsgAckResponseVO transferResponse(ReportNewMsgAckVO output) {
        WetoolWexinReportNewMsgAckResponseVO responseVO = new WetoolWexinReportNewMsgAckResponseVO();
        super.transferSuccessResponse(responseVO, null);
        WetoolWexinReportNewMsgAckResponseVO.TaskAck taskAck = WetoolWexinReportNewMsgMapStruct.INSTANCE.transferToResponseVO(output.getData());
        responseVO.setData(taskAck);
        return responseVO;
    }
}
