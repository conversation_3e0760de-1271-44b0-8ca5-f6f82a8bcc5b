package com.yaowu.alpha.domain.proxy.protocol.adapter.common;

import com.yaowu.alpha.model.dto.proxy.control.ReportTaskResultNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.common.CommonReportTaskResultRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import org.springframework.stereotype.Component;

/**
 * 任务执行成功通知转换处理器
 */
@Component
public class CommonReportTaskResultAdapter extends AbstractCommonRequestAdapter<CommonReportTaskResultRequestDTO, ReportTaskResultNoticeRequestDTO, NoticeBaseResponseVO, Boolean> {

    @Override
    protected boolean support(String action) {
        return WetoolActionConstants.REPORT_TASK_RESULT.equals(action);
    }

    @Override
    public ReportTaskResultNoticeRequestDTO transferRequest(CommonReportTaskResultRequestDTO input) {
        ReportTaskResultNoticeRequestDTO request = new ReportTaskResultNoticeRequestDTO();
        request.setTask_id(input.getTaskId());
        request.setTask_result(input.getTaskResult());
        request.setError_reason(input.getErrorReason());
        request.setProxyType(input.getProxyType().getValue());
        request.setProxyThirdType(input.getProxyThirdType().getValue());
        request.setProxyId(input.getAccountProxyId());

        return request;
    }

    @Override
    public Boolean transferResponse(NoticeBaseResponseVO output) {
        return true;
    }
}
