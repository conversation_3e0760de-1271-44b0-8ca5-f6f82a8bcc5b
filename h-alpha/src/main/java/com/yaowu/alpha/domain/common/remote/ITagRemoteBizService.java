package com.yaowu.alpha.domain.common.remote;

import com.yaowu.tagsystemapi.model.dto.tag.v2.TagAddV2DTO;
import com.yaowu.tagsystemapi.model.vo.tag.v2.TagAddV2VO;
import com.yaowu.tagsystemapi.model.vo.tag.v2.TagPageV2VO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/25 14:46
 */
public interface ITagRemoteBizService {


    List<TagPageV2VO> searchTagByName(Set<String> tagNames, Integer category);


    /**
     * 添加标签
     * @param remoteDTO
     * @return
     */
    TagAddV2VO addTag(TagAddV2DTO remoteDTO);

    /**
     * 打标签

     * @param tagCode
     * @param objInstanceId
     */
    void mark(String tagCode, String objInstanceId);

    /**
     * 批量打标签
     * @param tagCodes
     * @param objInstanceId
     */
    void batchMark(Set<String> tagCodes, String objInstanceId);

    /**
     *
     * @param tagCode
     * @return
     */
    List<String> listObjInstIds(String tagCode);

    /**
     *
     * @param tagCodes
     * @return
     */
    Map<String, List<String>> mapObjInstIdsByTagCodes(Set<String> tagCodes);
}
