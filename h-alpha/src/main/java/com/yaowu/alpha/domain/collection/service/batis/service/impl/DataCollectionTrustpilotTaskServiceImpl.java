package com.yaowu.alpha.domain.collection.service.batis.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.collection.service.batis.mapper.DataCollectionTrustpilotTaskMapper;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTrustpilotTaskService;
import com.yaowu.alpha.enums.collection.DataCollectionTrustpilotTaskStatusEnum;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotTask;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Trustpilot数据采集任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Service
public class DataCollectionTrustpilotTaskServiceImpl extends ServiceImpl<DataCollectionTrustpilotTaskMapper, DataCollectionTrustpilotTask> implements IDataCollectionTrustpilotTaskService {

    @Override
    public Page<DataCollectionTrustpilotTask> pageByCondition(String taskName, Integer taskStatus, Page<DataCollectionTrustpilotTask> page) {
        LambdaQueryWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        wrapper.like(StrUtil.isNotBlank(taskName), DataCollectionTrustpilotTask::getTaskName, taskName)
               .eq(taskStatus != null, DataCollectionTrustpilotTask::getTaskStatus, taskStatus);
        
        // 排序：最新创建的在前
        wrapper.orderByDesc(DataCollectionTrustpilotTask::getCreateTime);
        
        // 分页查询
        return page(page, wrapper);
    }

    @Override
    public List<DataCollectionTrustpilotTask> listByTaskStatus(Integer taskStatus) {
        LambdaQueryWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getTaskStatus, taskStatus)
               .orderByDesc(DataCollectionTrustpilotTask::getCreateTime);
        return list(wrapper);
    }

    @Override
    public boolean startTask(Long taskId) {
        LambdaUpdateWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getId, taskId)
               .set(DataCollectionTrustpilotTask::getTaskStatus, DataCollectionTrustpilotTaskStatusEnum.EXECUTING.getValue())
               .set(DataCollectionTrustpilotTask::getStartTime, LocalDateTime.now());
        return update(wrapper);
    }

    @Override
    public boolean pauseTask(Long taskId) {
        LambdaUpdateWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getId, taskId)
               .set(DataCollectionTrustpilotTask::getTaskStatus, DataCollectionTrustpilotTaskStatusEnum.PAUSED.getValue());
        return update(wrapper);
    }

    @Override
    public boolean cancelTask(Long taskId) {
        LambdaUpdateWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getId, taskId)
               .set(DataCollectionTrustpilotTask::getTaskStatus, DataCollectionTrustpilotTaskStatusEnum.CANCELLED.getValue())
               .set(DataCollectionTrustpilotTask::getEndTime, LocalDateTime.now());
        return update(wrapper);
    }
} 