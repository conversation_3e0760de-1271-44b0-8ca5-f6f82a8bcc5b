package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportFriendAddNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportFriendAddRequestDTO;
import com.yaowu.alpha.model.vo.proxy.ReportAddFriendAckVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinReportAddFriendAckResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinNewAddFriendMapStruct;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信添加新好友
 */
@Component
public class WetoolNewAddFriendAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportFriendAddRequestDTO, ReportFriendAddNoticeRequestDTO, ReportAddFriendAckVO, WetoolWexinReportAddFriendAckResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_FRIEND_ADD_REQUEST.equals(action);
    }

    @Override
    public ReportFriendAddNoticeRequestDTO transferRequest(WetoolWexinReportFriendAddRequestDTO input) {
        return WetoolWexinNewAddFriendMapStruct.INSTANCE.toAddNewFriendDTO(input);
    }

    @Override
    public WetoolWexinReportAddFriendAckResponseVO transferResponse(ReportAddFriendAckVO output) {
        WetoolWexinReportAddFriendAckResponseVO responseVO = new WetoolWexinReportAddFriendAckResponseVO();
        super.transferSuccessResponse(responseVO, null);
        WetoolWexinReportAddFriendAckResponseVO.TaskAck taskAck = new WetoolWexinReportAddFriendAckResponseVO.TaskAck();
        WetoolWexinReportAddFriendAckResponseVO.AddFriendAck addFriendAck = WetoolWexinNewAddFriendMapStruct.INSTANCE.toAddFriendResponseVo(output);
        WetoolWexinReportAddFriendAckResponseVO.ReplyTask replyTask = new WetoolWexinReportAddFriendAckResponseVO.ReplyTask();
        //默认通过
        replyTask.setTask_type(13);
        replyTask.setTask_dict(addFriendAck);
        List<WetoolWexinReportAddFriendAckResponseVO.ReplyTask> reply_task_list = new ArrayList<>();
        reply_task_list.add(replyTask);
        taskAck.setReply_task_list(reply_task_list);
        responseVO.setData(taskAck);
        return responseVO;
    }
}
