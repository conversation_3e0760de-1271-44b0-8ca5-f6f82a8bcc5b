package com.yaowu.alpha.domain.captcha.biz;

import com.yaowu.alpha.model.dto.passport.EmailCaptchaDTO;
import com.yaowu.alpha.model.dto.passport.SmsCaptchaDTO;
import com.yaowu.alpha.model.vo.passport.CrownCodeVO;

import java.util.List;

public interface ICaptchaBizService {

    List<CrownCodeVO> crownCodeList();

    Boolean sendSmsCaptcha(SmsCaptchaDTO dto);

    Boolean verifySmsCaptcha(String crownCode, String phone, String captchaType, String captcha);

    Boolean sendEmailCaptcha(EmailCaptchaDTO dto);

    Boolean verifyEmailCaptcha(String email, String captchaType, String captcha);
}
