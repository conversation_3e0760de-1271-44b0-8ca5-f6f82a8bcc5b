package com.yaowu.alpha.domain.common.remote;

import com.yaowu.alpha.model.bo.common.NoticeParam;
import com.yaowu.jmcapi.model.vo.message.MessageRelVO;
import com.yaowu.notice.model.dto.NotifyDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:17
 */
public interface IJmcRemoteService {

    /**
     * 发送消息
     * @param noticeParam
     * @return
     */
    NotifyDto send(NoticeParam noticeParam);

    /**
     * 完成任务消息
     * @param taskKey
     * @return
     */
    Boolean finishTaskMsg(String taskKey);

    /**
     * 判断任务是否存在
     * @param taskKey
     * @return
     */
    Boolean checkTaskExist(String taskKey);

    /**
     * 获取任务消息列表
     * @param taskKey
     * @return
     */
    List<MessageRelVO> listTaskMessage(String taskKey);


}
