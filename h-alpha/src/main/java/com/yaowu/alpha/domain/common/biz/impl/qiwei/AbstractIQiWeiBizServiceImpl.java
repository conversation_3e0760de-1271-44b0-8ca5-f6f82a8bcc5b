package com.yaowu.alpha.domain.common.biz.impl.qiwei;

import cn.hutool.json.JSONObject;
import com.freedom.redis.utils.RedisHelper;
import com.yaowu.alpha.domain.common.biz.IQiWeiExternalContactBizService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class AbstractIQiWeiBizServiceImpl implements IQiWeiExternalContactBizService {

    @Autowired
    private RedisHelper redisHelper;

    private static final String QI_WEI_TOKEN_GET_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";

    private String accessTokenKeyPrefix = "QyWeixin:AccessToken:%s:%s";

    /**
     * 发起外部调用的restTemplate不能使用自动注入的实例
     * 自动注入的会有@LoadBalance注解，会走负载均衡策略，使用服务名进行ip寻找
     */
    protected RestTemplate outerRestTemplate;

    @PostConstruct
    public void init() {
        outerRestTemplate = new RestTemplate();
        // 不设置的话，post请求中中文参数会乱码
        outerRestTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }
    protected RestTemplate getOuterRestTemplate() {
        return outerRestTemplate;
    }


    protected String getAccessToken(String corpid, String corpsecret) {
        String key = getAccessTokenKey(corpid, corpsecret);
        String accessToken = redisHelper.strGet(key, String.class);
        if (StringUtils.isBlank(accessToken)) {
            Map<String, String> params = new HashMap<>();
            params.put("corpid", corpid);
            params.put("corpsecret", corpsecret);
            JSONObject jsonObj = outerRestTemplate.getForEntity(QI_WEI_TOKEN_GET_URL + "?corpid={corpid}&corpsecret={corpsecret}", JSONObject.class, params)
                    .getBody();
            accessToken = jsonObj.getStr("access_token");
            int expiresIn = jsonObj.getInt("expires_in");
            redisHelper.strSet(key, accessToken, expiresIn - 5, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    private String getAccessTokenKey(String corpid, String corpsecret) {
        return String.format(accessTokenKeyPrefix, corpid, corpsecret);
    }
}
