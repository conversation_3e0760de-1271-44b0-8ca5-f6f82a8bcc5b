package com.yaowu.alpha.domain.common.service.batis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.TagInstanceMapper;
import com.yaowu.alpha.domain.common.service.batis.service.ITagInstanceService;
import com.yaowu.alpha.domain.common.service.batis.service.ITagService;
import com.yaowu.alpha.model.entity.common.Tag;
import com.yaowu.alpha.model.entity.common.TagInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签实例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
@Slf4j
public class TagInstanceServiceImpl extends ServiceImpl<TagInstanceMapper, TagInstance> implements ITagInstanceService {

    private final ITagService tagService;

    public TagInstanceServiceImpl(ITagService tagService) {
        this.tagService = tagService;
    }

    /**
     * 根据标签ID和实例ID查询标签实例
     * @param tagId 标签ID
     * @param instanceId 实例ID
     * @return 标签实例
     */
    @Override
    public TagInstance findByTagIdAndInstanceId(Long tagId, String instanceId) {
        LambdaQueryWrapper<TagInstance> queryWrapper = Wrappers.lambdaQuery(TagInstance.class)
                .eq(TagInstance::getTagId, tagId)
                .eq(TagInstance::getInstanceId, instanceId)
                .last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 根据标签ID查询实例ID列表
     * @param tagId 标签ID
     * @return 实例ID列表
     */
    @Override
    public List<String> listInstanceIdsByTagId(Long tagId) {
        LambdaQueryWrapper<TagInstance> queryWrapper = Wrappers.lambdaQuery(TagInstance.class)
                .eq(TagInstance::getTagId, tagId)
                .select(TagInstance::getInstanceId);
        
        List<TagInstance> instanceList = list(queryWrapper);
        
        return instanceList.stream()
                .map(TagInstance::getInstanceId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据实例ID查询标签列表
     * @param instanceId 实例ID
     * @param businessCode 业务编码
     * @return 标签列表
     */
    @Override
    public List<Tag> listTagsByInstanceId(String instanceId, String businessCode) {
        // 查询实例关联的标签ID列表
        LambdaQueryWrapper<TagInstance> queryWrapper = Wrappers.lambdaQuery(TagInstance.class)
                .eq(TagInstance::getInstanceId, instanceId)
                .select(TagInstance::getTagId);
        
        List<TagInstance> instanceList = list(queryWrapper);
        
        if (instanceList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 提取标签ID列表
        List<Long> tagIds = instanceList.stream()
                .map(TagInstance::getTagId)
                .collect(Collectors.toList());
        
        // 查询标签详情
        LambdaQueryWrapper<Tag> tagQueryWrapper = Wrappers.lambdaQuery(Tag.class)
                .in(Tag::getId, tagIds);
        
        // 如果指定了业务编码，则按业务编码过滤
        if (StringUtils.isNotBlank(businessCode)) {
            tagQueryWrapper.eq(Tag::getBusinessCode, businessCode);
        }
        
        return tagService.list(tagQueryWrapper);
    }
    
    /**
     * 批量获取多个标签的所有实例ID
     * @param tagIds 标签ID列表
     * @return 所有实例ID列表，不去重
     */
    @Override
    public List<String> listAllInstanceIdsByTagIds(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<TagInstance> queryWrapper = Wrappers.lambdaQuery(TagInstance.class)
                .in(TagInstance::getTagId, tagIds)
                .select(TagInstance::getTagId, TagInstance::getInstanceId);
        
        List<TagInstance> instanceList = list(queryWrapper);
        
        return instanceList.stream()
                .map(TagInstance::getInstanceId)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据标签ID列表批量查询标签实例
     * @param tagIds 标签ID列表
     * @return 标签实例列表
     */
    @Override
    public List<TagInstance> listByTagIds(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<TagInstance> queryWrapper = Wrappers.lambdaQuery(TagInstance.class)
                .in(TagInstance::getTagId, tagIds);
        
        return list(queryWrapper);
    }
}
