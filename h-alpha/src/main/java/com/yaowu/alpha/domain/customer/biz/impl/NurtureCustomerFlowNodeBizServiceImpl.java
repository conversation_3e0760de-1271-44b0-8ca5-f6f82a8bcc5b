package com.yaowu.alpha.domain.customer.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.nacos.NurtureCustomerFlowConfig;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowNodeBizService;
import com.yaowu.alpha.domain.customer.biz.impl.node.AbstractNurtureCustomerFlowNodeProcessor;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowNodeService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.inference.NurtureCustomerReplyClassifier;
import com.yaowu.alpha.enums.customer.NurtureCustomerFlowNodeStatusEnum;
import com.yaowu.alpha.enums.customer.NurtureCustomerFlowStatusEnum;
import com.yaowu.alpha.enums.customer.NurtureCustomerReplyTypeEnum;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;
import com.yaowu.alpha.utils.DistributedLockUtil;
import com.yaowu.alpha.utils.TransactionUtils;
import com.yaowu.alpha.utils.common.StreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 培育客户流程节点业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NurtureCustomerFlowNodeBizServiceImpl implements INurtureCustomerFlowNodeBizService {
    
    private static final String FIRST_NODE = "FIRST_GREETING_NODE";
    private static final Long DEFAULT_MAX_SIZE = 50L;

    private final List<AbstractNurtureCustomerFlowNodeProcessor> nodeProcessors;
    private final INurtureCustomerFlowNodeService nurtureCustomerFlowNodeService;
    private final INurtureCustomerFlowService nurtureCustomerFlowService;
    private final TransactionUtils transactionUtils;
    private final NurtureCustomerFlowConfig nurtureCustomerFlowConfig;
    private final DistributedLockUtil distributedLockUtil;
    private final NurtureCustomerReplyClassifier nurtureCustomerReplyClassifier;

    /**
     * 创建培育流程的首个节点
     * 
     * @param flow 培育流程对象
     */
    @Override
    public void createFirstNode(NurtureCustomerFlow flow) {
        validateFlow(flow);
        AbstractNurtureCustomerFlowNodeProcessor processor = findRequiredNodeProcessor(FIRST_NODE);
        processor.createNode(flow);
        log.info("成功创建首个节点，flowId: {}, nodeCode: {}", flow.getId(), FIRST_NODE);
    }

    /**
     * 执行待处理的培育流程节点
     * 
     * @param maxSize 单次最大处理数量
     */
    @Override
    public void executeNode(Long maxSize) {
        processNodesInBatch(
            maxSize,
            "执行培育流程节点",
            nurtureCustomerFlowNodeService::listPendingExecutionNodes,
            "没有待执行的培育流程节点", 
            this::executeNodeInternal
        );
    }

    /**
     * 处理已过期的待回复节点
     * 
     * @param maxSize 单次最大处理数量
     */
    @Override
    public void processExpiredNodes(Long maxSize) {
        processNodesInBatch(
            maxSize,
            "处理过期节点",
            nurtureCustomerFlowNodeService::listExpiredPendingReplyNodes,
            "没有待处理的过期节点",
            this::processExpiredNodeInternal
        );
    }

    /**
     * 处理用户回复的培育流程
     * 判断用户是否在培育流程中，及是否有最新的在等待回复且未过期的节点
     * 调用LLM推断回复类型，并处理相应的培育流程节点
     *
     * @param proxyAccount 代理账号
     * @param userMessage 用户消息
     */
    @Override
    public void processNurtureCustomerReply(ProxyAccount proxyAccount, ProxyChatMessage userMessage, ProxyAccountFriend friend) {
        if (proxyAccount == null || userMessage == null || friend == null) {
            log.warn("processUserReplyForNurture: proxyAccount, userMessage or friend is null");
            return;
        }
        // 使用分布式锁防止同一好友的并发处理，避免重复触发培育流程
        String lockKey = "nurture:reply:friend:" + friend.getId();
        try {
            distributedLockUtil.execute(lockKey, () -> processUserReplyInternal(proxyAccount, userMessage, friend));
        } catch (Exception e) {
            log.warn("处理用户回复培育流程失败，friendId: {}, friendName: {}, messageId: {}",
                     friend.getId(), friend.getFriendName(), userMessage.getId(), e);
        }
    }
    
    /**
     * 处理用户回复的内部逻辑
     */
    private void processUserReplyInternal(ProxyAccount proxyAccount,
                                          ProxyChatMessage userMessage,
                                          ProxyAccountFriend friend) {
        log.info("处理用户回复培育流程，friendId: {}, messageId: {}", friend.getId(), userMessage.getId());
        
        // 查找流程和待回复节点
        NurtureFlowContext flowContext = findActiveFlowAndPendingNode(friend.getId());
        if (flowContext == null) {
            return;
        }
        
        // 分析用户回复类型并处理
        NurtureCustomerReplyTypeEnum replyType = analyzeUserReply(userMessage, proxyAccount, flowContext.flow);
        if (replyType == null) {
            log.warn("无法分析用户回复类型，friendId: {}, messageId: {}", friend.getId(), userMessage.getId());
            return;
        }
        processReplyWithType(flowContext.pendingNode, replyType, userMessage.getId());
        
        log.info("培育流程回复处理完成，nodeId: {}, replyType: {}", 
                 flowContext.pendingNode.getId(), replyType.getDesc());
    }

    /**
     * 查找活跃流程和待回复节点
     */
    private NurtureFlowContext findActiveFlowAndPendingNode(Long friendId) {
        NurtureCustomerFlow activeFlow = nurtureCustomerFlowService.getActiveFlowByFriendId(friendId);
        if (activeFlow == null) {
            log.info("好友没有正在进行的培育流程，friendId: {}", friendId);
            return null;
        }
        
        NurtureCustomerFlowNode pendingNode = nurtureCustomerFlowNodeService.getActivePendingReplyNodeByFlowId(activeFlow.getId());
        if (pendingNode == null) {
            log.info("流程没有待回复的节点，flowId: {}", activeFlow.getId());
            return null;
        }
        
        return new NurtureFlowContext(activeFlow, pendingNode);
    }

    /**
     * 分析用户回复类型
     */
    private NurtureCustomerReplyTypeEnum analyzeUserReply(ProxyChatMessage userMessage, 
                                                         ProxyAccount proxyAccount, 
                                                         NurtureCustomerFlow flow) {
        List<ProxyChatMessage> contextMessages = getContextMessages(userMessage, proxyAccount);
        return classifyUserReply(contextMessages, userMessage.getChatUserProxyId(), flow);
    }

    /**
     * 根据回复类型处理
     */
    private void processReplyWithType(NurtureCustomerFlowNode pendingNode, 
                                     NurtureCustomerReplyTypeEnum replyType, 
                                     Long messageId) {
        AbstractNurtureCustomerFlowNodeProcessor processor = findRequiredNodeProcessor(pendingNode.getNodeCode());
        processor.processReply(pendingNode, replyType, List.of(messageId));
    }
    
    /**
     * 获取上下文消息
     * 基于用户最后发言的消息id回溯拿到最近一条ai发送消息及之后的用户回复消息
     *
     * @param userMessage 用户消息
     * @param proxyAccount 代理账号
     * @return 上下文消息列表
     */
    private List<ProxyChatMessage> getContextMessages(ProxyChatMessage userMessage, ProxyAccount proxyAccount) {
        // TODO: 基于用户最后发言的消息id回溯拿到最近一条ai发送消息及之后的用户回复消息，后续优化
        // 这里需要调用IProxyChatMessageService的相关方法获取上下文消息
        return List.of(userMessage);
    }
    
    /**
     * 调用LLM推断用户回复类型
     */
    /**
     * 调用培育客户回复分类器推断用户回复类型
     */
    private NurtureCustomerReplyTypeEnum classifyUserReply(List<ProxyChatMessage> contextMessages,
                                                           String chatUserProxyId,
                                                           NurtureCustomerFlow flow) {
        List<NurtureCustomerFlowConfig.ReplyAiClassifyConfig> classifyConfigs = 
                nurtureCustomerFlowConfig.getReplyAiClassifyConfig();
        
        return nurtureCustomerReplyClassifier.classifyUserReply(flow,
                chatUserProxyId,
                contextMessages,
                classifyConfigs
        );
    }


    
    /**
     * 节点批处理通用模板方法
     *
     * @param maxSize 最大处理数量
     * @param operationName 操作名称
     * @param nodesFetcher 节点获取函数
     * @param emptyMessage 无节点时的日志消息
     * @param nodeProcessor 节点处理逻辑
     */
    private void processNodesInBatch(Long maxSize,
                                   String operationName,
                                   Function<Long, List<NurtureCustomerFlowNode>> nodesFetcher,
                                   String emptyMessage,
                                   Consumer<NurtureCustomerFlowNode> nodeProcessor) {
        Long validMaxSize = validateAndGetMaxSize(maxSize);
        log.info("开始{}，最大处理数量：{}", operationName, validMaxSize);
        
        try {
            List<NurtureCustomerFlowNode> nodes = nodesFetcher.apply(validMaxSize);
            if (CollUtil.isEmpty(nodes)) {
                log.info(emptyMessage);
                return;
            }
            
            log.info("查询到节点数量：{}", nodes.size());
            processNodesWithTransaction(nodes, nodeProcessor);
            
            log.info("{}完成，处理节点数量：{}", operationName, nodes.size());
        } catch (Exception e) {
            log.error("{}失败", operationName, e);
        }
    }

    /**
     * 事务中处理节点列表
     */
    private void processNodesWithTransaction(List<NurtureCustomerFlowNode> nodes, 
                                           Consumer<NurtureCustomerFlowNode> nodeProcessor) {
        for (NurtureCustomerFlowNode node : nodes) {
            transactionUtils.execute(() -> {
                try {
                    nodeProcessor.accept(node);
                } catch (Exception e) {
                    logNodeError(node, e);
                    handleNodeError(node, e);
                }
            });
        }
    }

    /**
     * 执行单个节点的内部逻辑
     */
    private void executeNodeInternal(NurtureCustomerFlowNode node) {
        log.info("开始执行节点，nodeId: {}, nodeCode: {}, templateCode: {}", 
                 node.getId(), node.getNodeCode(), node.getTemplateCode());

        AbstractNurtureCustomerFlowNodeProcessor processor = findRequiredNodeProcessor(node.getNodeCode());
        processor.execute(node);
        
        log.info("节点执行完成，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
    }

    /**
     * 处理单个过期节点的内部逻辑
     */
    private void processExpiredNodeInternal(NurtureCustomerFlowNode node) {
        log.info("开始处理过期节点，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());

        AbstractNurtureCustomerFlowNodeProcessor processor = findRequiredNodeProcessor(node.getNodeCode());
        processor.processReply(node, NurtureCustomerReplyTypeEnum.NO_REPLY, null);
        
        log.info("过期节点处理完成，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
    }

    /**
     * 处理节点执行异常
     */
    private void handleNodeError(NurtureCustomerFlowNode node, Exception e) {
        try {
            updateNodeErrorStatus(node, e);
            updateFlowErrorStatus(node, e);
        } catch (Exception updateException) {
            log.error("更新节点或流程异常状态失败，nodeId: {}", node.getId(), updateException);
        }
    }

    /**
     * 更新节点异常状态
     */
    private void updateNodeErrorStatus(NurtureCustomerFlowNode node, Exception e) {
        node.setNodeStatus(NurtureCustomerFlowNodeStatusEnum.EXCEPTION);
        node.setErrorTime(LocalDateTime.now());
        node.setErrorMsg(StrUtil.sub(e.getMessage(), 0, 2000));
        nurtureCustomerFlowNodeService.updateById(node);
        
        log.info("已将节点状态更新为异常，nodeId: {}, errorMsg: {}", node.getId(), e.getMessage());
    }

    /**
     * 更新流程异常状态
     */
    private void updateFlowErrorStatus(NurtureCustomerFlowNode node, Exception e) {
        NurtureCustomerFlow flow = nurtureCustomerFlowService.getById(node.getFlowId());
        if (flow != null) {
            flow.setFlowStatus(NurtureCustomerFlowStatusEnum.ERROR);
            flow.setFlowErrorMsg(StrUtil.sub(e.getMessage(), 0, 2000));
            flow.setErrorNodeId(node.getId());
            nurtureCustomerFlowService.updateById(flow);
            
            log.info("已将流程状态更新为异常，flowId: {}, errorMsg: {}", flow.getId(), e.getMessage());
        }
    }

    /**
     * 查找支持指定节点代码的处理器（必须存在）
     */
    private AbstractNurtureCustomerFlowNodeProcessor findRequiredNodeProcessor(String nodeCode) {
        AbstractNurtureCustomerFlowNodeProcessor processor = findNodeProcessor(nodeCode);
        if (processor == null) {
            throw new BusinessException("未找到支持节点[" + nodeCode + "]的处理器");
        }
        return processor;
    }

    /**
     * 查找支持指定节点代码的处理器
     */
    private AbstractNurtureCustomerFlowNodeProcessor findNodeProcessor(String nodeCode) {
        return StreamUtil.of(nodeProcessors)
                .filter(processor -> processor.supports(nodeCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 校验流程参数
     */
    private void validateFlow(NurtureCustomerFlow flow) {
        if (flow == null || flow.getId() == null) {
            throw new BusinessException("流程信息不能为空");
        }
    }

    /**
     * 校验并获取有效的最大处理数量
     */
    private Long validateAndGetMaxSize(Long maxSize) {
        return (maxSize == null || maxSize <= 0) ? DEFAULT_MAX_SIZE : maxSize;
    }

    /**
     * 记录节点错误日志
     */
    private void logNodeError(NurtureCustomerFlowNode node, Exception e) {
        if (node.getTemplateCode() != null) {
            log.error("执行节点失败，nodeId: {}, nodeCode: {}, templateCode: {}",
                    node.getId(), node.getNodeCode(), node.getTemplateCode(), e);
        } else {
            log.error("处理过期节点失败，nodeId: {}, nodeCode: {}",
                    node.getId(), node.getNodeCode(), e);
        }
    }

    /**
     * 培育流程上下文
     */
    private static class NurtureFlowContext {
        final NurtureCustomerFlow flow;
        final NurtureCustomerFlowNode pendingNode;

        NurtureFlowContext(NurtureCustomerFlow flow, NurtureCustomerFlowNode pendingNode) {
            this.flow = flow;
            this.pendingNode = pendingNode;
        }
    }
}
