package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportFriendAddNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.ReportAddFriendAckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 请求添加好友通知
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportNewAddFriendNoticeActionProcessor implements INoticeActionProcessor<ReportFriendAddNoticeRequestDTO, ReportAddFriendAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportFriendAddNoticeRequestDTO;
    }


    @Override
    public ReportAddFriendAckVO process(ReportFriendAddNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return ReportAddFriendAckVO.autoAccept(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-请求添加好友通知-消息：{}", JacksonUtils.toJsonStr(request));

        return ReportAddFriendAckVO.autoAccept(request);
    }

}
