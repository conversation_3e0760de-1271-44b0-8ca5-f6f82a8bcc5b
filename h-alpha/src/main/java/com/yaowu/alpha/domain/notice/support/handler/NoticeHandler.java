package com.yaowu.alpha.domain.notice.support.handler;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.common.remote.ICustomerServiceRemoteBizService;
import com.yaowu.alpha.domain.notice.support.event.NoticeEvent;
import com.yaowu.alpha.domain.notice.support.parser.NoticeParser;
import com.yaowu.alpha.domain.notice.support.sender.NoticeSender;
import com.yaowu.alpha.model.bo.common.NoticeParam;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteCustomerPushDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/28
 */
@Component
@Slf4j
public class NoticeHandler {

    private static final ExecutorService EXECUTOR = new ThreadPoolExecutor(5, 10, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1024),
            new BasicThreadFactory.Builder().namingPattern("notice-send-thread-%d").daemon(true).build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private List<NoticeParser<?>> noticeParsers;

    @Autowired
    private NoticeSender noticeSender;

    @Autowired
    private ICustomerServiceRemoteBizService customerServiceRemoteBizService;

    @EventListener
    public void onApplicationEvent(NoticeEvent event) {
        log.info("通知发送监听到事件，eventData:{}", JSONUtil.toJsonStr(event.getData()));
        NoticeParser noticeParser = noticeParsers.stream().filter(parser -> parser.support(event)).findFirst()
                .orElse(null);
        if (noticeParser == null) {
            log.error("事件无法路由到合适解析器，eventClazz:{}, event:{}", event.getClass(), JSONUtil.toJsonStr(event));
            return;
        }
        if (false && noticeParser.isAsync()) {
            CompletableFuture.runAsync(RunnableWrapper.of(() -> send(event, noticeParser)), EXECUTOR);
            return;
        }
        send(event, noticeParser);
    }

    private void send(NoticeEvent event, NoticeParser noticeParser) {
        try {
            NoticeParam param = noticeParser.parse(event.getData());
            if (param != null) {
                this.send(param);
                return;
            }
            List<NoticeParam> list = noticeParser.parseList(event.getData());
            for (NoticeParam noticeParam : list) {
                if (noticeParam != null) {
                    this.send(noticeParam);

                }
            }
        } catch (Exception e) {
            log.error("发送通知时异常， eventData:{}", JSONUtil.toJsonStr(event.getData()), e);
        }
    }

    private void send(NoticeParam noticeParam) {
        if (noticeParam == null) {
            return;
        }
        if (NoticeParam.NoticeSceneEnum.NOTIFY_TO_PLATFORM_BD_WX_BOOT.equals(noticeParam.getNoticeScene())) {
            // 判断一下这个是不是比较特殊的场景
            customerServiceRemoteBizService.pushCustomerMsgToWxBoot(convert(noticeParam));
        } else {
            noticeSender.send(noticeParam);
        }
    }

    private RemoteCustomerPushDTO convert(NoticeParam noticeParam) {
        RemoteCustomerPushDTO pushDTO = new RemoteCustomerPushDTO();
        pushDTO.setCustomerId(noticeParam.getCustomerId());
        pushDTO.setTemplateCode(noticeParam.getTemplateCode());
        pushDTO.setTemplateParams(noticeParam.getTemplateParams());
        pushDTO.setExtParams(noticeParam.getExtParams());

        return pushDTO;
    }

}
