package com.yaowu.alpha.domain.customer.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.customer.service.batis.mapper.NurtureCustomerFlowMapper;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowService;
import com.yaowu.alpha.enums.customer.NurtureCustomerFlowStatusEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.bo.customer.TenantContactTypePair;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.utils.common.StreamUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户培育流程状态管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class NurtureCustomerFlowServiceImpl extends ServiceImpl<NurtureCustomerFlowMapper, NurtureCustomerFlow> implements INurtureCustomerFlowService {

    @Override
    public boolean hasUnfinishedFlow(Long customerId) {
        if (customerId == null) {
            return false;
        }
        return lambdaQuery()
                .eq(NurtureCustomerFlow::getCustomerId, customerId)
                .notIn(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.getFinishedStatuses())
                .count() > 0;
    }

    @Override
    public Set<Long> getCustomersWithUnfinishedFlow(List<Long> customerIds) {
        if (CollUtil.isEmpty(customerIds)) {
            return Collections.emptySet();
        }
        List<NurtureCustomerFlow> flows = lambdaQuery()
                .in(NurtureCustomerFlow::getCustomerId, customerIds)
                .notIn(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.getFinishedStatuses())
                .select(NurtureCustomerFlow::getCustomerId)
                .list();
        
        return StreamUtil.of(flows)
                .map(NurtureCustomerFlow::getCustomerId)
                .collect(java.util.stream.Collectors.toSet());
    }

    @Override
    public List<String> listPendingSenders() {
        List<NurtureCustomerFlow> flows = lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .select(NurtureCustomerFlow::getSender)
                .groupBy(NurtureCustomerFlow::getSender)
                .list();
        
        return StreamUtil.of(flows)
                .map(NurtureCustomerFlow::getSender)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<NurtureCustomerFlow> listPendingFlowsBySender(String sender, int limit) {
        if (StrUtil.isBlank(sender) || limit <= 0) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .eq(NurtureCustomerFlow::getSender, sender)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .orderByAsc(NurtureCustomerFlow::getPriority)
                .orderByAsc(NurtureCustomerFlow::getFlowStartTime)
                .last("LIMIT " + limit)
                .list();
    }

    @Override
    public NurtureCustomerFlow getActiveFlowByFriendId(Long friendId) {
        if (friendId == null) {
            return null;
        }
        return lambdaQuery()
                .eq(NurtureCustomerFlow::getFriendId, friendId)
                .notIn(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.getFinishedStatuses())
                .orderByDesc(NurtureCustomerFlow::getFlowStartTime)
                .last("LIMIT 1")
                .one();
    }

    @Override
    public List<Long> listPendingTenantIds() {
        List<NurtureCustomerFlow> flows = lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .select(NurtureCustomerFlow::getTenantId)
                .groupBy(NurtureCustomerFlow::getTenantId)
                .list();
        
        return StreamUtil.of(flows)
                .map(NurtureCustomerFlow::getTenantId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<NurtureCustomerFlow> listPendingFlowsByTenant(Long tenantId, int limit) {
        if (tenantId == null || limit <= 0) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .eq(NurtureCustomerFlow::getTenantId, tenantId)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .orderByAsc(NurtureCustomerFlow::getPriority)
                .orderByAsc(NurtureCustomerFlow::getFlowStartTime)
                .last("LIMIT " + limit)
                .list();
    }

    @Override
    public List<TenantContactTypePair> listPendingTenantContactTypePairs() {
        List<NurtureCustomerFlow> flows = lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .select(NurtureCustomerFlow::getTenantId, NurtureCustomerFlow::getCustomerContactType)
                .groupBy(NurtureCustomerFlow::getTenantId, NurtureCustomerFlow::getCustomerContactType)
                .list();

        return StreamUtil.of(flows)
                .filter(flow -> flow.getTenantId() != null && flow.getCustomerContactType() != null)
                .map(flow -> new TenantContactTypePair(
                        flow.getTenantId(),
                        flow.getCustomerContactType()))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<NurtureCustomerFlow> listPendingFlowsByTenantAndContactType(Long tenantId, ProxyThirdTypeEnum contactType, int limit) {
        if (tenantId == null || contactType == null || limit <= 0) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .eq(NurtureCustomerFlow::getFlowStatus, NurtureCustomerFlowStatusEnum.PENDING)
                .eq(NurtureCustomerFlow::getTenantId, tenantId)
                .eq(NurtureCustomerFlow::getCustomerContactType, contactType)
                .le(NurtureCustomerFlow::getFlowStartTime, LocalDateTime.now())
                .orderByAsc(NurtureCustomerFlow::getPriority)
                .orderByAsc(NurtureCustomerFlow::getFlowStartTime)
                .last("LIMIT " + limit)
                .list();
    }
}
