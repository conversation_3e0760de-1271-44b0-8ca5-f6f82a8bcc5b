package com.yaowu.alpha.domain.customer.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.customer.converter.NurtureCustomerConverter;
import com.yaowu.alpha.domain.customer.service.batis.mapper.NurtureCustomerMapper;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerService;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerCreateDTO;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;
import com.yaowu.alpha.utils.common.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 客户培育池 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@Slf4j
public class NurtureCustomerServiceImpl extends ServiceImpl<NurtureCustomerMapper, NurtureCustomer> implements INurtureCustomerService {

    @Override
    public NurtureCustomer createNurtureCustomer(NurtureCustomerCreateDTO dto) {
        // 转换为实体对象
        NurtureCustomer nurtureCustomer = NurtureCustomerConverter.INSTANCE.toEntity(dto);
        
        // 将Integer类型的contactType转换为枚举
        ProxyThirdTypeEnum contactTypeEnum = EnumUtil.fromValue(dto.getContactType(), ProxyThirdTypeEnum.class);
        if (contactTypeEnum == null) {
            throw new BusinessException("联系方式类型不合法");
        }
        nurtureCustomer.setContactType(contactTypeEnum);
        
        // 保存到数据库
        boolean saved = save(nurtureCustomer);
        if (!saved) {
            throw new BusinessException("新增培育客户失败");
        }
        
        log.info("成功新增培育客户，ID：{}, 联系方式：{}", nurtureCustomer.getId(), dto.getCustomerContact());
        return nurtureCustomer;
    }

    @Override
    public boolean existsByCustomerContact(String customerContact) {
        return lambdaQuery()
                .eq(NurtureCustomer::getCustomerContact, customerContact)
                .count() > 0;
    }

    @Override
    public List<NurtureCustomer> listByIds(List<Long> customerIds) {
        if (CollUtil.isEmpty(customerIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(NurtureCustomer::getId, customerIds)
                .list();
    }
}
