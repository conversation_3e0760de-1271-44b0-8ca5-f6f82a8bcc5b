package com.yaowu.alpha.domain.collection.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.bo.collection.TaskDataCountBO;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawDataPageDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;

import java.util.List;

/**
 * <p>
 * 数据采集原始数据集
 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IDataCollectionRawDataService extends IService<DataCollectionRawData> {

    /**
     * 批量统计任务的采集数据数量
     * 
     * @param taskIds 任务ID列表
     * @return 任务数据统计结果列表
     */
    List<TaskDataCountBO> countDataByTaskIds(List<Long> taskIds);
    
    /**
     * 分页查询数据采集原始数据
     * 
     * @param dto 查询条件
     * @return 分页结果
     */
    Page<DataCollectionRawData> pageByCondition(DataCollectionRawDataPageDTO dto);
}
