package com.yaowu.alpha.domain.customer.converter;

import com.yaowu.alpha.model.dto.customer.NurtureCustomerCreateDTO;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;
import com.yaowu.alpha.model.vo.customer.NurtureCustomerVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 培育客户对象转换器
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Mapper
public interface NurtureCustomerConverter {
    
    NurtureCustomerConverter INSTANCE = Mappers.getMapper(NurtureCustomerConverter.class);
    
    /**
     * 将创建DTO转换为实体
     *
     * @param dto 创建DTO
     * @return 实体对象
     */
    @Mapping(target = "contactType", ignore = true)
    NurtureCustomer toEntity(NurtureCustomerCreateDTO dto);
    
    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    @Mapping(target = "contactType", expression = "java(entity.getContactType() != null ? entity.getContactType().getValue() : null)")
    NurtureCustomerVO toVO(NurtureCustomer entity);
} 