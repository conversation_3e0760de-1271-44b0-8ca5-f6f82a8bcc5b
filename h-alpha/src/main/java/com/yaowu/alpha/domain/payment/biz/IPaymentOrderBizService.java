package com.yaowu.alpha.domain.payment.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.payment.PaymentOrderCreateRequest;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryDTO;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryRequest;
import com.yaowu.alpha.model.entity.payment.PaymentOrder;
import com.yaowu.alpha.model.vo.payment.PaymentOrderDetailVO;
import com.yaowu.alpha.model.vo.payment.PaymentOrderVO;
import com.yaowu.alpha.model.vo.payment.PaymentProductResultVO;
import com.yaowu.alpha.model.vo.payment.PaymentResultVO;
import com.yaowu.settle.api.model.mtl.pojo.message.RemotePaymentNotificationResultMessage;

/**
 * <AUTHOR>
 * @date 2025/4/7-20:38
 */
public interface IPaymentOrderBizService {

    /**
     * 创建订单
     *
     * @param dto
     * @return
     */
    PaymentResultVO createOrderAndPay(PaymentOrderCreateRequest dto);

    /**
     * 商品列表
     *
     * @return
     */
    PaymentProductResultVO products();

    /**
     * 分页查询订单
     *
     * @param dto
     * @return
     */
    BasePage<PaymentOrderVO> page(PaymentOrderQueryRequest dto);

    /**
     * 订单详情
     *
     * @param id
     * @return
     */
    PaymentOrderDetailVO detail(Long id);

    /**
     * 根据id获取订单
     *
     * @param id
     * @return
     */
    PaymentOrder getAndCheckPaymentOrder(Long id);

    /**
     * 处理支付结果
     *
     * @param message
     */
    void handlerMqMessage(RemotePaymentNotificationResultMessage message);
}
