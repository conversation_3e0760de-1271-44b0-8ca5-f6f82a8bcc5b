package com.yaowu.alpha.domain.notice.support.parser;

import com.yaowu.alpha.domain.notice.support.common.EventClazzCache;
import com.yaowu.alpha.domain.notice.support.event.NoticeEvent;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/29
 */
public abstract class AbstractNoticeParser<T> implements NoticeParser<T> {

    @Autowired
    private EventClazzCache<T> clazzCache;

    @Override
    public boolean support(NoticeEvent<?> event) {
        Class<T> eventClazz = clazzCache.getEventClazz(getClass());
        return eventClazz.isAssignableFrom(event.getData().getClass());
    }
}
