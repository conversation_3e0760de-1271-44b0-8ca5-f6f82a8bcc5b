package com.yaowu.alpha.domain.proxy.protocol.adapter.common;

import com.yaowu.alpha.domain.proxy.protocol.INoticeActionAdapter;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;

/**
 * <AUTHOR>
 * @date 2024/12/4 11:11
 */
public abstract class AbstractCommonRequestAdapter<T1, T2 extends BaseNoticeRequestDTO, R1 extends NoticeBaseResponseVO, R2> implements INoticeActionAdapter<T1, T2, R1, R2> {


    @Override
    public Boolean supportAction(ProxyTypeEnum proxyType, ProxyThirdTypeEnum thirdParty, String action) {
        return support(action) && (
                ProxyTypeEnum.YING_DAO.equals(proxyType)
                        || ProxyTypeEnum.INNER_API.equals(proxyType)
                        || ProxyTypeEnum.JIN_YUN.equals(proxyType)
                        || ProxyTypeEnum.AICC.equals(proxyType)
                        || ProxyTypeEnum.Y_CLOUD.equals(proxyType)
                );
    }

    protected abstract boolean support(String action);

}
