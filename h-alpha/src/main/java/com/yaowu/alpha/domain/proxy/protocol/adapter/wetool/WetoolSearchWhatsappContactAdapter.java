package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.SearchWhatsappContactNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinSearchWhatsappContactResultDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolSearchWhatsappContactMapStruct;
import org.springframework.stereotype.Component;

/**
 * 搜索WhatsApp联系人结果适配器
 * <AUTHOR>
 */
@Component
public class WetoolSearchWhatsappContactAdapter extends AbstractWetoolActionAdapter<WetoolWexinSearchWhatsappContactResultDTO, SearchWhatsappContactNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.SEARCH_WHATSAPP_CONTACT_RESULT.equals(action);
    }

    @Override
    public SearchWhatsappContactNoticeRequestDTO transferRequest(WetoolWexinSearchWhatsappContactResultDTO input) {
        return WetoolSearchWhatsappContactMapStruct.INSTANCE.toSearchWhatsappContactRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }
}
