package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.model.bo.proxy.qw.QwExternalUserDetail;

import java.util.List;

/**
 * 企微外部外联系人服务
 * 目前对应的企业ID是要务
 * 对应的应用是机械星球小程序
 *
 * <AUTHOR>
 */
public interface IQiWeiExternalContactBizService {

    /**
     * 获取企微员工外部客户Id
     * https://developer.work.weixin.qq.com/document/path/92113
     *
     * @param workWxUserId 企微员工ID
     * @return
     */
    List<String> getExternalContact(String workWxUserId);

    /**
     * 获取企微员工外部客户Id
     * https://developer.work.weixin.qq.com/document/path/92114
     *
     * @param workWxUserId   企微员工ID
     * @param externalUserid 外部客户ID
     * @return
     */
    QwExternalUserDetail getExternalContactDetail(String workWxUserId, String externalUserid);

    /**
     * 更新外部联系人描述
     *
     * @param workWxUserId
     * @param externalUserid
     * @param description
     * @return
     */
    Boolean updateExternalUserDescription(String workWxUserId, String externalUserid, String description);
}
