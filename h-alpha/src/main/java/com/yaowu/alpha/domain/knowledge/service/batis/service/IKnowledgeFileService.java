package com.yaowu.alpha.domain.knowledge.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.knowledge.KnowledgeFilePageDTO;
import com.yaowu.alpha.model.entity.knowledge.KnowledgeFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 知识库文件表 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface IKnowledgeFileService extends IService<KnowledgeFile> {

    List<KnowledgeFile> getByKnowledgeId(Long knowledgeId);

    Map<Long, List<KnowledgeFile>> getByKnowledgeIds(List<Long> knowledgeIds);

    Page<KnowledgeFile> pageByDto(KnowledgeFilePageDTO dto);
}
