package com.yaowu.alpha.domain.common.feign.fallback;

import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.common.feign.IRemoteLLMBizServiceFeign;
import org.springframework.cloud.openfeign.FallbackFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/26 10:04
 */
public class RemoteLLMServiceFeignFactory implements FallbackFactory<IRemoteLLMBizServiceFeign> {
    @Override
    public IRemoteLLMBizServiceFeign create(Throwable cause) {
        throw new BusinessException(cause.getMessage());
    }
}
