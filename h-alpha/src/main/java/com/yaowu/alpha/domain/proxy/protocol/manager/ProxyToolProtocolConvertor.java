package com.yaowu.alpha.domain.proxy.protocol.manager;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.*;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/6 16:24
 */
public class ProxyToolProtocolConvertor {

    private static final Map<String, Class<? extends BaseWetoolRequest>> TYPE_MAPPING = new HashMap<>();

    static  {
        TYPE_MAPPING.put(WetoolActionConstants.LOGIN, WetoolWexinLoginRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_CONTACT, WetoolWexinReportContactRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_FRIEND_ADD_REQUEST, WetoolWexinReportFriendAddRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_NEW_MSG, WetoolWexinReportNewMsgRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_NEW_FRIEND, WetoolWexinReportNewFriendRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.PULL_TASK, BaseWetoolRequest.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_TASK_RESULT, WetoolWexinReportTaskResultRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_CONTACT_UPDATE, WetoolWexinReportContractUpdateRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_USER_INFO, WetoolWexinReportUserInfoRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_ROOM_MEMBER_UPDATE, WetoolWexinReportRoomMemberUpdateRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_NEW_ROOM, WetoolWexinReportNewRoomRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_ROOM_MEMBER_CHANGE, WetoolWexinReportRoomMemberChangeDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_ROOM_REMOVED, WetoolWexinReportRoomRemoveDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_TAG_INFO, WetoolWexinReportTagInfoDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_FRIEND_REMOVED, WetoolWexinReportFriendRemoveDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REPORT_ROOM_MEMBER_INFO, WetoolWexinReportRoomMemberInfoDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.SEARCH_WHATSAPP_CONTACT_RESULT, WetoolWexinSearchWhatsappContactResultDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.LOGOUT_WHATSAPP_ACCOUNT, WetoolWexinLogoutWhatsappAccountRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.QUERY_WHATSAPP_MSG_STATUS, WetoolWexinQueryWhatsappMsgStatusRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.REVOKE_WHATSAPP_MSG, WetoolWexinRevokeWhatsappMsgRequestDTO.class);
        TYPE_MAPPING.put(WetoolActionConstants.QUERY_LAST_MSG, WetoolWexinQueryLastMsgRequestDTO.class);

    }

    public static BaseWetoolRequest convertWeToolProtocol(WetoolCommonRequest request) {
        Class<? extends BaseWetoolRequest> type = TYPE_MAPPING.get(request.getAction());
        if (type == null) {
            return null;
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(request), type);
    }


}
