package com.yaowu.alpha.domain.common.service.batis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.common.service.batis.mapper.TagMapper;
import com.yaowu.alpha.domain.common.service.batis.service.ITagService;
import com.yaowu.alpha.model.dto.common.TagQueryDTO;
import com.yaowu.alpha.model.entity.common.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 代理平台标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements ITagService {

    @Override
    public Tag findTag(Long tenantId, String businessCode, String tagName) {
        return this.lambdaQuery()
                .eq(Tag::getTenantId, tenantId)
                .eq(Tag::getBusinessCode, businessCode)
                .eq(Tag::getTagName, tagName)
                .last("limit 1")
                .one();
    }

    @Override
    public List<Tag> listByCondition(TagQueryDTO dto) {
        LambdaQueryWrapper<Tag> queryWrapper = buildQueryWrapper(dto);
        
        // 设置查询数量限制
        if (dto.getLimit() != null) {
            queryWrapper.last("limit " + dto.getLimit());
        }
        
        return list(queryWrapper);
    }

    @Override
    public BasePage<Tag> pageByCondition(TagQueryDTO dto) {
        LambdaQueryWrapper<Tag> queryWrapper = buildQueryWrapper(dto);
        Page<Tag> page = page(dto.pageRequest(), queryWrapper);
        return new BasePage<>(page, page.getRecords());
    }
    
    /**
     * 构建标签查询条件
     * @param dto 查询参数
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<Tag> buildQueryWrapper(TagQueryDTO dto) {
        LambdaQueryWrapper<Tag> queryWrapper = Wrappers.lambdaQuery(Tag.class);
        
        // 添加租户ID条件
        if (dto.getTenantId() != null) {
            queryWrapper.eq(Tag::getTenantId, dto.getTenantId());
        }
        
        // 添加业务编码条件
        if (StringUtils.isNotBlank(dto.getBusinessCode())) {
            queryWrapper.eq(Tag::getBusinessCode, dto.getBusinessCode());
        }
        
        // 添加标签名称条件（模糊查询）
        if (StringUtils.isNotBlank(dto.getTagName())) {
            queryWrapper.like(Tag::getTagName, dto.getTagName());
        }
        
        // 按ID降序排序
        queryWrapper.orderByDesc(Tag::getId);
        
        return queryWrapper;
    }
}
