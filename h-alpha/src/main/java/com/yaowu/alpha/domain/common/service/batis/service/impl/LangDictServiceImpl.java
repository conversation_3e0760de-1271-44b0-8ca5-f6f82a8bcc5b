package com.yaowu.alpha.domain.common.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.LangDictMapper;
import com.yaowu.alpha.domain.common.service.batis.service.ILangDictService;
import com.yaowu.alpha.model.dto.common.LangDictQueryDTO;
import com.yaowu.alpha.model.entity.common.LangDict;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/22-14:42
 */
@Service
public class LangDictServiceImpl extends ServiceImpl<LangDictMapper, LangDict>
        implements ILangDictService {

    public List<LangDict> listByCondition(LangDictQueryDTO dto) {
        return list(wrapper(dto));
    }


    public Page<LangDict> pageByCondition(LangDictQueryDTO dto) {
        return page(dto.pageRequest(), wrapper(dto));
    }

    public LangDict getParentLangDict(String categoryCode) {
        LambdaQueryWrapper<LangDict> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(LangDict::getCategoryCode, categoryCode);
        wrapper.eq(LangDict::getParent, 0L);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }

    public LangDict getByCondition(String lang, String categoryCode, String fieldName) {
        LangDictQueryDTO dto = new LangDictQueryDTO();
        dto.setLang(lang);
        dto.setCategoryCodes(CollUtil.newHashSet(categoryCode));
        dto.setFieldNames(CollUtil.newHashSet(fieldName));
        return getOne(wrapper(dto));
    }

    public boolean remoteByParent(Long parent) {
        LambdaQueryWrapper<LangDict> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(LangDict::getParent, parent);
        return remove(wrapper);
    }

    private LambdaQueryWrapper<LangDict> wrapper(LangDictQueryDTO queryDTO) {
        LambdaQueryWrapper<LangDict> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Objects.nonNull(queryDTO.getLang()), LangDict::getLang, queryDTO.getLang());
        wrapper.and(CollUtil.isNotEmpty(queryDTO.getCategoryCodes()), q -> q.in(LangDict::getCategoryCode, queryDTO.getCategoryCodes()))
                .and(CollUtil.isNotEmpty(queryDTO.getFieldNames()), q -> q.in(LangDict::getFieldName, queryDTO.getFieldNames()));
        wrapper.eq(queryDTO.getParent() != null, LangDict::getParent, queryDTO.getParent());
        wrapper.gt(queryDTO.getChild() != null && BooleanUtil.isTrue(queryDTO.getChild()), LangDict::getParent, 0L);
        wrapper.like(StringUtils.isNotBlank(queryDTO.getCategoryCodeLike()), LangDict::getCategoryCode, queryDTO.getCategoryCodeLike());
        wrapper.like(StringUtils.isNotBlank(queryDTO.getCategoryDescLike()), LangDict::getCategoryDesc, queryDTO.getCategoryDescLike());
        return wrapper;
    }
}
