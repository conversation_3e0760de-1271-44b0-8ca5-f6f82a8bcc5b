package com.yaowu.alpha.domain.passport.biz;

import com.yaowu.alpha.model.dto.passport.LoginByEmailDTO;
import com.yaowu.alpha.model.dto.passport.LoginByPwdDTO;
import com.yaowu.alpha.model.dto.passport.LoginBySmsDTO;
import com.yaowu.alpha.model.vo.passport.TokenVO;
import com.yaowu.alpha.model.vo.passport.UserInfoVO;

public interface ILoginBizService {

    TokenVO loginByPwd(LoginByPwdDTO dto);

    TokenVO loginBySms(LoginBySmsDTO dto);

    TokenVO loginByEmail(LoginByEmailDTO dto);

    TokenVO refreshToken(String refreshToken);

    UserInfoVO getUserInfo();
}
