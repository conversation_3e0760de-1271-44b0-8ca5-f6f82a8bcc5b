package com.yaowu.alpha.domain.collection.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.collection.service.batis.mapper.DataCollectionRawMapper;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionRawDataService;
import com.yaowu.alpha.model.bo.collection.TaskDataCountBO;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawDataPageDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据采集原始数据集
 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class DataCollectionRawDataServiceImpl extends ServiceImpl<DataCollectionRawMapper, DataCollectionRawData> implements IDataCollectionRawDataService {

    @Override
    public List<TaskDataCountBO> countDataByTaskIds(List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return List.of();
        }
        
        // 直接返回数据库查询结果，使用强类型BO对象
        return baseMapper.countByTaskIds(taskIds);
    }
    
    @Override
    public Page<DataCollectionRawData> pageByCondition(DataCollectionRawDataPageDTO dto) {
        LambdaQueryWrapper<DataCollectionRawData> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(dto.getTaskId() != null, DataCollectionRawData::getTaskId, dto.getTaskId());
        
        // 关键词多字段搜索
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            wrapper.and(qw -> qw
                // 标题模糊查询
                .like(DataCollectionRawData::getTitle, dto.getKeyword())
                // 国家模糊查询
                .or().like(DataCollectionRawData::getCountry, dto.getKeyword())
                // 城市模糊查询
                .or().like(DataCollectionRawData::getCity, dto.getKeyword())
                // 类别模糊查询
                .or().like(DataCollectionRawData::getCategory, dto.getKeyword())
                // 地址信息模糊查询（直接对location JSON字段进行like）
                .or().like(DataCollectionRawData::getLocation, dto.getKeyword())
            );
        }
        
        // 排序：最新创建的在前
        wrapper.orderByDesc(DataCollectionRawData::getCreateTime);
        
        // 分页查询
        return page(dto.pageRequest(), wrapper);
    }
}
