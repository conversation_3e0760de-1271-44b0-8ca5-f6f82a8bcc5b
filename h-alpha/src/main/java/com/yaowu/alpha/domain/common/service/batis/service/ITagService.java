package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.common.TagQueryDTO;
import com.yaowu.alpha.model.entity.common.Tag;

import java.util.List;

/**
 * <p>
 * 代理平台标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface ITagService extends IService<Tag> {

    /**
     *
     * @param tenantId
     * @param businessCode
     * @param tagName
     * @return
     */
    Tag findTag(Long tenantId, String businessCode, String tagName);

    /**
     * 根据查询条件获取标签列表
     * @param dto 查询参数
     * @return 标签列表
     */
    List<Tag> listByCondition(TagQueryDTO dto);

    /**
     * 根据查询条件分页获取标签列表
     * @param dto 查询参数
     * @return 分页标签列表
     */
    BasePage<Tag> pageByCondition(TagQueryDTO dto);
}
