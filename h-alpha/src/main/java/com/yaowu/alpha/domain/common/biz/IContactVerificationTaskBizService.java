package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.model.dto.common.ContactVerificationCreateDTO;
import com.yaowu.alpha.model.dto.common.ContactVerificationQueryDTO;
import com.yaowu.alpha.model.vo.common.ContactVerificationCreateResultVO;
import com.yaowu.alpha.model.vo.common.ContactVerificationStatusVO;

import java.util.List;

public interface IContactVerificationTaskBizService {

    /**
     * 批量创建验证任务
     * @param dto 创建请求
     * @return 创建结果统计
     */
    ContactVerificationCreateResultVO createVerificationTasks(ContactVerificationCreateDTO dto);

    /**
     * 查询验证状态
     * @param dto 查询条件
     * @return 验证状态列表
     */
    List<ContactVerificationStatusVO> queryVerificationStatus(ContactVerificationQueryDTO dto);

    /**
     * 处理验证任务
     * 从数据库获取待验证的任务并执行验证
     *
     * @param batchSize 批次大小，默认50
     * @return 处理的任务数量
     */
    int processVerificationTasks(int batchSize);
}
