package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportNewFriendNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportNewFriendRequestDTO;
import com.yaowu.alpha.model.vo.proxy.ReportNewMsgAckVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinReportNewMsgAckResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinNewFriendMapStruct;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinReportNewMsgMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信新好友通知
 */
@Component
public class WetoolNewFriendAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportNewFriendRequestDTO, ReportNewFriendNoticeRequestDTO, ReportNewMsgAckVO, WetoolWexinReportNewMsgAckResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_NEW_FRIEND.equals(action);
    }

    @Override
    public ReportNewFriendNoticeRequestDTO transferRequest(WetoolWexinReportNewFriendRequestDTO input) {
        return WetoolWexinNewFriendMapStruct.INSTANCE.toNewFriendDTO(input);
    }

    @Override
    public WetoolWexinReportNewMsgAckResponseVO transferResponse(ReportNewMsgAckVO output) {
        WetoolWexinReportNewMsgAckResponseVO responseVO = new WetoolWexinReportNewMsgAckResponseVO();
        super.transferSuccessResponse(responseVO, null);
        WetoolWexinReportNewMsgAckResponseVO.TaskAck taskAck = WetoolWexinReportNewMsgMapStruct.INSTANCE.transferToResponseVO(output.getData());
        responseVO.setData(taskAck);
        return responseVO;
    }
}
