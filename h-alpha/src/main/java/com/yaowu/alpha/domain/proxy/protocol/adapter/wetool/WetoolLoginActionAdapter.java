package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.LoginNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinLoginRequestDTO;
import com.yaowu.alpha.model.vo.proxy.LoginAckVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinLoginAckResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolAckConstants;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinLoginMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信登录
 */
@Component
public class WetoolLoginActionAdapter extends AbstractWetoolActionAdapter<WetoolWexinLoginRequestDTO, LoginNoticeRequestDTO, LoginAckVO, WetoolWexinLoginAckResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.LOGIN.equals(action);
    }

    @Override
    public LoginNoticeRequestDTO transferRequest(WetoolWexinLoginRequestDTO input) {
        return WetoolWexinLoginMapStruct.INSTANCE.toLoginRequestDTO(input);
    }

    @Override
    public WetoolWexinLoginAckResponseVO transferResponse(LoginAckVO ouput) {
        WetoolWexinLoginAckResponseVO responseVO = new WetoolWexinLoginAckResponseVO();
        super.transferSuccessResponse(responseVO,WetoolAckConstants.LOGIN_ACK);
        WetoolWexinLoginAckResponseVO.LoginAck data = new WetoolWexinLoginAckResponseVO.LoginAck();
        WetoolWexinLoginAckResponseVO.LoginAckOption option =WetoolWexinLoginMapStruct.INSTANCE.toLoginResponseVo(ouput);
        data.setOption(option);
        responseVO.setData(data);
        return responseVO;
    }

}
