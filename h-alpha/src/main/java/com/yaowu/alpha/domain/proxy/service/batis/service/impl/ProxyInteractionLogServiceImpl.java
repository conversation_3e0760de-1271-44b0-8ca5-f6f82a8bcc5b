package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyInteractionLogMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyInteractionLogService;
import com.yaowu.alpha.model.entity.proxy.ProxyInteractionLog;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代理账户互动日志
 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
public class ProxyInteractionLogServiceImpl extends ServiceImpl<ProxyInteractionLogMapper, ProxyInteractionLog> implements IProxyInteractionLogService {

}
