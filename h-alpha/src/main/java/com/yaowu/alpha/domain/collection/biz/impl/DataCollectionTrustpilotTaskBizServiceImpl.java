package com.yaowu.alpha.domain.collection.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionTrustpilotTaskBizService;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTrustpilotRawDataService;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTrustpilotTaskService;
import com.yaowu.alpha.enums.collection.DataCollectionTrustpilotAiAnalysisStatusEnum;
import com.yaowu.alpha.enums.collection.DataCollectionTrustpilotTaskStatusEnum;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotRawDataCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotRawDataUpdateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotTaskCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotTaskUpdateDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotRawData;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotTask;
import com.yaowu.alpha.model.vo.collection.DataCollectionTrustpilotRawDataVO;
import com.yaowu.alpha.model.vo.collection.DataCollectionTrustpilotTaskVO;
import com.yaowu.alpha.utils.convertor.collection.DataCollectionTrustpilotConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Trustpilot数据采集任务业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
public class DataCollectionTrustpilotTaskBizServiceImpl implements IDataCollectionTrustpilotTaskBizService {

    @Autowired
    private IDataCollectionTrustpilotTaskService dataCollectionTrustpilotTaskService;
    @Autowired
    private IDataCollectionTrustpilotRawDataService dataCollectionTrustpilotRawDataService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTask(DataCollectionTrustpilotTaskCreateDTO dto) {
        // 使用MapStruct转换DTO为Entity
        DataCollectionTrustpilotTask task = DataCollectionTrustpilotConverter.INSTANCE.taskCreateDtoToEntity(dto);

        // 设置默认状态为待执行
        task.setTaskStatus(DataCollectionTrustpilotTaskStatusEnum.PENDING);

        // 保存任务
        dataCollectionTrustpilotTaskService.save(task);
        log.info("成功创建Trustpilot数据采集任务，ID：{}", task.getId());
        return task.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCollectionTrustpilotTaskVO pullNextTask() {
        // 查询待执行且到达预期执行时间的任务
        LambdaQueryWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getTaskStatus, DataCollectionTrustpilotTaskStatusEnum.PENDING.getValue())
                .le(DataCollectionTrustpilotTask::getScheduledStartTime, LocalDateTime.now())
                .orderByAsc(DataCollectionTrustpilotTask::getScheduledStartTime)
                .last("LIMIT 1");

        DataCollectionTrustpilotTask task = dataCollectionTrustpilotTaskService.getOne(wrapper);

        if (task != null) {
            log.info("拉取到待执行任务，ID：{}", task.getId());
            return DataCollectionTrustpilotConverter.INSTANCE.entityToVO(task);
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTask(DataCollectionTrustpilotTaskUpdateDTO dto) {
        DataCollectionTrustpilotTask task = getAndValidateTask(dto.getId());

        // 更新任务信息
        LambdaUpdateWrapper<DataCollectionTrustpilotTask> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DataCollectionTrustpilotTask::getId, dto.getId());

        if (dto.getTaskStatus() != null) {
            wrapper.set(DataCollectionTrustpilotTask::getTaskStatus, dto.getTaskStatus());
        }
        if (dto.getStartTime() != null) {
            wrapper.set(DataCollectionTrustpilotTask::getStartTime, dto.getStartTime());
        }
        if (dto.getEndTime() != null) {
            wrapper.set(DataCollectionTrustpilotTask::getEndTime, dto.getEndTime());
        }
        if (dto.getErrorMessage() != null) {
            wrapper.set(DataCollectionTrustpilotTask::getErrorMessage, dto.getErrorMessage());
        }

        boolean result = dataCollectionTrustpilotTaskService.update(wrapper);
        if (result) {
            log.info("成功更新任务信息，ID：{}", dto.getId());
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long reportRawData(DataCollectionTrustpilotRawDataCreateDTO dto) {
        // 判断公司是否存在
        DataCollectionTrustpilotRawData rawData = dataCollectionTrustpilotRawDataService.getByTaskIdAndCompanyName(dto.getTaskId(), dto.getCompanyName());
        if (rawData != null) {
            log.info("该数据已存在，ID: {}. 任务ID：{}，公司名称：{}", rawData.getId(), dto.getTaskId(), dto.getCompanyName());
            return rawData.getId();
        }
        // 使用MapStruct转换DTO为Entity
        rawData = DataCollectionTrustpilotConverter.INSTANCE.rawDataCreateDtoToEntity(dto);

        // 设置默认AI分析状态为未分析
        rawData.setAiAnalysisStatus(DataCollectionTrustpilotAiAnalysisStatusEnum.NOT_ANALYZED);

        // 保存原始数据
        dataCollectionTrustpilotRawDataService.save(rawData);
        log.info("成功上报原始数据，ID：{}，任务ID：{}", rawData.getId(), dto.getTaskId());
        return rawData.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCollectionTrustpilotRawDataVO getNextAiAnalysisData() {
        // 查询待分析的原始数据
        LambdaQueryWrapper<DataCollectionTrustpilotRawData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionTrustpilotRawData::getAiAnalysisStatus, DataCollectionTrustpilotAiAnalysisStatusEnum.NOT_ANALYZED.getValue())
                .orderByAsc(DataCollectionTrustpilotRawData::getCreateTime)
                .last("LIMIT 1");

        DataCollectionTrustpilotRawData rawData = dataCollectionTrustpilotRawDataService.getOne(wrapper);

        if (rawData != null) {
            log.info("获取到待分析数据，ID：{}", rawData.getId());
            return DataCollectionTrustpilotConverter.INSTANCE.rawDataEntityToVO(rawData);
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAiAnalysisResult(DataCollectionTrustpilotRawDataUpdateDTO dto) {
        DataCollectionTrustpilotRawData rawData = getAndValidateRawData(dto.getId());

        if (dto.getAiAnalysisStatus() != null) {
            rawData.setAiAnalysisStatus(dto.getAiAnalysisStatus());
        }
        if (dto.getAiAnalysisTime() != null) {
            rawData.setAiAnalysisTime(dto.getAiAnalysisTime());
        }
        if (dto.getAiAnalysisErrorMessage() != null) {
            rawData.setAiAnalysisResult(dto.getAiAnalysisErrorMessage());
        }
        if (dto.getAiAnalysisResult() != null) {
            rawData.setAiAnalysisResult(dto.getAiAnalysisResult());
        }
        boolean result = dataCollectionTrustpilotRawDataService.updateById(rawData);
        if (result) {
            log.info("成功更新AI分析结果，ID：{}", dto.getId());
        }
        return result;
    }

    /**
     * 获取并验证任务
     */
    private DataCollectionTrustpilotTask getAndValidateTask(Long id) {
        DataCollectionTrustpilotTask task = dataCollectionTrustpilotTaskService.getById(id);
        if (task == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "Trustpilot数据采集任务不存在");
        }
        return task;
    }

    /**
     * 获取并验证原始数据
     */
    private DataCollectionTrustpilotRawData getAndValidateRawData(Long id) {
        DataCollectionTrustpilotRawData rawData = dataCollectionTrustpilotRawDataService.getById(id);
        if (rawData == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "Trustpilot原始数据不存在");
        }
        return rawData;
    }
} 