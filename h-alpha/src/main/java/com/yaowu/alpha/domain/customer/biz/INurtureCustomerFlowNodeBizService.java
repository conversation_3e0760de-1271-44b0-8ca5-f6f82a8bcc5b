package com.yaowu.alpha.domain.customer.biz;

import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;

/**
 * 培育客户流程节点业务服务接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface INurtureCustomerFlowNodeBizService {

    /**
     * 创建首个节点
     *
     * @param flow 流程信息
     */
    void createFirstNode(NurtureCustomerFlow flow);

    /**
     * 执行节点
     * @param maxSize
     */
    void executeNode(Long maxSize);

    /**
     * 处理过期节点（无回复处理）
     * 
     * @param maxSize 最大处理数量
     */
    void processExpiredNodes(Long maxSize);

    /**
     * 处理用户回复的培育流程
     * 判断用户是否在培育流程中，及是否有最新的在等待回复且未过期的节点
     * 基于用户最后发言的消息id回溯拿到最近一条ai发送消息及之后的用户回复消息
     * 拼接上下文调用LLM推断用户回复类型，根据返回的回复编码调用AbstractNurtureCustomerFlowNodeProcessor#processReply
     * 
     * @param proxyAccount 代理账号
     * @param userMessage 用户消息
     * @param friend 好友信息
     */
    void processNurtureCustomerReply(ProxyAccount proxyAccount, ProxyChatMessage userMessage, ProxyAccountFriend friend);
}
