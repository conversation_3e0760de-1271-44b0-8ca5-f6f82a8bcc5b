package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.collection.CollUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BasePage;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.remote.IHeraRemoteBizService;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.heraapi.feign.clue.IRemoteClueTicketInfoFeign;
import com.yaowu.heraapi.feign.content.IRemoteContentFeign;
import com.yaowu.heraapi.feign.mtl.IRemoteAgentServiceFeign;
import com.yaowu.heraapi.feign.mtl.IRemoteDeviceServiceFeign;
import com.yaowu.heraapi.feign.mtl.IRemoteFloorCardFeign;
import com.yaowu.heraapi.feign.omk.IRemoteOmkClueServiceFeign;
import com.yaowu.heraapi.model.dto.association.RemoteIdDTO;
import com.yaowu.heraapi.model.dto.clue.ClueTicketInfoDTO;
import com.yaowu.heraapi.model.dto.common.RemoteIdsDTO;
import com.yaowu.heraapi.model.dto.content.*;
import com.yaowu.heraapi.model.dto.mtl.RemoteCategoryConditionDTO;
import com.yaowu.heraapi.model.dto.mtl.agent.RemoteAgentSubmitMtlLeadInfoDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkGeneralCustomerAddDTO;
import com.yaowu.heraapi.model.dto.schedule.PageQueryClueTicketDTO;
import com.yaowu.heraapi.model.vo.clue.ClueTicketVO;
import com.yaowu.heraapi.model.vo.content.RemoteContentTagVO;
import com.yaowu.heraapi.model.vo.content.RemoteFloorCardVO;
import com.yaowu.heraapi.model.vo.content.RemoteSimpleContentDetailVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteLeadsSubmitVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteWebCategoryDetailVO;
import com.yaowu.heraapi.model.vo.omk.RemoteClueTicketVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/2 21:09
 */
@Component
@RequiredArgsConstructor
public class HeraRemoteBizServiceImpl implements IHeraRemoteBizService {

    private final IRemoteClueTicketInfoFeign remoteClueTicketInfoFeign;

    private final IRemoteAgentServiceFeign remoteAgentServiceFeign;

    private final IRemoteDeviceServiceFeign remoteDeviceServiceFeign;

    private final IRemoteOmkClueServiceFeign remoteOmkClueServiceFeign;

    private final IRemoteFloorCardFeign remoteFloorCardFeign;

    private final IRemoteContentFeign remoteContentFeign;

    @Override
    public Boolean addGeneralCustomerInfo(RemoteOmkGeneralCustomerAddDTO remoteDTO) {
        return FeignInvokeUtils.convert(remoteOmkClueServiceFeign.addGeneralCustomerInfo(remoteDTO), Boolean.class);
    }

    @Override
    public RemoteClueTicketVO pushClueTicket(ClueTicketInfoDTO remoteDTO) {
        return FeignInvokeUtils.convert(remoteClueTicketInfoFeign.pushClueTicket(remoteDTO), RemoteClueTicketVO.class);
    }

    @Override
    public BasePage<ClueTicketVO> pageQueryClueTicket(PageQueryClueTicketDTO queryClueTicketDTO) {
        return FeignInvokeUtils.convertPage(remoteClueTicketInfoFeign.pageQueryClueTicket(queryClueTicketDTO), ClueTicketVO.class);
    }

    @Override
    public RemoteLeadsSubmitVO submitMtlLeadsInfo(RemoteAgentSubmitMtlLeadInfoDTO remoteDTO) {
        return FeignInvokeUtils.convert(remoteAgentServiceFeign.submitMtlLeadsInfo(remoteDTO), RemoteLeadsSubmitVO.class);
    }

    public BasePage<RemoteFloorCardVO> floorCardPage(RemoteFloorCardQueryDTO dto) {
        return FeignInvokeUtils.convertPage(remoteFloorCardFeign.page(dto), RemoteFloorCardVO.class);
    }

    public Long addFloorCard(RemoteFloorCardAddDTO dto) {
        return FeignInvokeUtils.convert(remoteFloorCardFeign.addFloorCard(dto), Long.class);
    }

    @Override
    public boolean deleteFloorCard(Long id) {
        RemoteIdDTO dto = new RemoteIdDTO();
        dto.setId(id);
        return FeignInvokeUtils.convert(remoteFloorCardFeign.deleteFloorCard(dto), Boolean.class);
    }

    @Override
    public boolean modifyFloorCard(RemoteFloorCardModifyDTO dto) {
        return FeignInvokeUtils.convert(remoteFloorCardFeign.modifyFloorCard(dto), Boolean.class);
    }

    @Override
    public boolean sortFloorCard(RemoteFloorCardSortDTO dto) {
        return FeignInvokeUtils.convert(remoteFloorCardFeign.sortFloorCard(dto), Boolean.class);
    }

    @Override
    public List<RemoteFloorCardVO> listFloorCard(RemoteFloorCardQueryDTO dto) {
        BaseResult<List<RemoteFloorCardVO>> listBaseResult = remoteFloorCardFeign.listQuery(dto);
        return FeignInvokeUtils.convertList(listBaseResult, RemoteFloorCardVO.class);
    }

    @Override
    public RemoteFloorCardVO getFloorCard(Long id) {
        if (id == null) {
            return null;
        }
        RemoteFloorCardQueryDTO dto = new RemoteFloorCardQueryDTO();
        dto.setIds(CollUtil.newArrayList(id));
        List<RemoteFloorCardVO> remoteFloorCardVOS = listFloorCard(dto);
        return CollUtil.isEmpty(remoteFloorCardVOS) ? null : remoteFloorCardVOS.get(0);
    }

    public boolean viewFloorCard(RemoteFloorCardViewedDTO dto) {
        return FeignInvokeUtils.convert(remoteFloorCardFeign.viewFloorCard(dto), Boolean.class);
    }

    @Override
    public Map<Long, RemoteSimpleContentDetailVO> batchGetSimpleContentDetail(List<Long> contentIds) {
        // 判空，空返回
        if (CollUtil.isEmpty(contentIds)) {
            return new HashMap<>(0);
        }
        RemoteIdsDTO dto = new RemoteIdsDTO();
        dto.setIds(contentIds);
        BaseResult<List<RemoteSimpleContentDetailVO>> listBaseResult = remoteContentFeign.batchGetSimpleContentDetail(dto);
        List<RemoteSimpleContentDetailVO> result = FeignInvokeUtils.convertList(listBaseResult, RemoteSimpleContentDetailVO.class);
        return StreamUtil.of(result).collect(Collectors.toMap(RemoteSimpleContentDetailVO::getId, Function.identity(), (o1, o2) -> o1));
    }

    @Override
    public List<RemoteContentTagVO> contentTags(RemoteContentTagsQueryDTO dto) {
        BaseResult<List<RemoteContentTagVO>> listBaseResult = remoteContentFeign.contentTags(dto);
        return FeignInvokeUtils.convertList(listBaseResult, RemoteContentTagVO.class);
    }

    @Override
    public List<RemoteWebCategoryDetailVO> getCategoryByCondition(RemoteCategoryConditionDTO dto) {
        final BaseResult<List<RemoteWebCategoryDetailVO>> baseResult = remoteDeviceServiceFeign.getCategoryByCondition(dto);
        return FeignInvokeUtils.convertList(baseResult, RemoteWebCategoryDetailVO.class);
    }

}
