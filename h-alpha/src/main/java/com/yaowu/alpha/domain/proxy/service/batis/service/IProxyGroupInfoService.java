package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.group.GroupPageDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 群信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyGroupInfoService extends IService<ProxyGroupInfo> {

    /**
     * 查询群信息
     *
     * @param proxyAccountId 代理账户配置项id
     */
    List<ProxyGroupInfo> listByProxyAccountId(Long proxyAccountId);


    /**
     * 根据群聊id查询群信息
     *
     * @param groupProxyId 群聊id
     */
    ProxyGroupInfo getByAccountIdAndGroupProxyId(Long accountId, String groupProxyId);

    /**
     * 根据群名称查询群信息
     */
    List<ProxyGroupInfo> getByGroupName(Long accountId, String groupName);


    /**
     * 移除已经删掉的群聊
     */
    void removeGroups(Long proxyAccountId, Collection<String> needDeleteGroupIds);

    Page<ProxyGroupInfo> pageGroup(GroupPageDTO dto);


    /**
     * 根据群聊ids查询群信息
     */
    List<ProxyGroupInfo> listByGroupProxyIds(Set<String> groupProxyIds);

    Map<String, ProxyGroupInfo> getByGroupProxyIds(List<String> groupProxyIds);
}
