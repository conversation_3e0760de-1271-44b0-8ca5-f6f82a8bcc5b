package com.yaowu.alpha.domain.collection.biz;

import com.yaowu.alpha.model.bo.collection.DataCollectionInstruction;
import com.yaowu.alpha.model.bo.collection.GmapDataCollectionParam;
import com.yaowu.alpha.model.dto.collection.DataCollectionInstructionReportDTO;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 数据采集任务指令业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IDataCollectionInstructionBizService {

    /**
     * 创建GMap数据采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @param gmapParam GMap数据采集参数
     * @return 指令任务ID
     */
    Long createGMapDataCollectionInstruction(Long collectionTaskId, GmapDataCollectionParam gmapParam);

    /**
     * 创建GMap数据采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param gmapParam GMap数据采集参数
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    Long createGMapDataCollectionInstruction(Long collectionTaskId, GmapDataCollectionParam gmapParam,
                                           LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit);

    /**
     * 创建暂停采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    Long createPauseInstruction(Long collectionTaskId);

    /**
     * 创建暂停采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    Long createPauseInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit);

    /**
     * 创建恢复采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    Long createResumeInstruction(Long collectionTaskId);

    /**
     * 创建恢复采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    Long createResumeInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit);

    /**
     * 创建终止采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    Long createTerminateInstruction(Long collectionTaskId);

    /**
     * 创建终止采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    Long createTerminateInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit);

    /**
     * 获取并处理一个待执行的GMap数据采集任务（需要分布式锁）
     * 注意：此方法会获取分布式锁，确保多机部署时不重复执行
     *
     * @return 数据采集指令，如果没有待执行任务则返回null
     */
    DataCollectionInstruction pullOnePendingGMapTask();

    /**
     * 获取并处理一个待执行的采集任务控制指令（不需要分布式锁）
     * 用于处理暂停、恢复、终止等控制指令
     *
     * @return 数据采集指令，如果没有待执行任务则返回null
     */
    DataCollectionInstruction pullPendingControlInstruction(Long collectionTaskId);

    /**
     * 处理客户端上报的指令执行结果
     * 处理指令执行结果，更新任务状态并发布事件
     *
     * @param reportDTO 指令执行结果上报DTO
     */
    void handleInstructionResult(DataCollectionInstructionReportDTO reportDTO);

} 