package com.yaowu.alpha.domain.collection.service.batis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaowu.alpha.model.bo.collection.TaskDataCountBO;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据采集原始数据集
 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface DataCollectionRawMapper extends BaseMapper<DataCollectionRawData> {

    /**
     * 批量统计任务的采集数据数量
     * 
     * @param taskIds 任务ID列表
     * @return 任务数据统计结果列表
     */
    List<TaskDataCountBO> countByTaskIds(@Param("taskIds") List<Long> taskIds);
}
