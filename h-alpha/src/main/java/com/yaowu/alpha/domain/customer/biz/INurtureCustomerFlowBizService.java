package com.yaowu.alpha.domain.customer.biz;

import com.yaowu.alpha.model.dto.customer.FlowTerminateDTO;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerFlowBatchCreateDTO;

/**
 * 培育客户流程业务服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface INurtureCustomerFlowBizService {

    /**
     * 批量创建培育客户流程
     *
     * @param dto 批量创建请求参数
     * @return 批量创建结果
     */
    void batchCreateNurtureFlow(NurtureCustomerFlowBatchCreateDTO dto);


    /**
     * 终止培育流程
     *
     * @param dto 终止流程请求参数
     */
    void terminateFlow(FlowTerminateDTO dto);

    /**
     * 启动培育流程
     */
    void startFlow();


}
