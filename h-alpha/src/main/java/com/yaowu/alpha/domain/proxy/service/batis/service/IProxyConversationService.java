package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.proxy.ProxyConversationQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyConversation;

import java.util.List;

/**
 * <p>
 * 消息会话表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface IProxyConversationService extends IService<ProxyConversation> {

    /**
     * 根据条件分页查询代理会话
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<ProxyConversation> pageByCondition(ProxyConversationQueryDTO queryDTO);

    List<ProxyConversation> listByCondition(ProxyConversationQueryDTO queryDTO);
}
