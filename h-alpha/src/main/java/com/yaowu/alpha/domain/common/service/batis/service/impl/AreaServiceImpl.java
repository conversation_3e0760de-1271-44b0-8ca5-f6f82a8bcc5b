package com.yaowu.alpha.domain.common.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.AreaMapper;
import com.yaowu.alpha.domain.common.service.batis.service.IAreaService;
import com.yaowu.alpha.model.entity.common.Area;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地区服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area> implements IAreaService {

    @Override
    public List<Area> getCitiesByCountryAreaId(Long countryAreaId) {
        List<Area> areas = baseMapper.selectCitiesByCountryAreaId(countryAreaId);
        if (CollUtil.isEmpty(areas)) {
            areas = this.lambdaQuery()
                    .eq(Area::getParentId, countryAreaId)
                    .orderByAsc(Area::getAreaId)
                    .list();
        }
        return areas;
    }

    @Override
    public List<Area> getStatesByCountryAreaId(Long countryAreaId) {
        return lambdaQuery()
            .eq(Area::getParentId, countryAreaId)
            .orderByAsc(Area::getChineseName)
            .list();
    }

    @Override
    public List<Area> getCitiesByStateAreaId(Long stateAreaId) {
        return lambdaQuery()
            .eq(Area::getParentId, stateAreaId)
            .orderByAsc(Area::getChineseName)
            .list();
    }

    @Override
    public List<Area> getChildrenByParentAreaId(Long parentAreaId) {
        return lambdaQuery()
            .eq(Area::getParentId, parentAreaId)
            .orderByAsc(Area::getChineseName)
            .list();
    }
}
