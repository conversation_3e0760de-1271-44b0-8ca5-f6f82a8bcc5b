package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.QueryWhatsappMsgStatusNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinQueryWhatsappMsgStatusRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolQueryWhatsappMsgStatusMapStruct;
import org.springframework.stereotype.Component;

/**
 * 查询WhatsApp消息状态适配器
 * <AUTHOR>
 */
@Component
public class WetoolQueryWhatsappMsgStatusAdapter extends AbstractWetoolActionAdapter<WetoolWexinQueryWhatsappMsgStatusRequestDTO, QueryWhatsappMsgStatusNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.QUERY_WHATSAPP_MSG_STATUS.equals(action);
    }

    @Override
    public QueryWhatsappMsgStatusNoticeRequestDTO transferRequest(WetoolWexinQueryWhatsappMsgStatusRequestDTO input) {
        return WetoolQueryWhatsappMsgStatusMapStruct.INSTANCE.toQueryWhatsappMsgStatusRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }
}
