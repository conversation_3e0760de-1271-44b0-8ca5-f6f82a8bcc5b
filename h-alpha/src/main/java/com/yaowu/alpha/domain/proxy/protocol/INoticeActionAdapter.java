package com.yaowu.alpha.domain.proxy.protocol;

import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;

public interface INoticeActionAdapter<T1, T2 extends BaseNoticeRequestDTO, R1 extends NoticeBaseResponseVO, R2> {

    /**
     * 是否支持该操作
     *
     * @param proxyType
     * @param thirdParty
     * @param action
     * @return
     */
    Boolean supportAction(ProxyTypeEnum proxyType, ProxyThirdTypeEnum thirdParty, String action);

    /**
     * 转换输入参数
     *
     * @param input
     * @return
     */
    T2 transferRequest(T1 input);

    /**
     * 转换输出参数
     *
     * @param output
     * @return
     */
    R2 transferResponse(R1 output);
}
