package com.yaowu.alpha.domain.common.biz.impl;

import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.biz.ISettleRemoteBizService;
import com.yaowu.settle.api.fallback.feign.IRemotePromoterAccountServiceFeign;
import com.yaowu.settle.api.fallback.feign.mtl.aggregation.IThirdPartyPaymentAggregationServiceFeign;
import com.yaowu.settle.api.fallback.feign.mtl.withdrawal.IRemoteWithdrawalRecordServiceFeign;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteSubmitThirdPartyPaymentDTO;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteThirdPartyPaymentRecordQueryDTO;
import com.yaowu.settle.api.model.mtl.dto.withdrawal.RemoteWithdrawalRecordQueryDTO;
import com.yaowu.settle.api.model.mtl.vo.aggregation.RemoteSubmitThirdPartyPaymentVO;
import com.yaowu.settle.api.model.mtl.vo.aggregation.RemoteThirdPartyPaymentRecordVO;
import com.yaowu.settle.api.model.mtl.vo.withdrawal.RemoteWithdrawalRecordVO;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/9 16:31
 */
@Service
public class SettleRemoteBizServiceImpl implements ISettleRemoteBizService {

    @Resource
    private IRemotePromoterAccountServiceFeign remotePromoterAccountServiceFeign;
    @Resource
    private IRemoteWithdrawalRecordServiceFeign remoteWithdrawalRecordServiceFeign;

    @Autowired
    private IThirdPartyPaymentAggregationServiceFeign thirdPartyPaymentAggregationServiceFeign;

    @Override
    public List<RemoteWithdrawalRecordVO> listWithdrawalRecords(RemoteWithdrawalRecordQueryDTO dto) {
        List<RemoteWithdrawalRecordVO> remoteWithdrawalRecordVOS = FeignInvokeUtils.convertList(remoteWithdrawalRecordServiceFeign.listWithdrawalRecords(dto), RemoteWithdrawalRecordVO.class);
        return remoteWithdrawalRecordVOS;
    }

    @Override
    public RemoteSubmitThirdPartyPaymentVO submitPayment(RemoteSubmitThirdPartyPaymentDTO dto) {
        BaseResult<RemoteSubmitThirdPartyPaymentVO> result = thirdPartyPaymentAggregationServiceFeign.submitPayment(dto);
        return FeignInvokeUtils.convert(result, RemoteSubmitThirdPartyPaymentVO.class);
    }

    @Override
    public RemoteThirdPartyPaymentRecordVO getPreSubmitPaymentRecord(RemoteThirdPartyPaymentRecordQueryDTO dto) {
        BaseResult<RemoteThirdPartyPaymentRecordVO> result = thirdPartyPaymentAggregationServiceFeign.getPreSubmitPaymentRecord(dto);
        return FeignInvokeUtils.convert(result, RemoteThirdPartyPaymentRecordVO.class);
    }

    @Override
    public RemoteThirdPartyPaymentRecordVO getPreSubmitPaymentRecordById(Long id) {
        RemoteThirdPartyPaymentRecordQueryDTO dto = new RemoteThirdPartyPaymentRecordQueryDTO();
        dto.setPrePaymentRecordId(id);
        return getPreSubmitPaymentRecord(dto);
    }


}
