package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportNewFriendNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.ReportNewMsgAckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 新好友消息通知
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportNewFriendNoticeActionProcessor implements INoticeActionProcessor<ReportNewFriendNoticeRequestDTO, ReportNewMsgAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyAccountFriendBizService friendBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportNewFriendNoticeRequestDTO;
    }


    @Override
    public ReportNewMsgAckVO process(ReportNewFriendNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return ReportNewMsgAckVO.emptyAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-新好友消息通知-消息：{}", JacksonUtils.toJsonStr(request));
        // 新增好友处理
        friendBizService.newFriend(request, accountConfig);

        // 空响应
        return ReportNewMsgAckVO.emptyAck(request);
    }

}
