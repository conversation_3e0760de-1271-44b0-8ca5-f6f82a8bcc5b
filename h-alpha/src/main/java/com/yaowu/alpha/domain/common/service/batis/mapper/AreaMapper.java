package com.yaowu.alpha.domain.common.service.batis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaowu.alpha.model.entity.common.Area;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface AreaMapper extends BaseMapper<Area> {

    /**
     * 查询指定国家下的所有城市
     * 通过两级子查询：先查国家下的直接子区域（州/省），再查这些子区域下的城市
     *
     * @param countryAreaId 国家区域ID
     * @return 城市列表
     */
    List<Area> selectCitiesByCountryAreaId(@Param("countryAreaId") Long countryAreaId);
}
