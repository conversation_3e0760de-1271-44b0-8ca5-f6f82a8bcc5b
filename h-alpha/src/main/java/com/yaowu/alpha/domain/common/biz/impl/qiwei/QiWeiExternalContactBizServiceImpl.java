package com.yaowu.alpha.domain.common.biz.impl.qiwei;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.config.wetool.WorkWxConfig;
import com.yaowu.alpha.model.bo.proxy.qw.QwExternalUserDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Slf4j
@Service
public class QiWeiExternalContactBizServiceImpl extends AbstractIQiWeiBizServiceImpl {
    private final WorkWxConfig workWxConfig;
    private static final String QW_EXTERNALCONTACT = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list";
    private static final String QW_EXTERNALCONTACT_DETAIL = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get";
    private static final String QW_EXTERNALCONTACT_REMARK = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/remark";

    @Override
    public List<String> getExternalContact(String workWxUserId) {
        String token = getAccessToken(workWxConfig.getCorpid(), workWxConfig.getCorpsecret());
        if (!StringUtils.hasText(token)) {
            log.error("获取企微token失败");
            return CollUtil.newArrayList();
        }

        Map<String, String> params = new HashMap<>();
        params.put("accessToken", token);
        params.put("userId", workWxUserId);
        log.info("获取企微外部联系人列表:{}", JSONUtil.toJsonStr(params));
        try {
            JSONObject jsonObj = outerRestTemplate.getForEntity(QW_EXTERNALCONTACT + "?access_token={accessToken}&userid={userId}", JSONObject.class, params)
                    .getBody();
            // log.info("获取企微外部联系人列表结果：{}", JSONUtil.toJsonStr(jsonObj));
            if (jsonObj != null && jsonObj.getInt("errcode").equals(0)) {
                List<String> externalUseridList = jsonObj.getJSONArray("external_userid").toList(String.class);
                return externalUseridList;
            }
        } catch (Exception e) {
            log.error("获取企微外部联系人列表异常", e);
        }
        return CollUtil.newArrayList();
    }

    @Override
    public QwExternalUserDetail getExternalContactDetail(String workWxUserId, String externalUserid) {
        String token = getAccessToken(workWxConfig.getCorpid(), workWxConfig.getCorpsecret());
        if (!StringUtils.hasText(token)) {
            log.error("获取企微token失败");
            return null;
        }
        Map<String, String> params = new HashMap<>();
        params.put("accessToken", token);
        params.put("externalUserid", externalUserid);
        log.info("获取企微外部联系人详情:{}", JSONUtil.toJsonStr(params));
        try {
            JSONObject jsonObj = outerRestTemplate.getForEntity(QW_EXTERNALCONTACT_DETAIL + "?access_token={accessToken}&external_userid={externalUserid}", JSONObject.class, params)
                    .getBody();
            log.info("获取企微外部联系人详情:{}", JSONUtil.toJsonStr(jsonObj));
            if (jsonObj != null && jsonObj.getInt("errcode").equals(0)) {
                QwExternalUserDetail qwExternalUserDetail = jsonObj.get("external_contact", QwExternalUserDetail.class);
                return qwExternalUserDetail;
            }
        } catch (Exception e) {
            log.error("获取企微外部联系人详情异常", e);
        }
        return null;
    }

    @Override
    public Boolean updateExternalUserDescription(String workWxUserId, String externalUserid, String description) {
        String token = getAccessToken(workWxConfig.getCorpid(), workWxConfig.getCorpsecret());
        if (!StringUtils.hasText(token)) {
            log.error("获取企微token失败");
            return false;
        }
        Map<String, String> params = new HashMap<>();
        params.put("userid", workWxUserId);
        params.put("external_userid", externalUserid);
        params.put("description", description);
        log.info("更新企微外部联系人描述:{}", JSONUtil.toJsonStr(params));
        try {
            JSONObject jsonObj = outerRestTemplate.postForObject(QW_EXTERNALCONTACT_REMARK + "?access_token=" + token, params, JSONObject.class);
            log.info("更新企微外部联系人描述:{}", JSONUtil.toJsonStr(jsonObj));
            if (jsonObj != null && jsonObj.getInt("errcode").equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("更新企微外部联系人描述异常", e);
        }
        return false;
    }
}
