package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyBatchTask;

import java.util.List;

/**
 * <p>
 * 代理批量任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyBatchTaskService extends IService<ProxyBatchTask> {

    List<ProxyBatchTask> pullUnFinishedTask(long limit);
}
