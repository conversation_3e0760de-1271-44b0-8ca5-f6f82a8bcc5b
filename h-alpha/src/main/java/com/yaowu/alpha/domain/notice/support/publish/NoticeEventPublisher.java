package com.yaowu.alpha.domain.notice.support.publish;

import com.yaowu.alpha.domain.notice.support.event.NoticeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 消息通知 事件发布
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/29
 */
@Component
public class NoticeEventPublisher {

    @Autowired
    private ApplicationEventPublisher publisher;

    public <T> void publish(T data) {
        publisher.publishEvent(new NoticeEvent<>(data));
    }
}
