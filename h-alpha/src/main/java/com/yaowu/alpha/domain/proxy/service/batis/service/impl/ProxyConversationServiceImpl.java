package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyConversationMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyConversationService;
import com.yaowu.alpha.model.dto.proxy.ProxyConversationQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyConversation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 消息会话表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Service
public class ProxyConversationServiceImpl extends ServiceImpl<ProxyConversationMapper, ProxyConversation> implements IProxyConversationService {

    @Override
    public Page<ProxyConversation> pageByCondition(ProxyConversationQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyConversation> wrapper = new LambdaQueryWrapper<>();
        
        // 会话ID列表条件
        if (CollUtil.isNotEmpty(queryDTO.getIds())) {
            wrapper.in(ProxyConversation::getId, queryDTO.getIds());
        }
        
        // 账号ID条件
        if (queryDTO.getAccountId() != null) {
            wrapper.eq(ProxyConversation::getAccountId, queryDTO.getAccountId());
        }
        
        // 账号ID列表条件
        if (CollUtil.isNotEmpty(queryDTO.getAccountIds())) {
            wrapper.in(ProxyConversation::getAccountId, queryDTO.getAccountIds());
        }
        
        // 最新消息时间范围条件
        if (queryDTO.getLatestMessageTimeStart() != null) {
            wrapper.ge(ProxyConversation::getLatestMessageTime, queryDTO.getLatestMessageTimeStart());
        }
        if (queryDTO.getLatestMessageTimeEnd() != null) {
            wrapper.le(ProxyConversation::getLatestMessageTime, queryDTO.getLatestMessageTimeEnd());
        }

        
        
        // 排序条件
        if (Boolean.TRUE.equals(queryDTO.getIsOrderByLatestMessageTimeDesc())) {
            wrapper.orderByDesc(ProxyConversation::getLatestMessageTime);
        }
        
        // 分页查询
        Page<ProxyConversation> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        return this.page(page, wrapper);
    }

    @Override
    public List<ProxyConversation> listByCondition(ProxyConversationQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyConversation> wrapper = buildQueryWrapper(queryDTO);
        if (wrapper.isEmptyOfWhere()) {
            return CollUtil.newArrayList();
        }
        return list(wrapper);
    }

    /**
     * 构建查询条件
     *
     * @param queryDTO 查询条件
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<ProxyConversation> buildQueryWrapper(ProxyConversationQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyConversation> wrapper = Wrappers.lambdaQuery();

        // 设置查询条件

        if (CollectionUtils.isNotEmpty(queryDTO.getIds())) {
            wrapper.in(ProxyConversation::getId, queryDTO.getIds());
        }

        if (Objects.nonNull(queryDTO.getAccountId())) {
            wrapper.eq(ProxyConversation::getAccountId, queryDTO.getAccountId());
        }

        if (CollectionUtils.isNotEmpty(queryDTO.getAccountIds())) {
            wrapper.in(ProxyConversation::getAccountId, queryDTO.getAccountIds());
        }

        if (CollectionUtils.isNotEmpty(queryDTO.getExternalConversationIds())) {
            wrapper.in(ProxyConversation::getExternalConversationId, queryDTO.getExternalConversationIds());
        }


        if (Objects.nonNull(queryDTO.getLatestMessageTimeStart())) {
            wrapper.ge(ProxyConversation::getLatestMessageTime, queryDTO.getLatestMessageTimeStart());
        }

        if (Objects.nonNull(queryDTO.getLatestMessageTimeEnd())) {
            wrapper.le(ProxyConversation::getLatestMessageTime, queryDTO.getLatestMessageTimeEnd());
        }

        // 设置排序
        if (Objects.nonNull(queryDTO.getIsOrderByLatestMessageTimeDesc()) && queryDTO.getIsOrderByLatestMessageTimeDesc()) {
            wrapper.orderByDesc(ProxyConversation::getLatestMessageTime);
        } else {
            wrapper.orderByDesc(ProxyConversation::getCreateTime);
        }

        return wrapper;
    }
}
