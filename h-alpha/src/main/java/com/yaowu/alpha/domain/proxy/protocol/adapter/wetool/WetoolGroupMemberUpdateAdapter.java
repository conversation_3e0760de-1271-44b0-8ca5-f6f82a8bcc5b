package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberUpdateNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportRoomMemberUpdateRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 上报群成员更新
 *
 * <AUTHOR>
 */
@Component
public class WetoolGroupMemberUpdateAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportRoomMemberUpdateRequestDTO, ReportRoomMemberUpdateNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_ROOM_MEMBER_UPDATE.equals(action);
    }

    @Override
    public ReportRoomMemberUpdateNoticeRequestDTO transferRequest(WetoolWexinReportRoomMemberUpdateRequestDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportGroupMemberUpdateRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
