package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.ProxyCustomerRequirementBizServiceImpl.RequirementQueryContext;
import com.yaowu.alpha.model.dto.clue.CluePageDTO;
import com.yaowu.alpha.model.dto.proxy.control.ProxyCustomerRequirementQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyCustomerRequirement;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 代理平台用户需求单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IProxyCustomerRequirementService extends IService<ProxyCustomerRequirement> {

    /**
     * 根据条件获取一条需求单
     *
     * @param queryDTO
     * @return
     */
    ProxyCustomerRequirement oneByCondition(ProxyCustomerRequirementQueryDTO queryDTO);

    /**
     * 根据条件获取需求单列表
     * @param queryDTO
     * @return
     */
    List<ProxyCustomerRequirement> listByCondition(ProxyCustomerRequirementQueryDTO queryDTO);

    /**
     * 获取最后一个未完成的需求单
     *
     * @param userProxyId
     * @param agentProxyId
     * @return
     */
    ProxyCustomerRequirement lastUnClosedOne(String userProxyId,
                                             String agentProxyId);

    /**
     * 获取最后一个未完成的需求单
     *
     * @param userProxyId
     * @param agentProxyId
     * @param phone
     * @param startTime
     * @return
     */
    ProxyCustomerRequirement lastUnClosedOne(String userProxyId,
                                             String agentProxyId,
                                             String phone, LocalDateTime startTime);



    /**
     * 保存分发目标信息
     *
     * @param id                代理平台用户需求单id
     * @param toChannel         分发目标渠道：1=电销，2=平台MTL
     * @param toChannelTicketId 分发目标id，电销线索工单id(h_hera.b_clue_ticket)、报价单线索信息表id(b_leads_info)
     */
    boolean saveToChannelInfo(Long id, Integer toChannel, Long toChannelTicketId);


    List<String> listFriendProxyIds();

    Page<ProxyCustomerRequirement> getRequirementPage(RequirementQueryContext context, CluePageDTO dto);

}
