package com.yaowu.alpha.domain.facebook.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.facebook.FacebookGroup;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook群组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookGroupService extends IService<FacebookGroup> {

    Map<Long, Integer> getGroupStatisticMap(List<Long> taskIds);

    Map<Long, FacebookGroup> getGroupMap(List<Long> groupIds);

    List<Long> listIdsByLikeGroupName(String groupName);
} 