package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.common.LangDictQueryDTO;
import com.yaowu.alpha.model.entity.common.LangDict;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22-14:42
 */
public interface ILangDictService extends IService<LangDict> {

    /**
     * 根据字典类型编码查询父级字典
     *
     * @param categoryCode
     * @return
     */
    LangDict getParentLangDict(String categoryCode);

    Page<LangDict> pageByCondition(LangDictQueryDTO dto);

    /**
     * 根据条件查询多条记录
     *
     * @param dto
     * @return
     */
    List<LangDict> listByCondition(LangDictQueryDTO dto);

    /**
     * 根据条件查询一条记录
     *
     * @param lang
     * @param categoryCode
     * @param fieldName
     * @return
     */
    LangDict getByCondition(String lang, String categoryCode, String fieldName);

    boolean remoteByParent(Long parent);
}
