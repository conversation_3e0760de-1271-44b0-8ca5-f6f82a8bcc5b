package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportUserInfoNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportUserInfoRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信任务执行成功通知转换处理器
 */
@Component
public class WetoolReportUserInfoAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportUserInfoRequestDTO, ReportUserInfoNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_USER_INFO.equals(action);
    }

    @Override
    public ReportUserInfoNoticeRequestDTO transferRequest(WetoolWexinReportUserInfoRequestDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportUserInfoRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        super.transferSuccessResponse(responseVO, null);
        return responseVO;
    }
}
