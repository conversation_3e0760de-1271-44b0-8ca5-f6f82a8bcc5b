package com.yaowu.alpha.domain.proxy.protocol.manager;

import com.yaowu.alpha.domain.proxy.protocol.INoticeActionAdapter;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.domain.proxy.protocol.factory.ProxyProtocolProcessorFactory;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.common.BaseCommonProxyRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
public class ProxyActionManager {

    @Resource
    private ProxyProtocolProcessorFactory proxyProtocolProcessorFactory;

    public <T extends BaseCommonProxyRequestDTO, R> R processAction(T t, String action) {
        return processAction(t, action, t.getProxyType(), t.getProxyThirdType());
    }

    public <T, R> R processAction(T t, String action, ProxyTypeEnum proxyType, ProxyThirdTypeEnum proxyThirdType) {
        INoticeActionAdapter actionAdapter = proxyProtocolProcessorFactory.findAdapterProcessor(proxyType, proxyThirdType, action);
        if (actionAdapter == null) {
            log.warn("未找到适配器处理器");
            return null;
        }
        BaseNoticeRequestDTO innerRequest = actionAdapter.transferRequest(t);
        INoticeActionProcessor actionProcessor = proxyProtocolProcessorFactory.findNoticeActionProcessor(innerRequest);
        if (actionProcessor == null) {
            log.warn("未找到处理器");
            return null;
        }
        NoticeBaseResponseVO processResult = actionProcessor.process(innerRequest);
        return (R)actionAdapter.transferResponse(processResult);
    }
}
