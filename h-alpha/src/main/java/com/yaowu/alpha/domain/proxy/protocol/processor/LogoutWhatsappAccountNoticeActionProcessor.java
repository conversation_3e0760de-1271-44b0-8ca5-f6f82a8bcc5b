package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.redis.utils.RedisHelper;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.enums.proxy.ProxyAuthStatusEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.LogoutWhatsappAccountNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountExtendInfo;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 登出WhatsApp账号处理器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LogoutWhatsappAccountNoticeActionProcessor implements INoticeActionProcessor<LogoutWhatsappAccountNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyAccountConfigService proxyAccountConfigService;

    private final RedisHelper redisHelper;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof LogoutWhatsappAccountNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(LogoutWhatsappAccountNoticeRequestDTO request) {
        // 1. 获取账号配置
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);

        // 卫语句：账号不存在直接返回
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }

        // 2. 记录日志
        logRequestInfo(accountConfig, request);

        // 3. 处理登出结果
        processLogoutResult(accountConfig, request);

        // 4. 返回响应
        return NoticeBaseResponseVO.commonAck(request);
    }

    /**
     * 记录请求信息日志
     */
    private void logRequestInfo(ProxyAccount accountConfig, LogoutWhatsappAccountNoticeRequestDTO request) {
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc + "代理-登出WhatsApp账号-消息：{}", JacksonUtils.toJsonStr(request));
    }

    /**
     * 处理登出结果
     */
    private void processLogoutResult(ProxyAccount accountConfig, LogoutWhatsappAccountNoticeRequestDTO request) {
        // 卫语句：登出失败直接返回
        if (request.getSuccess() == null || !request.getSuccess()) {
            log.warn("登出WhatsApp账号失败，账号ID：{}，消息：{}", accountConfig.getId(), request.getMessage());
            return;
        }
        
        // 记录账户信息
        if (request.getAccount() != null) {
            log.info("登出的账户信息 - ID：{}，UUID：{}，手机号：{}", 
                    request.getAccount().getId(), 
                    request.getAccount().getUuid(), 
                    request.getAccount().getPhone());
        }
        
        // 缓存登出状态
        String key = String.format("WHATSAPP_LOG_OUT:%s", accountConfig.getId());
        cacheLogoutStatus(key, Boolean.TRUE);
        
        // 成功登出，更新授权状态
        updateAuthorizationStatus(accountConfig);
        log.info("登出WhatsApp账号成功，账号ID：{}，消息：{}", accountConfig.getId(), request.getMessage());
    }

    /**
     * 缓存登出状态信息
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    private void cacheLogoutStatus(String key, Object value) {
        redisHelper.strSet(key, value);
        redisHelper.setExpire(key, 10L, TimeUnit.MINUTES);
    }

    /**
     * 更新授权状态
     * 将WhatsApp信息的授权状态修改为未授权
     */
    private void updateAuthorizationStatus(ProxyAccount accountConfig) {
        ProxyAccountExtendInfo extendInfo = accountConfig.getExtendInfo();

        // 卫语句：扩展信息或WhatsApp信息不存在直接返回
        if (extendInfo == null || extendInfo.getWhatsAppInfo() == null) {
            return;
        }

        // 更新授权状态并保存
        extendInfo.getWhatsAppInfo().setAuthStatus(ProxyAuthStatusEnum.PENDING.getValue());
        accountConfig.setExtendInfo(extendInfo);
        proxyAccountConfigService.updateById(accountConfig);
    }
}
