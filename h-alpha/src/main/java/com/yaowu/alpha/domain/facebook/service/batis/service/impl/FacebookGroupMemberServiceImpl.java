package com.yaowu.alpha.domain.facebook.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookGroupMemberMapper;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookGroupMemberService;
import com.yaowu.alpha.model.entity.facebook.FacebookGroupMember;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook群组成员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class FacebookGroupMemberServiceImpl extends ServiceImpl<FacebookGroupMemberMapper, FacebookGroupMember> implements FacebookGroupMemberService {

    @Override
    public Map<Long, Integer> getMemberStatisticMapByTaskIds(List<Long> taskIds) {
        // 参数校验
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }

        // 使用XML映射的SQL查询，高效统计每个任务的群组数量
        List<Map<String, Object>> statisticList = this.baseMapper.getGroupMemberStatisticByTaskIds(taskIds);

        // 转换为Map<Long, Integer>格式
        Map<Long, Integer> resultMap = new HashMap<>();
        for (Map<String, Object> item : statisticList) {
            Long taskId = (Long) item.get("taskId");
            Integer count = ((Number) item.get("count")).intValue();
            resultMap.put(taskId, count);
        }

        // 为没有群组的任务ID设置0
        for (Long taskId : taskIds) {
            resultMap.putIfAbsent(taskId, 0);
        }

        return resultMap;
    }

    @Override
    public Map<Long, Integer> getMemberStatisticMapByGroupIds(List<Long> groupIds) {
        // 参数校验
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<>();
        }

        // 使用XML映射的SQL查询，高效统计每个任务的群组数量
        List<Map<String, Object>> statisticList = this.baseMapper.getGroupMemberStatisticByGroupIds(groupIds);

        // 转换为Map<Long, Integer>格式
        Map<Long, Integer> resultMap = new HashMap<>();
        for (Map<String, Object> item : statisticList) {
            Long taskId = (Long) item.get("groupId");
            Integer count = ((Number) item.get("count")).intValue();
            resultMap.put(taskId, count);
        }

        for (Long groupId : groupIds) {
            resultMap.putIfAbsent(groupId, 0);
        }

        return resultMap;
    }
}