package com.yaowu.alpha.domain.facebook.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTask;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook搜索任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookSearchTaskService extends IService<FacebookSearchTask> {

    List<Long> listIdsByLikeTaskName(String taskName);

    Map<Long, FacebookSearchTask> getTaskMap(List<Long> taskIds);
} 