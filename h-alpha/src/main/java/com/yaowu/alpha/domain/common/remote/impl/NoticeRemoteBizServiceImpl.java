package com.yaowu.alpha.domain.common.remote.impl;

import com.freedom.feign.utils.FeignInvokeUtils;
import com.yaowu.alpha.domain.common.remote.INoticeRemoteBizService;
import com.yaowu.notice.api.v1.IRemoteShortLinkServiceFeign;
import com.yaowu.notice.model.dto.shortlink.RemoteGenerateShortLinkDTO;
import com.yaowu.notice.model.vo.shortlink.RemoteShortLinkVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/30 19:21
 */
@RequiredArgsConstructor
@Service
public class NoticeRemoteBizServiceImpl implements INoticeRemoteBizService {


    private final IRemoteShortLinkServiceFeign remoteShortLinkServiceFeign;

    /**
     * 生成短链接
     * @param longUrl
     * @param expireDays
     * @return
     */
    @Override
    public RemoteShortLinkVO generateShortLink(String longUrl, Integer expireDays) {
        RemoteGenerateShortLinkDTO dto = new RemoteGenerateShortLinkDTO();
        dto.setLongLink(longUrl);
        dto.setExpire((long) expireDays);

        return FeignInvokeUtils.convert(remoteShortLinkServiceFeign.generateShortLink(dto), RemoteShortLinkVO.class);
    }


}
