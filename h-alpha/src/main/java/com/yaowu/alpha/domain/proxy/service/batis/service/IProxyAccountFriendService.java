package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.bo.proxy.ProxyAccountFriendQuery;
import com.yaowu.alpha.model.dto.friend.PageFriendDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 托管账户好友列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyAccountFriendService extends IService<ProxyAccountFriend> {

    /**
     * 查询好友
     *
     * @param proxyAccountId 代理账户配置项id
     * @param friendProxyId  好友微信id
     */
    ProxyAccountFriend selectFriend(Long proxyAccountId, String friendProxyId);

    /**
     * 查询好友
     *
     * @param proxyAccountId 代理账户配置项id
     * @param friendProxyIds 好友微信id
     */
    List<ProxyAccountFriend> selectFriends(Long proxyAccountId, List<String> friendProxyIds);

    /**
     * 查询好友
     *
     * @param proxyAccountId 代理账户配置项id
     */
    List<ProxyAccountFriend> listByProxyAccountId(Long proxyAccountId);

    /**
     * 查询
     */
    List<ProxyAccountFriend> query(ProxyAccountFriendQuery query);

    /**
     * 查询好友
     *
     * @param proxyAccountId 代理账户配置项id
     * @param keyword        关键词，好友名称、手机号
     */
    List<ProxyAccountFriend> listByAccountIdAndKeyword(Long proxyAccountId, String keyword);

    /**
     * 移除已经删掉的好友
     */
    void removeFriends(Long proxyAccountId, Collection<String> needDeleteFriendIds);

    List<String> queryAllFriendProxyId(Long proxyAccountId);

    Page<ProxyAccountFriend> pageProxyFriend(PageFriendDTO dto);

    /**
     * 查询好友
     *
     * @param proxyAccountIds 代理账户配置项ids
     */
    List<ProxyAccountFriend> listByProxyAccountIds(Set<Long> proxyAccountIds);


    Map<String, ProxyAccountFriend> getByFriendProxyIds(List<String> friendProxyIds);

    List<ProxyAccountFriend> listByAccountFriends(List<Long> proxyAccountIds, Integer infoSyncFlag, Integer limit);
}
