package com.yaowu.alpha.domain.knowledge.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.knowledge.KnowledgePageDTO;
import com.yaowu.alpha.model.entity.knowledge.Knowledge;

/**
 * <p>
 * 知识库表 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface IKnowledgeService extends IService<Knowledge> {

    Page<Knowledge> pageByDto(KnowledgePageDTO dto);
}
