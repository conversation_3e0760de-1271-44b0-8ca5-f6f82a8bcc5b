package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.collection.CollUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.common.remote.ITagRemoteBizService;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import com.yaowu.tagsystemapi.enums.tag.SourceSystemEnum;
import com.yaowu.tagsystemapi.enums.tag.TagOperateTypeEnum;
import com.yaowu.tagsystemapi.feign.RemoteTagCommonServiceV2Feign;
import com.yaowu.tagsystemapi.model.dto.tag.v2.BatchMarkPickV2DTO;
import com.yaowu.tagsystemapi.model.dto.tag.v2.MarkPickV2DTO;
import com.yaowu.tagsystemapi.model.dto.tag.v2.TagAddV2DTO;
import com.yaowu.tagsystemapi.model.dto.tag.v2.TagPageV2DTO;
import com.yaowu.tagsystemapi.model.vo.tag.v2.TagAddV2VO;
import com.yaowu.tagsystemapi.model.vo.tag.v2.TagPageV2VO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/25 14:46
 */
@RequiredArgsConstructor
@Service
public class TagRemoteBizServiceImpl implements ITagRemoteBizService {

    private final RemoteTagCommonServiceV2Feign tagCommonServiceFeign;

    @Override
    public List<TagPageV2VO> searchTagByName(Set<String> tagNames, Integer category) {
        if (CollUtil.isEmpty(tagNames)) {
            return Collections.emptyList();
        }
        TagPageV2DTO tagPageV2DTO = new TagPageV2DTO();
        tagPageV2DTO.setTagNames(tagNames);
        tagPageV2DTO.setCategories(CollUtil.newArrayList(category));
        tagPageV2DTO.setPage(1L);
        tagPageV2DTO.setSize(tagNames.size());
        BasePage<TagPageV2VO> page = FeignInvokeUtils.convertPage(tagCommonServiceFeign.pageTags(tagPageV2DTO), TagPageV2VO.class);
        return page.getRecords();
    }

    /**
     * 添加标签
     * @param remoteDTO
     * @return
     */
    @Override
    public TagAddV2VO addTag(TagAddV2DTO remoteDTO) {
        return FeignInvokeUtils.convert(tagCommonServiceFeign.addTag(remoteDTO), TagAddV2VO.class);
    }

    /**
     * 打标签
     * @param tagCode
     * @param objInstanceId
     */
    @Override
    public void mark(String tagCode, String objInstanceId) {
        MarkPickV2DTO remoteDTO = new MarkPickV2DTO();
        remoteDTO.setObjInstId(objInstanceId);
        remoteDTO.setTagCode(tagCode);

        FeignInvokeUtils.convert(tagCommonServiceFeign.mark(remoteDTO), Boolean.class);
    }

    /**
     * 批量打标签
     * @param tagCodes
     * @param objInstanceId
     */
    @Override
    public void batchMark(Set<String> tagCodes, String objInstanceId) {
        BatchMarkPickV2DTO batchMarkDTO = new BatchMarkPickV2DTO();
        List<MarkPickV2DTO> marks = StreamUtil.of(tagCodes)
                .map(tag -> {
                    MarkPickV2DTO mark = new MarkPickV2DTO();
                    mark.setObjInstId(objInstanceId);
                    mark.setTagCode(tag);
                    return mark;
                })
                .toList();
        batchMarkDTO.setList(marks);
        batchMarkDTO.setMarkType(TagOperateTypeEnum.MANUAL);
        batchMarkDTO.setSourceSystem(SourceSystemEnum.H_CRM);

        FeignInvokeUtils.convert(tagCommonServiceFeign.batchMark(batchMarkDTO), Boolean.class);
    }

    @Override
    public List<String> listObjInstIds(String tagCode) {
        if (StringUtil.isEmpty(tagCode)) {
            return Collections.emptyList();
        }
        return FeignInvokeUtils.convertList(tagCommonServiceFeign.getObjInstIds(tagCode), String.class);
    }

    /**
     *
     * @param tagCodes
     * @return
     */
    @Override
    public Map<String, List<String>> mapObjInstIdsByTagCodes(Set<String> tagCodes) {
        return FeignInvokeUtils.convertMapWithList(tagCommonServiceFeign.getObjInstIdMap(CollUtil.newArrayList(tagCodes)), String.class, String.class);
    }

}
