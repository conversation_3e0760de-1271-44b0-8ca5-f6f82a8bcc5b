package com.yaowu.alpha.domain.payment.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.mybatisplus.service.ITenantIdValueService;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.config.exception.AlphaException;
import com.yaowu.alpha.config.nacos.PaymentProductNacosConfig;
import com.yaowu.alpha.config.thredpool.WrapperUtils;
import com.yaowu.alpha.config.tenant.TenantIdValueServiceImpl;
import com.yaowu.alpha.domain.auth.IAuthBizService;
import com.yaowu.alpha.domain.common.biz.ISettleRemoteBizService;
import com.yaowu.alpha.domain.passport.biz.ITenantBizService;
import com.yaowu.alpha.domain.passport.service.batis.service.IUserService;
import com.yaowu.alpha.domain.payment.biz.IPaymentOrderBizService;
import com.yaowu.alpha.domain.payment.service.batis.service.IPaymentOrderService;
import com.yaowu.alpha.domain.proxy.biz.IUserBusinessAccountBizService;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.enums.payment.PaymentChannelEnum;
import com.yaowu.alpha.enums.payment.PaymentMethodEnum;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyOrgManagementBizService;
import com.yaowu.alpha.enums.payment.PaymentOrderStatusEnum;
import com.yaowu.alpha.model.bo.payment.PaymentProduct;
import com.yaowu.alpha.model.dto.common.DistributedTaskDTO;
import com.yaowu.alpha.model.dto.payment.PaymentOrderCreateRequest;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryDTO;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryRequest;
import com.yaowu.alpha.model.dto.proxy.OrderPurchasedBenefitsDTO;
import com.yaowu.alpha.model.entity.passport.User;
import com.yaowu.alpha.model.entity.payment.PaymentOrder;
import com.yaowu.alpha.model.vo.payment.PaymentOrderDetailVO;
import com.yaowu.alpha.model.vo.payment.PaymentOrderVO;
import com.yaowu.alpha.model.vo.payment.PaymentProductResultVO;
import com.yaowu.alpha.model.vo.payment.PaymentResultVO;
import com.yaowu.alpha.utils.DistributedLockUtil;
import com.yaowu.alpha.utils.TransactionUtils;
import com.yaowu.alpha.utils.common.EnumUtil;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.convertor.payment.PaymentOrderMapStruct;
import com.yaowu.settle.api.enums.mtl.payment.PaymentStatusEnum;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteSubmitThirdPartyPaymentDTO;
import com.yaowu.settle.api.model.mtl.pojo.message.RemotePaymentNotificationResultMessage;
import com.yaowu.settle.api.model.mtl.vo.aggregation.RemoteSubmitThirdPartyPaymentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yaowu.alpha.constant.Constants.DISPLAY_TENANT_ID;

/**
 * <AUTHOR>
 * @date 2025/4/7-20:39
 */
@Service
@Slf4j
public class PaymentOrderBizServiceImpl implements IPaymentOrderBizService {

    @Autowired
    private DistributedLockUtil distributedLockUtil;

    @Autowired
    private IPaymentOrderService paymentOrderService;

    @Autowired
    private ISettleRemoteBizService settleRemoteBizService;

    @Autowired
    private PaymentProductNacosConfig paymentProductNacosConfig;

    @Autowired
    private TransactionUtils transactionUtils;

    @Autowired
    private IAuthBizService authBizService;

    @Autowired
    private IUserBusinessAccountBizService userBusinessAccountBizService;

    @Autowired
    private TenantIdValueServiceImpl tenantIdValueService;

    @Autowired
    private IProxyOrgManagementBizService proxyOrgManagementBizService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ITenantBizService tenantBizService;

    @Override
    public PaymentResultVO createOrderAndPay(PaymentOrderCreateRequest dto) {
        // 获取产品信息
        PaymentProduct paymentProduct = StreamUtil.of(paymentProductNacosConfig.getProducts())
                .filter(v -> v.getProductId().equals(dto.getProductId()))
                .findAny().orElseThrow(() -> new AlphaException(ErrorCodeEnum.PRODUCT_NOT_FOUND));
        // 创建订单数据
        PaymentResultVO vo = distributedLockUtil.execute(null, DistributedTaskDTO.builder().taskName("支付订单创建")
                .taskId("ALPHA:ACCOUNT:PURCHASE:CREATE:" + String.format("%s_%s", dto.getProductId(),
                        authBizService.currentUserId()))
                .build(), (p) -> {
            Long tenantId = tenantIdValueService.getTenantId();
            // if是演示用户，随机生成一个 tenantId
            if (DISPLAY_TENANT_ID.equals(tenantId)) {
                tenantId = tenantBizService.createTenant("演示用户支付生成租户的场景");
            }
            return this.createAndPay(authBizService.currentUserId(), tenantId,
                    dto, paymentProduct);
        });
        if (BooleanUtil.isTrue(dto.getHandled())) {
            writeUserAccount(vo.getId());
        }
        return vo;
    }

    @Override
    public PaymentProductResultVO products() {
        PaymentProductNacosConfig.TenantPaymentInfo tenantProductConfig =
                StreamUtil.findFirstByFilter(paymentProductNacosConfig.getTenantPaymentInfos(), v -> authBizService.currentTenantId().equals(v.getTenantId()));
        // 展示的商品数据
        Set<String> showProducts = tenantProductConfig != null && CollUtil.isNotEmpty(tenantProductConfig.getProductIds()) ? tenantProductConfig.getProductIds() : paymentProductNacosConfig.getDefaultProducts();
        List<PaymentProduct> list = StreamUtil.of(paymentProductNacosConfig.getProducts())
                .filter(v -> CollUtil.isNotEmpty(showProducts) && showProducts.contains(v.getProductId()))
                .collect(Collectors.toList());
        return PaymentProductResultVO.builder().products(list).build();
    }

    @Override
    public BasePage<PaymentOrderVO> page(PaymentOrderQueryRequest dto) {
        PaymentOrderQueryDTO paymentOrderQueryDTO = PaymentOrderMapStruct.INSTANCE.toPaymentOrderQueryDTO(dto);
        paymentOrderQueryDTO.setTenantId(tenantIdValueService.getCurTenantIdByOrgType(dto.getTenantId()));
        paymentOrderQueryDTO.setOrderStatuses(dto.getOrderStatus() == null ?
                CollUtil.newHashSet(PaymentOrderStatusEnum.SUCCESS.getCode(), PaymentOrderStatusEnum.REFUND.getCode(), PaymentOrderStatusEnum.PARTIAL_REFUND.getCode()
                ) : CollUtil.newHashSet(dto.getOrderStatus()));
        Page<PaymentOrder> page = this.paymentOrderService.pageByCondition(paymentOrderQueryDTO);

        Set<Long> orgIds = StreamUtil.of(page.getRecords()).map(PaymentOrder::getTenantId).collect(Collectors.toSet());
        Map<Long, String> orgId2OrgName = proxyOrgManagementBizService.getOrgId2OrgName(orgIds);
        return BasePage.simpleConvert(page, e -> PaymentOrderMapStruct.INSTANCE.toPaymentOrderVO(e, orgId2OrgName.get(e.getTenantId())));
    }

    @Override
    public PaymentOrderDetailVO detail(Long id) {
        PaymentOrder order = getAndCheckPaymentOrder(id);
        return PaymentOrderMapStruct.INSTANCE.toPaymentOrderDetailVO(order);
    }

    public PaymentOrder getAndCheckPaymentOrder(Long id) {
        PaymentOrder order = paymentOrderService.getById(id);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        return order;
    }

    public void handlerMqMessage(RemotePaymentNotificationResultMessage message) {
        // 开始处理订单的数据
        PaymentOrder paymentOrder = paymentOrderService.getById(Long.valueOf(message.getBizId()));
        if (paymentOrder == null) {
            log.error("PurchaseAccountConsumer, order not exist: {}", JSONUtil.toJsonStr(message));
            return;
        }
        switch (PaymentStatusEnum.codeOf(message.getPaymentStatus())) {
            case SUCCESS -> {
                paymentOrder.setOrderStatus(PaymentOrderStatusEnum.SUCCESS.getValue());
                paymentOrder.setPaymentTime(message.getCompleteTime());
                paymentOrderService.updateById(paymentOrder);
                writeUserAccount(paymentOrder.getId());
            }
            case CANCEL, FAIL -> {
                paymentOrder.setOrderStatus(PaymentOrderStatusEnum.CANCEL.getValue());
                paymentOrder.setCloseTime(message.getCancelTime());
                paymentOrderService.updateById(paymentOrder);
            }
            case REFUND -> {
                paymentOrder.setOrderStatus(PaymentOrderStatusEnum.REFUND.getValue());
                paymentOrder.setRefundTime(message.getRefundTime());
                paymentOrderService.updateById(paymentOrder);
            }
            case PARTIAL_REFUND -> {
                paymentOrder.setOrderStatus(PaymentOrderStatusEnum.PARTIAL_REFUND.getValue());
                paymentOrder.setRefundTime(message.getRefundTime());
                paymentOrderService.updateById(paymentOrder);
            }
        }
    }

    private PaymentResultVO createAndPay(Long userId, Long tenantId, PaymentOrderCreateRequest dto, PaymentProduct paymentProduct) {
        // 获取手机号码
        User user = userService.getById(userId);
        // 创建订单
        Long id = transactionUtils.executeWithResult(() -> {
            PaymentOrder entity = PaymentOrderMapStruct.INSTANCE.createPaymentOrder(user, tenantId, paymentProduct, payInfo(tenantId));
            paymentOrderService.save(entity);
            return entity.getId();
        });
        // 创建支付
        PaymentOrder order = getAndCheckPaymentOrder(id);
        PaymentOrderStatusEnum paymentStatusEnum = EnumUtil.fromValue(order.getOrderStatus(), PaymentOrderStatusEnum.class);
        if (paymentStatusEnum != PaymentOrderStatusEnum.WAITING) {
            throw new AlphaException(ErrorCodeEnum.PARAM_ERROR);
        }
        if (BooleanUtil.isTrue(dto.getHandled())) {
            order.setPaymentMethod(PaymentMethodEnum.OFFLINE_PAYMENT.getCode());
            order.setPaymentChannel(PaymentChannelEnum.BANK_PAY.getCode());
            order.setPaymentTime(LocalDateTime.now());
            order.setOrderStatus(PaymentOrderStatusEnum.SUCCESS.getValue());
            this.paymentOrderService.updateById(order);
            return PaymentResultVO.builder().id(id).payParam(null).build();
        } else {
            RemoteSubmitThirdPartyPaymentDTO remoteDTO = PaymentOrderMapStruct.buildPaymentDTO(order, dto);
            RemoteSubmitThirdPartyPaymentVO vo = settleRemoteBizService.submitPayment(remoteDTO);
            // 把支付的数据更新进去
            PaymentOrder.OrderInfo orderInfo = order.getOrderInfo();
            orderInfo.setOutTradeNo(vo.getOutTradeNo());
            orderInfo.setExpireTime(vo.getExpireTime());
            orderInfo.setPrePaymentRecordId(vo.getPrePaymentRecordId());
            order.setOrderInfo(orderInfo);
            this.paymentOrderService.updateById(order);
            return PaymentResultVO.builder().id(id).payParam(vo).build();
        }
    }

    private PaymentProductNacosConfig.TenantPaymentInfo payInfo(Long tenantId) {
        return StreamUtil.of(paymentProductNacosConfig.getTenantPaymentInfos())
                .filter(v -> tenantId.equals(v.getTenantId())).findFirst()
                .orElse(null);
    }

    private void writeUserAccount(Long id) {
        ThreadUtil.execute(WrapperUtils.wrapper(() -> {
            OrderPurchasedBenefitsDTO innerDTO = new OrderPurchasedBenefitsDTO();
            innerDTO.setOrderId(id);
            userBusinessAccountBizService.writeBenifitsByOrder(innerDTO);
        }));
    }
}
