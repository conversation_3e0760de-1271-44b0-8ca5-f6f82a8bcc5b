package com.yaowu.alpha.domain.customer.biz.impl;

import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerBizService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerService;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerCreateDTO;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 培育客户业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NurtureCustomerBizServiceImpl implements INurtureCustomerBizService {
    
    private final INurtureCustomerService nurtureCustomerService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNurtureCustomer(NurtureCustomerCreateDTO dto) {
        // 检查联系方式是否已存在
        if (nurtureCustomerService.existsByCustomerContact(dto.getCustomerContact())) {
            throw new BusinessException("该联系方式已存在");
        }
        // 调用Service层创建客户
        NurtureCustomer nurtureCustomer = nurtureCustomerService.createNurtureCustomer(dto);
        return nurtureCustomer.getId();
    }
}
