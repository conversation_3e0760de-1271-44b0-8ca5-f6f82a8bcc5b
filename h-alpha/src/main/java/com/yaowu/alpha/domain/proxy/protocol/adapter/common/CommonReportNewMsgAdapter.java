package com.yaowu.alpha.domain.proxy.protocol.adapter.common;

import com.yaowu.alpha.model.dto.proxy.control.ReportNewMsgNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.common.CommonReportNewMsgRequestDTO;
import com.yaowu.alpha.model.vo.proxy.ReportNewMsgAckVO;
import com.yaowu.alpha.utils.contants.proxy.CommonProxyActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.common.ProxyCommonRequestMapStruct;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 微信消息通知
 */
@Component
public class CommonReportNewMsgAdapter extends AbstractCommonRequestAdapter<CommonReportNewMsgRequestDTO, ReportNewMsgNoticeRequestDTO, ReportNewMsgAckVO, ReportNewMsgAckVO> {

    @Override
    protected boolean support(String action) {
        return Objects.equals(CommonProxyActionConstants.REPORT_NEW_MSG, action);
    }

    @Override
    public ReportNewMsgNoticeRequestDTO transferRequest(CommonReportNewMsgRequestDTO input) {
        return ProxyCommonRequestMapStruct.INSTANCE.toReportNewMsgRequestDTO(input);
    }

    @Override
    public ReportNewMsgAckVO transferResponse(ReportNewMsgAckVO output) {
        return output;
    }
}
