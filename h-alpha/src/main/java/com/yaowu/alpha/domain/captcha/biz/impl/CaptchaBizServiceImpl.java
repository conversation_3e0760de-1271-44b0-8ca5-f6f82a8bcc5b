package com.yaowu.alpha.domain.captcha.biz.impl;

import cn.hutool.core.util.RandomUtil;
import com.yaowu.alpha.config.email.EmailConfig;
import com.yaowu.alpha.config.sms.SmsConfig;
import com.yaowu.alpha.domain.captcha.biz.ICaptchaBizService;
import com.yaowu.alpha.domain.email.biz.IEmailSenderBizService;
import com.yaowu.alpha.domain.sms.biz.ISmsBizService;
import com.yaowu.alpha.model.dto.passport.EmailCaptchaDTO;
import com.yaowu.alpha.model.dto.passport.SmsCaptchaDTO;
import com.yaowu.alpha.model.vo.passport.CrownCodeVO;
import com.yaowu.alpha.utils.contants.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CaptchaBizServiceImpl implements ICaptchaBizService {

    @Autowired
    private ISmsBizService smsBizService;

    @Autowired
    private IEmailSenderBizService emailSenderBizService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private EmailConfig emailConfig;

    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public List<CrownCodeVO> crownCodeList() {
        CrownCodeVO _86 = new CrownCodeVO();
        _86.setCrownCode("+86");
        _86.setSelected(true);

        return List.of(_86);
    }

    @Override
    public Boolean sendSmsCaptcha(SmsCaptchaDTO dto) {
        String code = RandomUtil.randomNumbers(6);
        String smsCodeRedisKey = RedisConstants.SMS_REDIS_PREFIX + dto.getCaptchaType() + ":" + dto.getCrownCode() + dto.getPhone();
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        ops.set(smsCodeRedisKey, code, 10 * 60, TimeUnit.SECONDS);
        return smsBizService.sendSms("LOGIN", dto.getCrownCode(), dto.getPhone(), List.of(code));
    }

    @Override
    public Boolean verifySmsCaptcha(String crownCode, String phone, String captchaType, String captcha) {
        String smsCodeRedisKey = RedisConstants.SMS_REDIS_PREFIX + captchaType + ":" + crownCode + phone;
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        Object value = ops.get(smsCodeRedisKey);
        boolean isTestWhitelist = smsConfig.isTestWhitelist(crownCode + phone);
        boolean flag = value != null && Objects.equals(value.toString(), captcha);
        if (flag) {
            return true;
        }
        if (!Objects.equals(env, "pre") && !Objects.equals(env, "prod") && !isTestWhitelist) {
            log.info("测试环境，非白名单，不校验短信验证码，验证码类型：{}，手机号：{}", captchaType, crownCode + "-" + phone);
            return true;
        }
        return false;
    }

    @Override
    public Boolean sendEmailCaptcha(EmailCaptchaDTO dto) {
        String code = RandomUtil.randomNumbers(6);
        String emailCodeRedisKey = RedisConstants.EMAIL_REDIS_PREFIX + "LOGIN:" + dto.getEmail();
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        ops.set(emailCodeRedisKey, code, 10 * 60, TimeUnit.SECONDS);
        String htmlTemplate = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>登录验证码</title><style>body {font-family: Arial, sans-serif;background-color: #f5f5f5;margin: 0;padding: 0;}.container {max-width: 600px;margin: 0 auto;background-color: #ffffff;padding: 20px;border-radius: 5px;box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);}.header {text-align: center;margin-bottom: 20px;}.content {padding: 20px;}.code {font-size: 24px;font-weight: bold;text-align: center;margin: 20px 0;padding: 10px;background-color: #f0f0f0;border-radius: 5px;}.footer {text-align: center;margin-top: 20px;color: #777;font-size: 12px;}</style></head><body><div class=\"container\"><div class=\"header\"><h1>您的登录验证码</h1></div><div class=\"content\"><p>尊敬的用户，</p><p>您正在尝试登录您的账户。以下是您的登录验证码：</p><div class=\"code\">%s</div><p>验证码有效期为5分钟，请勿将验证码泄露给他人。</p></div></div></body></html>";
        String htmlContent = String.format(htmlTemplate, code);
        return emailSenderBizService.sendHtmlEmail("LOGIN", dto.getEmail(), htmlContent);
    }

    @Override
    public Boolean verifyEmailCaptcha(String email, String captchaType, String captcha) {
        String emailCodeRedisKey = RedisConstants.EMAIL_REDIS_PREFIX + captchaType + ":" + email;
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        Object value = ops.get(emailCodeRedisKey);
        boolean isTestWhitelist = emailConfig.isTestWhitelist(email);
        boolean flag = value != null && Objects.equals(value.toString(), captcha);
        if (flag) {
            return true;
        }
        if (!Objects.equals(env, "pre") && !Objects.equals(env, "prod") && !isTestWhitelist) {
            log.info("测试环境，非白名单，不校验邮件验证码，验证码类型：{}，邮箱：{}", captchaType, email);
            return true;
        }
        return false;
    }
}
