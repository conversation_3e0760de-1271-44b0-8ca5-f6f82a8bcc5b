package com.yaowu.alpha.domain.common.processor.impl;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.common.processor.AbstractContactVerificationProcessor;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.enums.common.ContactVerificationStatusEnum;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;
import com.yaowu.alpha.utils.MailVerifierUtil;
import com.yaowu.alpha.utils.MailVerifierUtil.MailVerifyResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Email验证处理器
 * 
 * <AUTHOR>
 * 2025-01-27 15:35:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailVerificationProcessor extends AbstractContactVerificationProcessor {

    private final MailVerifierUtil mailVerifierUtil;


    @Override
    public boolean supports(ContactVerificationContactTypeEnum contactType) {
        return ContactVerificationContactTypeEnum.EMAIL.equals(contactType);
    }

    @Override
    protected void doVerifyProcess(ContactVerificationTask task) {
        // 执行邮箱验证
        MailVerifyResult result = mailVerifierUtil.verifyEmail(task.getContact());
        log.info("Email验证完成: {} - {}", task.getContact(), JSONUtil.toJsonStr(result));
        
        // 保存验证原始结果
        task.setValidationRawResult(JSONUtil.toJsonStr(result));

        task.setValidateTime(LocalDateTime.now());
        ContactVerificationStatusEnum status = determineValidationStatus(result);
        task.setValidationStatus(status);
        
        // 验证失败时发送企微通知
        if (ContactVerificationStatusEnum.FAILED.equals(status)) {
            sendFailureNotification(task, result);
        }
    }
    
    /**
     * 根据邮箱验证结果确定验证状态
     * 1. 邮箱有效(VALID) → VALID
     * 2. 邮箱无效(INVALID)、格式错误(FORMAT_ERROR) → INVALID  
     * 3. 其他错误 → FAILED
     * 
     * @param result 邮箱验证结果
     * @return 验证状态
     */
    private ContactVerificationStatusEnum determineValidationStatus(MailVerifyResult result) {
        if (result == null) {
            return ContactVerificationStatusEnum.FAILED;
        }
        
        MailVerifierUtil.VerifyStatusCode statusCode = result.getStatusCode();
        if (statusCode == null) {
            return ContactVerificationStatusEnum.FAILED;
        }
        
        return switch (statusCode) {
            case VALID -> ContactVerificationStatusEnum.VALID;
            case INVALID, FORMAT_ERROR -> ContactVerificationStatusEnum.INVALID;
            default -> ContactVerificationStatusEnum.FAILED;
        };
    }
    
    /**
     * 发送验证失败通知
     * 
     * @param task 验证任务
     * @param result 验证结果
     */
    private void sendFailureNotification(ContactVerificationTask task, MailVerifyResult result) {
        try {
            String statusDescription = result != null ? result.getStatusDescription() : "未知状态";
            String message = String.format("邮箱验证失败通知\n" +
                    "邮箱地址: %s\n" +
                    "任务ID: %s\n" +
                    "验证结果: %s\n" +
                    "失败原因: %s",
                    task.getContact(),
                    task.getId(),
                    statusDescription,
                    result != null ? result.getMsg() : "未知错误");
            
            warnNoticeBizService.sendNoticeToWechat(message, commonConfig.getSystemWarnNoticeBotKey(), null);
            log.info("邮箱验证失败通知已发送: {}", task.getContact());
        } catch (Exception e) {
            log.error("发送邮箱验证失败通知异常: {}", task.getContact(), e);
        }
    }
}