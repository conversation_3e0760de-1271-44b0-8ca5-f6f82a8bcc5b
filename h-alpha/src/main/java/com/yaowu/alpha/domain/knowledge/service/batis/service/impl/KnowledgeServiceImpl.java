package com.yaowu.alpha.domain.knowledge.service.batis.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.knowledge.service.batis.mapper.KnowledgeMapper;
import com.yaowu.alpha.domain.knowledge.service.batis.service.IKnowledgeService;
import com.yaowu.alpha.model.dto.knowledge.KnowledgePageDTO;
import com.yaowu.alpha.model.entity.knowledge.Knowledge;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 智能体聊天文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class KnowledgeServiceImpl extends ServiceImpl<KnowledgeMapper, Knowledge> implements IKnowledgeService {

    @Override
    public Page<Knowledge> pageByDto(KnowledgePageDTO dto) {
        return this.lambdaQuery()
                .eq(dto.getTenantId() != null, Knowledge::getTenantId, dto.getTenantId())
                .eq(dto.getUserId() != null, Knowledge::getUserId, dto.getUserId())
                .like(StrUtil.isNotBlank(dto.getKnowledgeName()), Knowledge::getKnowledgeName, dto.getKnowledgeName())
                .orderByDesc(Knowledge::getCreateTime)
                .page(dto.pageRequest());
    }
}
