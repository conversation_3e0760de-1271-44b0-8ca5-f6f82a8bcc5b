package com.yaowu.alpha.domain.payment.consumer;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.config.thredpool.WrapperUtils;
import com.yaowu.alpha.domain.payment.biz.IPaymentOrderBizService;
import com.yaowu.alpha.domain.payment.service.batis.service.IPaymentOrderService;
import com.yaowu.alpha.enums.payment.PaymentOrderStatusEnum;
import com.yaowu.alpha.model.dto.proxy.OrderPurchasedBenefitsDTO;
import com.yaowu.alpha.model.entity.payment.PaymentOrder;
import com.yaowu.settle.api.enums.mtl.payment.PaymentStatusEnum;
import com.yaowu.settle.api.model.mtl.pojo.RemotePaymentBizTypeConstants;
import com.yaowu.settle.api.model.mtl.pojo.message.PaymentBusinessMqMessage;
import com.yaowu.settle.api.model.mtl.pojo.message.RemotePaymentNotificationResultMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/7-19:35
 */
@Component
@Slf4j
public class PurchaseAccountConsumer extends AbstractPaymentMessageService {


    @Autowired
    private IPaymentOrderBizService paymentOrderBizService;


    @Override
    protected boolean support(RemotePaymentNotificationResultMessage message) {
        return RemotePaymentBizTypeConstants.ALPHA_PAY.equals(message.getBizType());
    }

    @Override
    protected void innerProcessMessage(RemotePaymentNotificationResultMessage message) {
        paymentOrderBizService.handlerMqMessage(message);
    }
}
