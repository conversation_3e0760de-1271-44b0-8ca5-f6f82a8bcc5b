package com.yaowu.alpha.domain.customer.biz.impl.node;

import com.yaowu.alpha.enums.customer.NurtureCustomerClassificationTypeEnum;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 7天消极节点处理器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
public class SevenDayNegativeNodeProcessor extends AbstractNurtureCustomerFlowNodeProcessor {

    private static final String NODE_CODE = "7_DAY_NEGATIVE_NODE";

    @Override
    public boolean supports(String nodeCode) {
        return NODE_CODE.equals(nodeCode);
    }

    @Override
    protected String getNodeCode() {
        return NODE_CODE;
    }

    @Override
    protected String nextNode() {
        // 7天消极节点是分支流程的终点，没有下一个节点
        return null;
    }

    /**
     * 7天消极节点特殊处理逻辑
     * 当7天消极节点收到消极回复时，直接设置为沉默池并结束流程
     * 
     * @param node 当前节点
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    @Override
    protected void handleNegativeReplySpecialLogic(NurtureCustomerFlowNode node, 
                                                 NurtureCustomerFlow flow, 
                                                 List<Long> msgIds) {
        // 7天消极节点收到消极回复，直接设置为沉默池并结束流程
        finishFlowWithClassification(flow, NurtureCustomerClassificationTypeEnum.SILENT_POOL);
        log.info("7天消极节点收到消极回复，流程已结束并设置客户为沉默池，nodeId: {}, flowId: {}", 
                 node.getId(), flow.getId());
    }
} 