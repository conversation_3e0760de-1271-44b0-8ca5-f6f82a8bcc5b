package com.yaowu.alpha.domain.facebook.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookGroupMapper;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookGroupService;
import com.yaowu.alpha.model.entity.facebook.FacebookGroup;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Facebook群组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class FacebookGroupServiceImpl extends ServiceImpl<FacebookGroupMapper, FacebookGroup> implements FacebookGroupService {

    @Override
    public Map<Long, Integer> getGroupStatisticMap(List<Long> taskIds) {
        // 参数校验
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }

        // 使用XML映射的SQL查询，高效统计每个任务的群组数量
        List<Map<String, Object>> statisticList = this.baseMapper.getGroupStatisticByTaskIds(taskIds);

        // 转换为Map<Long, Integer>格式
        Map<Long, Integer> resultMap = new HashMap<>();
        for (Map<String, Object> item : statisticList) {
            Long taskId = (Long) item.get("taskId");
            Integer count = ((Number) item.get("count")).intValue();
            resultMap.put(taskId, count);
        }

        // 为没有群组的任务ID设置0
        for (Long taskId : taskIds) {
            resultMap.putIfAbsent(taskId, 0);
        }

        return resultMap;
    }

    @Override
    public Map<Long, FacebookGroup> getGroupMap(List<Long> groupIds) {
        if (CollUtil.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        List<FacebookGroup> list = this.lambdaQuery().in(FacebookGroup::getId, groupIds).list();
        return list.stream().collect(Collectors.toMap(FacebookGroup::getId, Function.identity(), (g1, g2) -> g2));
    }

    @Override
    public List<Long> listIdsByLikeGroupName(String groupName) {
        if (StrUtil.isBlank(groupName)) {
            return new ArrayList<>();
        }
        List<FacebookGroup> list = this.lambdaQuery().like(FacebookGroup::getGroupName, groupName).select(FacebookGroup::getId).list();
        return list.stream().map(FacebookGroup::getId).toList();
    }
}
