package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyAccountFriendExtensionMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountFriendExtensionService;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriendExtension;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 好友信息扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Slf4j
@Service
public class ProxyAccountFriendExtensionServiceImpl extends ServiceImpl<ProxyAccountFriendExtensionMapper, ProxyAccountFriendExtension> implements IProxyAccountFriendExtensionService {

    @Override
    public ProxyAccountFriendExtension getAndCheckByFriendId(Long friendId) {
        ProxyAccountFriendExtension extension = getByFriendId(friendId);
        if (extension == null) {
            throw new BusinessException("好友扩展信息不存在");
        }
        return extension;
    }

    @Override
    public ProxyAccountFriendExtension getByFriendId(Long friendId) {
        if (friendId == null) {
            return null;
        }
        
        LambdaQueryWrapper<ProxyAccountFriendExtension> queryWrapper = Wrappers.lambdaQuery(ProxyAccountFriendExtension.class)
                .eq(ProxyAccountFriendExtension::getFriendId, friendId)
                .last("limit 1");
        
        return getOne(queryWrapper);
    }

    @Override
    public List<ProxyAccountFriendExtension> listByFriendIds(List<Long> friendIds) {
        if (CollectionUtils.isEmpty(friendIds)) {
            return Collections.emptyList();
        }
        
        LambdaQueryWrapper<ProxyAccountFriendExtension> queryWrapper = Wrappers.lambdaQuery(ProxyAccountFriendExtension.class)
                .in(ProxyAccountFriendExtension::getFriendId, friendIds);
        
        return list(queryWrapper);
    }
} 