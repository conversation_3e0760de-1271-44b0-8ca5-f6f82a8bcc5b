package com.yaowu.alpha.domain.common.biz.impl;

import cn.hutool.core.util.StrUtil;
import com.freedom.mybatisplus.service.ITenantIdValueService;
import com.yaowu.alpha.domain.common.biz.ICommonTranslationBizService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.CommonTranslateAgent;
import com.yaowu.alpha.model.dto.common.CommonTranslateDTO;
import com.yaowu.alpha.model.vo.common.CommonTranslateVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 通用翻译业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonTranslationBizServiceImpl implements ICommonTranslationBizService {

    private static final String DEFAULT_FROM_USER = "system";
    private static final String DEFAULT_TO_USER = "translation";

    private final CommonTranslateAgent commonTranslateAgent;
    private final ITenantIdValueService tenantIdValueService;

    @Override
    public CommonTranslateVO translateText(CommonTranslateDTO dto) {
        dto.validate();
        String translatedText = performTranslation(dto);
        return buildTranslateResponse(dto.getText(), translatedText);
    }

    /**
     * 执行翻译操作
     *
     * @param dto 翻译请求参数
     * @return 翻译后的文本，失败时返回原文
     */
    private String performTranslation(CommonTranslateDTO dto) {
        try {
            Long tenantId = tenantIdValueService.getTenantId();
            if (tenantId == null) {
                log.warn("租户ID为空，使用默认租户");
                tenantId = 0L;
            }

            return StrUtil.isNotBlank(dto.getCountry()) 
                ? translateByCountry(dto, tenantId)
                : translateByLanguage(dto, tenantId);
        } catch (Exception e) {
            log.error("翻译失败，返回原文，text: {}, error: {}", dto.getText(), e.getMessage(), e);
            return dto.getText();
        }
    }

    /**
     * 根据国家进行翻译
     */
    private String translateByCountry(CommonTranslateDTO dto, Long tenantId) {
        return Optional.ofNullable(
            commonTranslateAgent.translateByUserCountry(
                tenantId, 
                DEFAULT_FROM_USER, 
                DEFAULT_TO_USER, 
                dto.getText(), 
                dto.getCountry()
            )
        ).filter(StrUtil::isNotBlank)
         .orElse(dto.getText());
    }

    /**
     * 根据语言名称进行翻译
     */
    private String translateByLanguage(CommonTranslateDTO dto, Long tenantId) {
        return Optional.ofNullable(
            commonTranslateAgent.translateByLanguage(
                tenantId, 
                DEFAULT_FROM_USER, 
                DEFAULT_TO_USER, 
                dto.getText(), 
                dto.getLanguage()
            )
        ).filter(StrUtil::isNotBlank)
         .orElse(dto.getText());
    }

    /**
     * 构建翻译响应
     */
    private CommonTranslateVO buildTranslateResponse(String originalText, String translatedText) {
        return CommonTranslateVO.builder()
            .originalText(originalText)
            .translatedText(translatedText)
            .build();
    }
} 