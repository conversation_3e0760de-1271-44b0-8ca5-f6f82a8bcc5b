package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.common.Country;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface ICountryService extends IService<Country> {

    /**
     * 根据国家名称查询区号（支持中英文）
     *
     * @param countryName 国家名称（支持中文名或英文名）
     * @return 电话区号，如果未找到则返回null
     */
    String getPhoneCodeByCountryName(String countryName);
}
