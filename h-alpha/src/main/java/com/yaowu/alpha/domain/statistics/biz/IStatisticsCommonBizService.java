package com.yaowu.alpha.domain.statistics.biz;

import com.yaowu.alpha.model.dto.statistics.StatisticsCommonDTO;
import com.yaowu.alpha.model.vo.statistics.StatisticDataGroupVO;
import com.yaowu.alpha.model.vo.statistics.StatisticDataItemVO;
import com.yaowu.alpha.model.vo.statistics.StatisticsDailyVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IStatisticsCommonBizService {

    /**
     * 获取邮件统计报表
     */
    List<StatisticDataItemVO> getEmailTotalStatistics(StatisticsCommonDTO dto);

    /**
     * 获取邮件日统计
     */
    List<StatisticsDailyVO> getEmailDailyStatistics(StatisticsCommonDTO dto);

    /**
     * 获取对话统计报表
     */
    List<StatisticDataGroupVO> getChatTotalStatistics(StatisticsCommonDTO dto);

    /**
     * 获取对话日报表
     */
    List<StatisticsDailyVO> getChatDailyStatistics(StatisticsCommonDTO dto);

    /**
     * 获取打招呼任务统计报表
     */
    List<StatisticDataGroupVO> getGreetingTotalStatistics(StatisticsCommonDTO dto);

    /**
     * 获取打招呼任务日报表
     */
    List<StatisticsDailyVO> getGreetingDailyStatistics(StatisticsCommonDTO dto);

    /**
     * 获取客户培育流程统计报表
     */
    List<StatisticDataGroupVO> getNurtureCustomerFlowTotalStatistics(StatisticsCommonDTO dto);
}
