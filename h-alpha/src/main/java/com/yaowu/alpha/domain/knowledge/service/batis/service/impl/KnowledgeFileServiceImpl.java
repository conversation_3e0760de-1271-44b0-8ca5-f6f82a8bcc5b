package com.yaowu.alpha.domain.knowledge.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.knowledge.service.batis.mapper.KnowledgeFileMapper;
import com.yaowu.alpha.domain.knowledge.service.batis.service.IKnowledgeFileService;
import com.yaowu.alpha.model.dto.knowledge.KnowledgeFilePageDTO;
import com.yaowu.alpha.model.entity.knowledge.KnowledgeFile;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class KnowledgeFileServiceImpl extends ServiceImpl<KnowledgeFileMapper, KnowledgeFile> implements IKnowledgeFileService {


    @Override
    public List<KnowledgeFile> getByKnowledgeId(Long knowledgeId) {
        return this.lambdaQuery().eq(KnowledgeFile::getKnowledgeId, knowledgeId).list();
    }

    @Override
    public Map<Long, List<KnowledgeFile>> getByKnowledgeIds(List<Long> knowledgeIds) {
        if (CollUtil.isEmpty(knowledgeIds)) {
            return Map.of();
        }
        List<KnowledgeFile> list = this.lambdaQuery().in(KnowledgeFile::getKnowledgeId, knowledgeIds).list();
        return list.stream().collect(Collectors.groupingBy(KnowledgeFile::getKnowledgeId));
    }

    @Override
    public Page<KnowledgeFile> pageByDto(KnowledgeFilePageDTO dto) {
        return this.lambdaQuery()
                .eq(dto.getKnowledgeId() != null, KnowledgeFile::getKnowledgeId, dto.getKnowledgeId())
                .like(StrUtil.isNotBlank(dto.getFileName()), KnowledgeFile::getFileName, dto.getFileName())
                .orderByDesc(KnowledgeFile::getCreateTime)
                .page(new Page<>(dto.getPage(), dto.getSize()));
    }
}
