package com.yaowu.alpha.domain.common.remote.impl;

import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.common.remote.IRiskControlRemoteBizService;
import com.yaowu.riskcontrolapi.feign.namelist.IRemoteNameListServiceFeign;
import com.yaowu.riskcontrolapi.model.dto.namelist.NameListPageDTO;
import com.yaowu.riskcontrolapi.model.vo.namelist.NameListVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/16 14:44
 */
@RequiredArgsConstructor
@Service
public class RiskControlRemoteBizServiceImpl implements IRiskControlRemoteBizService {

    private final IRemoteNameListServiceFeign nameListServiceFeign;

    @Override
    public BasePage<NameListVO> pageNameLists(NameListPageDTO dto) {
        return FeignInvokeUtils.convertPage(nameListServiceFeign.page(dto), NameListVO.class);
    }
}
