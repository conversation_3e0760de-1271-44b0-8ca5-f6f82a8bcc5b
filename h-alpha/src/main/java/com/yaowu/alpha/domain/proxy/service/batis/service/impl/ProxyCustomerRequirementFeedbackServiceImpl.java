package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyCustomerRequirementFeedbackMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyCustomerRequirementFeedbackService;
import com.yaowu.alpha.model.entity.proxy.ProxyCustomerReqFeedback;
import com.yaowu.alpha.utils.common.LongUtils;
import com.yaowu.alpha.utils.common.StreamUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 代理平台用户需求反馈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Service
public class ProxyCustomerRequirementFeedbackServiceImpl extends ServiceImpl<ProxyCustomerRequirementFeedbackMapper, ProxyCustomerReqFeedback> implements IProxyCustomerRequirementFeedbackService {

    @Override
    public Map<Long, ProxyCustomerReqFeedback> getByRequirementIds(Set<Long> requirementIds) {
        LambdaQueryWrapper<ProxyCustomerReqFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProxyCustomerReqFeedback::getRequirementId, requirementIds);
        List<ProxyCustomerReqFeedback> feedbackList = list(queryWrapper);
        return StreamUtil.of(feedbackList)
                .collect(Collectors.toMap(ProxyCustomerReqFeedback::getRequirementId, Function.identity()));
    }

    @Override
    public ProxyCustomerReqFeedback getByRequirementId(Long requirementId) {
        LambdaQueryWrapper<ProxyCustomerReqFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProxyCustomerReqFeedback::getRequirementId, requirementId)
                .last("LIMIT 1");
        return  getOne(queryWrapper);
    }

    @Override
    public Boolean removeByRequirementId(Long requirementId) {
        if (LongUtils.isInvalid(requirementId)){
            return false;
        }
        LambdaQueryWrapper<ProxyCustomerReqFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProxyCustomerReqFeedback::getRequirementId, requirementId);
        return remove(queryWrapper);
    }
}
