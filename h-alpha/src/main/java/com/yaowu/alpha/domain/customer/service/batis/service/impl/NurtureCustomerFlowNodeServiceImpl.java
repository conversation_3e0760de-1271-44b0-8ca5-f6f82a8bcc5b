package com.yaowu.alpha.domain.customer.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.customer.service.batis.mapper.NurtureCustomerFlowNodeMapper;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowNodeService;
import com.yaowu.alpha.enums.customer.NurtureCustomerFlowNodeStatusEnum;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 客户培育流程节点执行记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class NurtureCustomerFlowNodeServiceImpl extends ServiceImpl<NurtureCustomerFlowNodeMapper, NurtureCustomerFlowNode> implements INurtureCustomerFlowNodeService {

    @Override
    public List<NurtureCustomerFlowNode> listTerminableNodes(Long flowId) {
        if (flowId == null) {
            return Collections.emptyList();
        }
        
        return lambdaQuery()
                .eq(NurtureCustomerFlowNode::getFlowId, flowId)
                .notIn(NurtureCustomerFlowNode::getNodeStatus, 
                       NurtureCustomerFlowNodeStatusEnum.EXPIRED, 
                       NurtureCustomerFlowNodeStatusEnum.COMPLETED)
                .ne(NurtureCustomerFlowNode::getTerminationFlag, true)
                .list();
    }

    @Override
    public void batchUpdateNodesTermination(List<Long> nodeIds) {
        if (CollUtil.isEmpty(nodeIds)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        lambdaUpdate().in(NurtureCustomerFlowNode::getId, nodeIds)
                .set(NurtureCustomerFlowNode::getTerminationFlag, true)
                .set(NurtureCustomerFlowNode::getTerminationTime, now)
                .update();
    }

    @Override
    public List<NurtureCustomerFlowNode> listPendingExecutionNodes(Long maxSize) {
        if (maxSize == null || maxSize <= 0) {
            return Collections.emptyList();
        }
        
        return lambdaQuery()
                .eq(NurtureCustomerFlowNode::getNodeStatus, NurtureCustomerFlowNodeStatusEnum.PENDING)
                .le(NurtureCustomerFlowNode::getNodeStartExecuteTime, LocalDateTime.now())
                .ne(NurtureCustomerFlowNode::getTerminationFlag, true)
                .orderByAsc(NurtureCustomerFlowNode::getProxyTaskExecuteTime)
                .last("LIMIT " + maxSize)
                .list();
    }

    @Override
    public List<NurtureCustomerFlowNode> listExpiredPendingReplyNodes(Long maxSize) {
        if (maxSize == null || maxSize <= 0) {
            return Collections.emptyList();
        }
        
        return lambdaQuery()
                .eq(NurtureCustomerFlowNode::getNodeStatus, NurtureCustomerFlowNodeStatusEnum.PENDING_REPLY)
                .lt(NurtureCustomerFlowNode::getReplyExpireTime, LocalDateTime.now())
                .ne(NurtureCustomerFlowNode::getTerminationFlag, true)
                .orderByAsc(NurtureCustomerFlowNode::getReplyExpireTime)
                .last("LIMIT " + maxSize)
                .list();
    }

    @Override
    public NurtureCustomerFlowNode getActivePendingReplyNodeByFlowId(Long flowId) {
        if (flowId == null) {
            return null;
        }
        
        return lambdaQuery()
                .eq(NurtureCustomerFlowNode::getFlowId, flowId)
                .eq(NurtureCustomerFlowNode::getNodeStatus, NurtureCustomerFlowNodeStatusEnum.PENDING_REPLY)
                .ne(NurtureCustomerFlowNode::getTerminationFlag, true)
                .orderByDesc(NurtureCustomerFlowNode::getCreateTime)
                .last("LIMIT 1")
                .one();
    }
}
