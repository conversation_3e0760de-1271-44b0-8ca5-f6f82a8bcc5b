package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.BaseWetoolRequest;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinPullTaskAckResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolAckConstants;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinPullTaskMapStruct;
import org.springframework.stereotype.Component;

/**
 * pull task通知转换处理器
 */
@Component
public class WetoolPullTaskAdapter extends AbstractWetoolActionAdapter<BaseWetoolRequest, BaseNoticeRequestDTO, PullTaskAckVO, WetoolWexinPullTaskAckResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.PULL_TASK.equals(action);
    }

    @Override
    public BaseNoticeRequestDTO transferRequest(BaseWetoolRequest input) {
        return WetoolWexinPullTaskMapStruct.INSTANCE.toTransferRequestDTO(input);
    }

    @Override
    public WetoolWexinPullTaskAckResponseVO transferResponse(PullTaskAckVO output) {
        WetoolWexinPullTaskAckResponseVO responseVO = new WetoolWexinPullTaskAckResponseVO();
        super.transferSuccessResponse(responseVO, WetoolAckConstants.PULL_TASK_ACK);
        WetoolWexinPullTaskAckResponseVO.TaskAck taskAck = WetoolWexinPullTaskMapStruct.INSTANCE.toTransferResponseVO(output.getData());
        responseVO.setData(taskAck);
        return responseVO;
    }
}
