package com.yaowu.alpha.domain.facebook.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookSearchTaskMapper;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookSearchTaskService;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTask;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Facebook搜索任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class FacebookSearchTaskServiceImpl extends ServiceImpl<FacebookSearchTaskMapper, FacebookSearchTask> implements FacebookSearchTaskService {

    @Override
    public List<Long> listIdsByLikeTaskName(String taskName) {
        if (StrUtil.isBlank(taskName)) {
            return new ArrayList<>();
        }
        List<FacebookSearchTask> list = this.lambdaQuery().like(FacebookSearchTask::getTaskName, taskName).select(FacebookSearchTask::getId).list();
        return list.stream().map(FacebookSearchTask::getId).toList();
    }

    @Override
    public Map<Long, FacebookSearchTask> getTaskMap(List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }
        List<FacebookSearchTask> taskList = this.listByIds(taskIds);
        return taskList.stream().collect(Collectors.toMap(FacebookSearchTask::getId, Function.identity()));
    }
}