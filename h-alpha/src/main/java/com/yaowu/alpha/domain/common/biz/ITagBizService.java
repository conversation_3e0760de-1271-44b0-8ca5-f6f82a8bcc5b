package com.yaowu.alpha.domain.common.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.common.TagBindingDTO;
import com.yaowu.alpha.model.dto.common.TagCreateDTO;
import com.yaowu.alpha.model.dto.common.TagQueryDTO;
import com.yaowu.alpha.model.vo.common.TagVO;

import java.util.List;

public interface ITagBizService {


    /**
     * create tag
     * @param tagCreateDTO
     * @param tagBusinessCode
     * @return
     */
    Long createTag(TagCreateDTO tagCreateDTO, String tagBusinessCode);

    /**
     * 绑定tag
     * @param dto
     * @return
     */
    void bindingTag(TagBindingDTO dto);

    /**
     * 解绑tag
     * @param dto 标签绑定信息
     */
    void unbindTag(TagBindingDTO dto);

    /**
     * 查询标签列表
     * @param dto 查询参数
     * @return 标签列表
     */
    List<TagVO> listTags(TagQueryDTO dto);

    /**
     * 分页查询标签列表
     * @param dto 查询参数
     * @return 分页标签列表
     */
    BasePage<TagVO> pageTags(TagQueryDTO dto);

    /**
     * 查询标签绑定的实例ID列表
     * @param tagId 标签ID
     * @return 实例ID列表
     */
    List<String> listTagInstanceIds(Long tagId);
    
    /**
     * 批量查询多个标签绑定的实例ID列表并取交集
     * @param tagIds 标签ID列表
     * @return 满足所有标签条件的实例ID列表
     */
    List<String> listTagInstanceIdsIntersection(List<Long> tagIds);
    
    /**
     * 根据实例ID查询绑定的标签列表
     * @param instanceId 实例ID
     * @param businessCode 业务编码
     * @return 标签列表
     */
    List<TagVO> listTagsByInstanceId(String instanceId, String businessCode);
    
    /**
     * 删除标签
     * @param tagId 标签ID
     * @return 是否删除成功
     */
    void deleteTag(Long tagId);
}
