package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportTagInfoNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportTagInfoDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信标签
 */
@Component
public class WetoolReportTagInfoAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportTagInfoDTO, ReportTagInfoNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_TAG_INFO.equals(action);
    }

    @Override
    public ReportTagInfoNoticeRequestDTO transferRequest(WetoolWexinReportTagInfoDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportTagInfoRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
