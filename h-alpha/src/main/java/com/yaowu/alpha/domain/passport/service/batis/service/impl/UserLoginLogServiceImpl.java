package com.yaowu.alpha.domain.passport.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.passport.service.batis.mapper.UserLoginLogMapper;
import com.yaowu.alpha.domain.passport.service.batis.service.IUserUserLoginLogService;
import com.yaowu.alpha.model.entity.passport.UserLoginLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户登录日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Slf4j
@Service
public class UserLoginLogServiceImpl extends ServiceImpl<UserLoginLogMapper, UserLoginLog>
        implements IUserUserLoginLogService {

}
