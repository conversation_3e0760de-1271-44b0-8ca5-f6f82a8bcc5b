package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.http.OkHttpClientConfig;
import com.yaowu.alpha.config.wetool.WhatsAppConfig;
import com.yaowu.alpha.domain.common.remote.IWhatsAppInvokeBizService;
import com.yaowu.alpha.model.dto.proxy.RemoteSearchContactDTO;
import com.yaowu.alpha.model.dto.whatsapp.WhatsappQrcodeAuthModel;
import com.yaowu.alpha.model.dto.whatsapp.WhatsappUnbindAccountModel;
import com.yaowu.alpha.model.vo.whatsapp.RemoteSearchContactResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/19 11:20
 */
@Slf4j
@Service
public class WhatsAppInvokeBizServiceImpl implements IWhatsAppInvokeBizService {

    @Autowired
    private WhatsAppConfig whatsAppConfig;

    private static final String APP_ID = "7a4e3b2c1d5f6e8a9b7c5d3e2f1a4b6c8d9e7f5a3b2c1d6e8f9a5b4c7d3e2f1";


    @Override
    public Boolean createAccountOrStartQrcodeAuth(WhatsappQrcodeAuthModel model) {
        // 发送post请求
        String apiName = "WhatsApp发起二维码授权流程";
        Map<String, Object> body = Map.of(
                "appId", APP_ID,
                "userId", model.getUserId().toString(),
                "uuid", model.getUuid()
        );
        return requestPost(apiName, "/api/whatsapp-client/create-account", body);
    }

    @Override
    public Boolean unbindAccount(WhatsappUnbindAccountModel model) {
        // 发送post请求
        String apiName = "WhatsApp发起解绑流程";
        Map<String, Object> body = Map.of(
                "appId", APP_ID,
                "userId", model.getUserId().toString(),
                "phone", model.getPhone()
        );
        return requestPost(apiName, "/api/whatsapp-client/logout-account", body);
    }

    @Override
    public RemoteSearchContactResultVO searchContact(RemoteSearchContactDTO dto) {
        String urlWithParams = StrUtil.format("/api/whatsapp-client/search-contact?agentPhone={}&targetPhone={}",
                dto.getAgentPhone(), dto.getTargetPhone());
        // 发送请求
        try {
            String response = OkHttpClientConfig.get(this.getFullUrl(urlWithParams));
            return JacksonUtils.jsonStrToObject(response, RemoteSearchContactResultVO.class);
        } catch (Exception e) {
            log.error("WhatsApp发起二维码授权调用失败", e);
            return null;
        }
    }


    private Boolean requestPost(String apiName, String url, Map<String, Object> body) {
        // 发送 POST 请求
        try {
            String response = OkHttpClientConfig.post(this.getFullUrl(url), body);
            // 解析响应
            JSONObject resultJson = JSONUtil.parseObj(response);
            // {"success":true,"id":"test_user_004","message":"账号创建成功，客户端正在后台启动"}
            // {"success":false,"error":"账号ID test_user_001 已存在"}
            log.info("{}, body:{} response:{}", apiName, JacksonUtils.toJsonStr(body), JacksonUtils.toJsonStr(resultJson));
            Boolean success = resultJson.getBool("success");
            if (!success) {
                log.warn("{}失败：body:{} response:{}", apiName, JacksonUtils.toJsonStr(body), JacksonUtils.toJsonStr(resultJson));
                String err = resultJson.getStr("error");
                throw new BusinessException(apiName+",调用失败:"+err);
            }
            return success;
        } catch (Exception e) {
            log.error(apiName + "调用失败", e);
            if(e instanceof BusinessException) {
                throw (BusinessException)e;
            }
            return null;
        }
    }

    private String getFullUrl(String urlPath) {
        return whatsAppConfig.getBaseUrl() + urlPath;
    }
}
