package com.yaowu.alpha.domain.collection.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.toolscommon.utils.StreamTools;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionRawDataBizService;
import com.yaowu.alpha.domain.collection.processor.DataCollectionProcessorFactory;
import com.yaowu.alpha.domain.collection.processor.IDataCollectionProcessor;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionRawDataService;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTaskService;
import com.yaowu.alpha.domain.common.biz.IContactVerificationTaskBizService;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.bo.collection.CollectionDataContactInfoBO;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawDataPageDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionRawReportDTO;
import com.yaowu.alpha.model.dto.collection.ListDataCollectionTaskDTO;
import com.yaowu.alpha.model.dto.common.ContactVerificationQueryDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;
import com.yaowu.alpha.model.entity.collection.DataCollectionTask;
import com.yaowu.alpha.model.vo.collection.DataCollectionRawDataVO;
import com.yaowu.alpha.model.vo.common.ContactVerificationStatusVO;
import com.yaowu.alpha.utils.convertor.collection.DataCollectionRawDataConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集原始数据业务服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionRawDataBizServiceImpl implements IDataCollectionRawDataBizService {
    
    private final IDataCollectionRawDataService dataCollectionRawService;
    private final IDataCollectionTaskService dataCollectionTaskService;
    private final DataCollectionProcessorFactory processorFactory;
    private final IContactVerificationTaskBizService contactVerificationTaskBizService;

    /**
     * 分页查询数据收集原始数据
     * 支持按任务名称和任务ID进行筛选
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    @Override
    public BasePage<DataCollectionRawDataVO> pageDataCollectionRawData(DataCollectionRawDataPageDTO dto) {
        // 根据任务名称获取相关任务ID
        Set<Long> finalTaskIds = buildFinalTaskIds(dto);
        if (finalTaskIds == null) {
            return new BasePage<>(dto.pageRequest(), CollUtil.newArrayList());
        }
        dto.setTaskIds(finalTaskIds);
        // 执行分页查询
        Page<DataCollectionRawData> dataPage = dataCollectionRawService.pageByCondition(dto);
        
        // 获取任务信息映射并转换返回结果
        Map<Long, DataCollectionTask> taskMap = getTaskMap(dataPage.getRecords());
        BasePage<DataCollectionRawDataVO> voPage = DataCollectionRawDataConverter.INSTANCE.toBasePage(dataPage, taskMap);
        
        // 批量查询验证状态并设置到VO中
        if (CollUtil.isNotEmpty(voPage.getRecords())) {
            enrichWithVerificationStatus(voPage.getRecords());
        }
        
        return voPage;
    }

    /**
     * 构建最终的任务ID集合
     * 将查询条件中的任务ID与按任务名称查询到的任务ID合并
     *
     * @param dto 查询条件
     * @return 最终的任务ID集合
     */
    private Set<Long> buildFinalTaskIds(DataCollectionRawDataPageDTO dto) {
        Set<Long> queryTaskIds = dto.getTaskIds();
        
        // 如果没有任务名称条件，直接返回原有的任务ID
        if (StrUtil.isBlank(dto.getTaskName())) {
            return CollUtil.newHashSet();
        }
        
        // 根据任务名称查询相关任务
        List<DataCollectionTask> tasksByName = dataCollectionTaskService.listByCondition(
            ListDataCollectionTaskDTO.builder()
                .taskName(dto.getTaskName())
                .build()
        );
        
        // 如果按任务名称查询不到任何任务，返回空集合（这样后续查询会返回空列表）
        if (CollUtil.isEmpty(tasksByName)) {
            return null;
        }
        
        Set<Long> taskIdsByName = StreamTools.toSet(tasksByName, DataCollectionTask::getId);
        // 合并两个任务ID集合
        Set<Long> mergedTaskIds = new HashSet<>(taskIdsByName);
        if (CollUtil.isNotEmpty(queryTaskIds)) {
            taskIdsByName.addAll(queryTaskIds);
        }
        return mergedTaskIds;
    }

    /**
     * 上报数据采集结果
     *
     * @param dto 上报数据
     * @return 创建的原始数据ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long reportDataCollectionResult(DataCollectionRawReportDTO dto) {
        // 获取任务信息
        DataCollectionTask task = getAndValidateTask(dto.getTaskId());
        
        // 根据任务的采集渠道获取对应的处理器
        IDataCollectionProcessor processor = processorFactory.getProcessor(task.getCollectionChannel());
        
        // 解析并处理数据
        DataCollectionRawData rawData = processor.processData(dto.getTaskId(), dto.getDataJson());
        
        // 保存到数据库
        boolean saved = dataCollectionRawService.save(rawData);
        if (!saved) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据保存失败");
        }
        
        log.info("成功上报数据采集结果，任务ID：{}，数据ID：{}", dto.getTaskId(), rawData.getId());
        return rawData.getId();
    }

    
    /**
     * 获取并验证任务存在性
     */
    private DataCollectionTask getAndValidateTask(Long taskId) {
        DataCollectionTask task = dataCollectionTaskService.getById(taskId);
        if (task == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据采集任务不存在");
        }
        return task;
    }

    /**
     * 批量查询验证状态并设置到VO中
     */
    private void enrichWithVerificationStatus(List<DataCollectionRawDataVO> voList) {
        List<String> allEmails = collectAllEmails(voList);
        List<String> allWhatsAppPhones = collectAllWhatsAppPhones(voList);
        
        Map<String, Integer> emailStatusMap = queryEmailVerificationStatus(allEmails);
        Map<String, Integer> whatsAppStatusMap = queryWhatsAppVerificationStatus(allWhatsAppPhones);
        
        voList.forEach(vo -> {
            setEmailVerificationStatus(vo, emailStatusMap);
            setWhatsAppVerificationStatus(vo, whatsAppStatusMap);
        });
    }
    
    /**
     * 收集所有邮箱地址
     *
     * @param voList 数据收集原始数据VO列表
     * @return 去重后的邮箱地址列表
     */
    private List<String> collectAllEmails(List<DataCollectionRawDataVO> voList) {
        return voList.stream()
                .filter(vo -> CollUtil.isNotEmpty(vo.getEmails()))
                .flatMap(vo -> vo.getEmails().stream())
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 收集所有WhatsApp号码
     *
     * @param voList 数据收集原始数据VO列表
     * @return 去重后的WhatsApp号码列表
     */
    private List<String> collectAllWhatsAppPhones(List<DataCollectionRawDataVO> voList) {
        return voList.stream()
                .filter(vo -> vo.getContactInfo() != null && CollUtil.isNotEmpty(vo.getContactInfo().getContacts()))
                .flatMap(vo -> vo.getContactInfo().getContacts().stream())
                .map(CollectionDataContactInfoBO.ContactItem::getPhone)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 设置邮箱验证状态
     *
     * @param vo 数据收集原始数据VO
     * @param emailStatusMap 邮箱验证状态映射
     */
    private void setEmailVerificationStatus(DataCollectionRawDataVO vo, Map<String, Integer> emailStatusMap) {
        Optional.ofNullable(vo.getEmails())
                .filter(CollUtil::isNotEmpty)
                .ifPresent(emails -> {
                    List<DataCollectionRawDataVO.EmailWithStatus> emailsWithStatus = emails.stream()
                            .map(email -> DataCollectionRawDataVO.EmailWithStatus.builder()
                                    .email(email)
                                    .validationStatus(emailStatusMap.get(email))
                                    .build())
                            .collect(Collectors.toList());
                    vo.setEmailsWithStatus(emailsWithStatus);
                });
    }
    
    /**
     * 设置WhatsApp验证状态
     *
     * @param vo 数据收集原始数据VO
     * @param whatsAppStatusMap WhatsApp验证状态映射
     */
    private void setWhatsAppVerificationStatus(DataCollectionRawDataVO vo, Map<String, Integer> whatsAppStatusMap) {
        Optional.ofNullable(vo.getContactInfo())
                .map(CollectionDataContactInfoBO::getContacts)
                .filter(CollUtil::isNotEmpty)
                .ifPresent(contacts -> contacts.forEach(contact -> {
                    if (StrUtil.isNotBlank(contact.getPhone())) {
                        Integer status = whatsAppStatusMap.get(contact.getPhone());
                        contact.setWhatsAppVerificationStatus(status);
                    }
                }));
    }
    
    /**
     * 批量查询邮箱验证状态
     */
    private Map<String, Integer> queryEmailVerificationStatus(List<String> emails) {
        if (CollUtil.isEmpty(emails)) {
            return Map.of();
        }
        
        ContactVerificationQueryDTO queryDTO = new ContactVerificationQueryDTO();
        queryDTO.setContactType(ContactVerificationContactTypeEnum.EMAIL.name());
        queryDTO.setContacts(emails);
        
        List<ContactVerificationStatusVO> statusList = contactVerificationTaskBizService.queryVerificationStatus(queryDTO);
        
        return statusList.stream()
                .filter(status -> StrUtil.isNotBlank(status.getContact()) && status.getValidationStatus() != null)
                .collect(Collectors.toMap(
                    ContactVerificationStatusVO::getContact,
                    ContactVerificationStatusVO::getValidationStatus,
                    (existing, replacement) -> existing
                ));
    }
    
    /**
     * 批量查询WhatsApp验证状态
     */
    private Map<String, Integer> queryWhatsAppVerificationStatus(List<String> phones) {
        if (CollUtil.isEmpty(phones)) {
            return Map.of();
        }
        
        ContactVerificationQueryDTO queryDTO = new ContactVerificationQueryDTO();
        queryDTO.setContactType(ContactVerificationContactTypeEnum.WHATSAPP.name());
        queryDTO.setContacts(phones);
        
        List<ContactVerificationStatusVO> statusList = contactVerificationTaskBizService.queryVerificationStatus(queryDTO);
        
        return statusList.stream()
                .filter(status -> StrUtil.isNotBlank(status.getContact()) && status.getValidationStatus() != null)
                .collect(Collectors.toMap(
                    ContactVerificationStatusVO::getContact,
                    ContactVerificationStatusVO::getValidationStatus,
                    (existing, replacement) -> existing
                ));
    }

    /**
     * 获取任务信息映射
     */
    private Map<Long, DataCollectionTask> getTaskMap(List<DataCollectionRawData> records) {
        if (records == null || records.isEmpty()) {
            return Map.of();
        }
        
        // 收集所有任务ID
        Set<Long> taskIds = records.stream()
            .map(DataCollectionRawData::getTaskId)
            .collect(Collectors.toSet());
        
        // 批量查询任务信息，返回完整的任务对象
        return dataCollectionTaskService.listByIds(taskIds)
            .stream()
            .collect(Collectors.toMap(
                DataCollectionTask::getId,
                task -> task
            ));
    }
}