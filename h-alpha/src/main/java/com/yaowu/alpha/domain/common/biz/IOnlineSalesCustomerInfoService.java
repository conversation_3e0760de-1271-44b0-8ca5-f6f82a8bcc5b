package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.model.dto.customer.CustomerAuthorizerCheckDTO;
import com.yaowu.alpha.model.dto.onlinesales.CrmOnlineSalesCustomerAddDTO;
import com.yaowu.alpha.model.vo.customer.CustomerOpVO;

/**
 * <AUTHOR>
 * @date 2025/3/16 16:56
 */
public interface IOnlineSalesCustomerInfoService {

    CustomerOpVO add(CrmOnlineSalesCustomerAddDTO customerAddDTO);

    boolean checkAuthorizerNameIdNumber(CustomerAuthorizerCheckDTO dto);
}
