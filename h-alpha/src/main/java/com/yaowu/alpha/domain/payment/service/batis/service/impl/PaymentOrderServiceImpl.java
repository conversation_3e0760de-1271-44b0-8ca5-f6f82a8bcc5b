package com.yaowu.alpha.domain.payment.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.payment.service.batis.mapper.PaymentOrderMapper;
import com.yaowu.alpha.domain.payment.service.batis.service.IPaymentOrderService;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryDTO;
import com.yaowu.alpha.model.entity.payment.PaymentOrder;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/7-20:36
 */
@Service
public class PaymentOrderServiceImpl extends ServiceImpl<PaymentOrderMapper, PaymentOrder> implements IPaymentOrderService {

    @Override
    public Page<PaymentOrder> pageByCondition(PaymentOrderQueryDTO dto) {
        LambdaQueryWrapper<PaymentOrder> wrapper = wrapper(dto);
        if (wrapper.isEmptyOfWhere()) {
            return dto.pageRequest();
        }
        return page(dto.pageRequest(), wrapper);
    }

    private LambdaQueryWrapper<PaymentOrder> wrapper(PaymentOrderQueryDTO dto) {
        LambdaQueryWrapper<PaymentOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(dto.getOrderCode() != null, PaymentOrder::getOrderCode, dto.getOrderCode());
        wrapper.eq(dto.getTenantId() != null, PaymentOrder::getTenantId, dto.getTenantId());
        wrapper.in(CollUtil.isNotEmpty(dto.getTenantIds()), PaymentOrder::getTenantId, dto.getTenantIds());
        wrapper.in(CollUtil.isNotEmpty(dto.getIds()), PaymentOrder::getId, dto.getIds());
        wrapper.in(CollUtil.isNotEmpty(dto.getOrderStatuses()), PaymentOrder::getOrderStatus, dto.getOrderStatuses());
        wrapper.orderByDesc(PaymentOrder::getCreateTime);
        return wrapper;
    }
}
