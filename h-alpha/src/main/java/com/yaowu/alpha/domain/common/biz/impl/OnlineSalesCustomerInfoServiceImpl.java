package com.yaowu.alpha.domain.common.biz.impl;

import com.freedom.security.common.SecurityContext;
import com.freedom.security.common.UserDetailsDto;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.exception.ErrorCodeException;
import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.domain.common.biz.IOnlineSalesCustomerInfoService;
import com.yaowu.alpha.domain.common.remote.ICustomerServiceRemoteBizService;
import com.yaowu.alpha.domain.common.remote.IHeraRemoteBizService;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.dto.customer.CustomerAuthorizerCheckDTO;
import com.yaowu.alpha.model.dto.onlinesales.CrmOnlineSalesCustomerAddDTO;
import com.yaowu.alpha.model.vo.customer.CustomerOpVO;
import com.yaowu.alpha.utils.convertor.customer.OnlineSalesCustomerInfoMapstruct;
import com.yaowu.alpha.utils.onlinesales.OnlineSaleBdUtil;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteMerchantCustomerAuthorizerCheckDTO;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteOnlineSalesCustomerAddDTO;
import com.yaowu.customerserviceapi.model.vo.customer.RemoteCustomerOpVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/16 16:56
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OnlineSalesCustomerInfoServiceImpl implements IOnlineSalesCustomerInfoService {

    private final ICustomerServiceRemoteBizService customerServiceRemoteBizService;

    private final IHeraRemoteBizService heraRemoteBizService;

    private final CommonConfig commonConfig;

    @Override
    public CustomerOpVO add(CrmOnlineSalesCustomerAddDTO dto) {
        //校验授权人名称和身份证是否正确
        CustomerAuthorizerCheckDTO crmCustomerAuthorizerCheckDTO =
                OnlineSalesCustomerInfoMapstruct.INSTANCE.toCustomerAuthorizerCheck(dto.getName(), dto.getIdCardNo());
        this.checkAuthorizerNameIdNumber(crmCustomerAuthorizerCheckDTO);
        final UserDetailsDto currentUser = SecurityContext.getCurrentUser();
        boolean isLogin = Objects.nonNull(currentUser);
        boolean belongOnlineSales = OnlineSaleBdUtil.isBelongOnlineSalesHasEditAbility(false);
        ErrorCodeException.condition(!belongOnlineSales && isLogin, ErrorCodeEnum.ONLINE_SALES_VALIDATE_FAIL);

        if (dto.getMerchantId() == null) {
            dto.setMerchantId(commonConfig.getPlatformMerchantId());
        }
        if (dto.getStoreId() == null) {
            dto.setStoreId(commonConfig.getPlatformStoreId());
        }
        if (dto.getPlatformSalesBdId() == null && isLogin) {
            dto.setPlatformSalesBdId(currentUser.getUserId());
        }

        RemoteOnlineSalesCustomerAddDTO remoteOnlineSalesCustomerAddDTO =
                OnlineSalesCustomerInfoMapstruct.INSTANCE.toOnlineSalesCustomerAddDTO(dto);
        remoteOnlineSalesCustomerAddDTO.setSalesBdId(0L);
        RemoteCustomerOpVO customer = customerServiceRemoteBizService.addOnlineSalesCustomer(remoteOnlineSalesCustomerAddDTO);
        heraRemoteBizService.addGeneralCustomerInfo(OnlineSalesCustomerInfoMapstruct.INSTANCE.toRemoteOmkGeneralCustomerAddDTO(dto));
        return OnlineSalesCustomerInfoMapstruct.INSTANCE.toCustomerOpVO(customer);
    }

    @Override
    public boolean checkAuthorizerNameIdNumber(CustomerAuthorizerCheckDTO dto) {
        RemoteMerchantCustomerAuthorizerCheckDTO remoteDTO =
                OnlineSalesCustomerInfoMapstruct.INSTANCE.toRemoteMerchantCustomerAuthorizerCheckDTO(dto);

        Boolean checkResult = customerServiceRemoteBizService.checkAuthorizerNameIdNumber(remoteDTO);
        ;
        BusinessException.condition(!Boolean.TRUE.equals(checkResult),
                ErrorCodeEnum.CUSTOMER_WARN_NAME_ID_NUMBER_MISMATCH.getCode(), ErrorCodeEnum.CUSTOMER_WARN_NAME_ID_NUMBER_MISMATCH.getDesc(),
                remoteDTO.getAuthorizerName(), remoteDTO.getAuthorizerIdNumber());
        return true;
    }
}
