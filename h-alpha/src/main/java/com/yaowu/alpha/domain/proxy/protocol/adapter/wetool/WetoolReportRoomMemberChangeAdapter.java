package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberChangeNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportRoomMemberChangeDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 群人数变化
 */
@Component
public class WetoolReportRoomMemberChangeAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportRoomMemberChangeDTO, ReportRoomMemberChangeNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_ROOM_MEMBER_CHANGE.equals(action);
    }

    @Override
    public ReportRoomMemberChangeNoticeRequestDTO transferRequest(WetoolWexinReportRoomMemberChangeDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportRoomMemberChangeRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
