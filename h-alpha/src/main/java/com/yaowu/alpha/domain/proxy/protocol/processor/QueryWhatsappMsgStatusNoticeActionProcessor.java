package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.yaowu.alpha.domain.proxy.control.biz.IProxyNoticeControlBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.QueryWhatsappMsgStatusNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 查询WhatsApp消息状态处理器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class QueryWhatsappMsgStatusNoticeActionProcessor implements INoticeActionProcessor<QueryWhatsappMsgStatusNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyNoticeControlBizService proxyNoticeControlBizService;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof QueryWhatsappMsgStatusNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(QueryWhatsappMsgStatusNoticeRequestDTO request) {
        log.info("开始处理查询WhatsApp消息状态通知, proxyId: {}, taskId: {}, msgId: {}, queryType: {}", 
                request.getProxyId(), request.getTask_id(), request.getMsg_id(), request.getQuery_type());
        try {
            // 调用业务服务处理
            proxyNoticeControlBizService.queryWhatsappMsgStatus(request);
            log.info("查询WhatsApp消息状态处理完成, proxyId: {}, taskId: {}", request.getProxyId(), request.getTask_id());
        } catch (Exception e) {
            log.error("处理查询WhatsApp消息状态异常, proxyId: {}, taskId: {}, 错误: {}", request.getProxyId(), request.getTask_id(), e.getMessage(), e);
        }
        return NoticeBaseResponseVO.commonAck(request);
    }
}
