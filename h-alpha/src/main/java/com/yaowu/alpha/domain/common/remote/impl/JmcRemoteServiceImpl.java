package com.yaowu.alpha.domain.common.remote.impl;

import cn.hutool.core.collection.CollUtil;
import com.freedom.feign.utils.FeignInvokeUtils;
import com.yaowu.alpha.domain.common.remote.IJmcRemoteService;
import com.yaowu.alpha.model.bo.common.NoticeParam;
import com.yaowu.jmcapi.model.vo.message.MessageRelVO;
import com.yaowu.notice.api.v1.RemoteMessageServiceFeign;
import com.yaowu.notice.model.dto.NotifyDto;
import com.yaowu.notice.model.dto.message.RemoteTaskMessageFinishDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21 11:17
 */
@Service
public class JmcRemoteServiceImpl implements IJmcRemoteService {

    @Autowired
    private RemoteMessageServiceFeign remoteMessageServiceFeign;

    /**
     * 发送消息
     * @param noticeParam
     * @return
     */
    @Override
    public NotifyDto send(NoticeParam noticeParam) {
        return FeignInvokeUtils.convert(remoteMessageServiceFeign.send(noticeParam), NotifyDto.class);
    }

    /**
     * 完成任务消息
     * @param taskKey
     * @return
     */
    @Override
    public Boolean finishTaskMsg(String taskKey) {
        RemoteTaskMessageFinishDTO finishDTO = new RemoteTaskMessageFinishDTO();
        finishDTO.setEntityObjectId(taskKey);
        return FeignInvokeUtils.convert(remoteMessageServiceFeign.finishTask(finishDTO), Boolean.class);
    }

    /**
     * 判断任务是否存在
     * @param taskKey
     * @return
     */
    @Override
    public Boolean checkTaskExist(String taskKey) {
        return !CollUtil.isEmpty(listTaskMessage(taskKey));
    }

    /**
     * 获取任务消息列表
     * @param taskKey
     * @return
     */
    @Override
    public List<MessageRelVO> listTaskMessage(String taskKey) {
        List<String> dto = CollUtil.newArrayList(taskKey);
        return FeignInvokeUtils.convertList(remoteMessageServiceFeign.listMessageRelByEntityObjectIds(dto), MessageRelVO.class);
    }
}
