package com.yaowu.alpha.domain.common.remote;

import com.yaowu.customerserviceapi.model.dto.customer.RemoteCustomerPushDTO;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteMerchantCustomerAuthorizerCheckDTO;
import com.yaowu.customerserviceapi.model.dto.customer.RemoteOnlineSalesCustomerAddDTO;
import com.yaowu.customerserviceapi.model.vo.customer.RemoteCustomerOpVO;

/**
 * <AUTHOR>
 * @date 2025/3/16 17:05
 */
public interface ICustomerServiceRemoteBizService {

    Boolean pushCustomerMsgToWxBoot(RemoteCustomerPushDTO remoteDTO);

    Boolean checkAuthorizerNameIdNumber(RemoteMerchantCustomerAuthorizerCheckDTO remoteDTO);

    RemoteCustomerOpVO addOnlineSalesCustomer(RemoteOnlineSalesCustomerAddDTO remoteDTO);
}
