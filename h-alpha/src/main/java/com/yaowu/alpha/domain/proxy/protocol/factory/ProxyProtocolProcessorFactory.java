package com.yaowu.alpha.domain.proxy.protocol.factory;

import com.yaowu.alpha.domain.proxy.protocol.INoticeActionAdapter;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.utils.common.StreamUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 代理协议层处理器工程
 */
@Slf4j
@Component
@SuppressWarnings({"unchecked", "rawtypes"})
public class ProxyProtocolProcessorFactory {

    @Resource
    private List<INoticeActionAdapter> adapters;

    @Resource
    private List<INoticeActionProcessor> actionProcessors;

    public <T1, T2 extends BaseNoticeRequestDTO, R1 extends NoticeBaseResponseVO, R2> INoticeActionAdapter<T1, T2, R1, R2> findAdapterProcessor(ProxyTypeEnum proxyType, ProxyThirdTypeEnum thirdParty, String action) {
        return StreamUtil.of(adapters)
                .filter(processor -> processor.supportAction(proxyType, thirdParty, action))
                .map(processor -> (INoticeActionAdapter<T1, T2, R1, R2>) processor)
                .findFirst()
                .orElse(null);
    }

    public <T extends BaseNoticeRequestDTO, R extends NoticeBaseResponseVO> INoticeActionProcessor<T, R> findNoticeActionProcessor(T t) {
        return StreamUtil.of(actionProcessors)
                .filter(processor -> processor.supports(t))
                .map(processor -> (INoticeActionProcessor<T, R>) processor)
                .findFirst()
                .orElse(null);
    }
}
