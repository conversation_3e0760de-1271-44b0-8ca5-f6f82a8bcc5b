package com.yaowu.alpha.domain.common.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.ContactVerificationTaskMapper;
import com.yaowu.alpha.domain.common.service.batis.service.IContactVerificationTaskService;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.enums.common.ContactVerificationStatusEnum;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 联系方式验证记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContactVerificationTaskServiceImpl extends ServiceImpl<ContactVerificationTaskMapper, ContactVerificationTask> implements IContactVerificationTaskService {

    @Override
    public List<ContactVerificationTask> listByContactsAndType(List<String> contacts, ContactVerificationContactTypeEnum contactType) {
        if (CollUtil.isEmpty(contacts) || contactType == null) {
            return CollUtil.newArrayList();
        }
        
        LambdaQueryWrapper<ContactVerificationTask> wrapper = new LambdaQueryWrapper<ContactVerificationTask>()
                .eq(ContactVerificationTask::getContactType, contactType)
                .in(ContactVerificationTask::getContact, contacts)
                .orderByDesc(ContactVerificationTask::getCreateTime);
        
        return list(wrapper);
    }

    @Override
    public List<ContactVerificationTask> queryPendingTasks(int batchSize) {
        return lambdaQuery()
                .eq(ContactVerificationTask::getValidationStatus, ContactVerificationStatusEnum.PENDING)
                .le(ContactVerificationTask::getStartExecuteTime, LocalDateTime.now())
                .orderByAsc(ContactVerificationTask::getCreateTime)
                .last("LIMIT " + batchSize)
                .list();
    }



    /**
     * 检查联系方式是否已存在
     */
    private boolean isContactExists(String contact) {
        LambdaQueryWrapper<ContactVerificationTask> wrapper = new LambdaQueryWrapper<ContactVerificationTask>()
                .eq(ContactVerificationTask::getContact, contact)
                .last("LIMIT 1");
        return getOne(wrapper) != null;
    }


}
