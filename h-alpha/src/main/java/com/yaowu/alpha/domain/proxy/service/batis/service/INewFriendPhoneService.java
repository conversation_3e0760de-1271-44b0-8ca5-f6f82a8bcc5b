package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.proxy.control.NewFriendPhoneDTO;
import com.yaowu.alpha.model.entity.proxy.NewFriendPhone;

import java.util.List;

public interface INewFriendPhoneService extends IService<NewFriendPhone> {

    //根据条件查询
    List<NewFriendPhone> queryByCondition(NewFriendPhoneDTO newFriendPhoneDTO);
}
