package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.utils.DistributedLockUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * pull任务通知处理器
 * 添加分布式锁防止同一账号重复查询导致重复操作
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportPullTaskNoticeActionProcessor implements INoticeActionProcessor<BaseNoticeRequestDTO, PullTaskAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyTaskBizService taskBizService;

    private final IProxyAccountConfigService accountConfigService;

    private final DistributedLockUtil distributedLockUtil;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request.getClass().isAssignableFrom(BaseNoticeRequestDTO.class);
    }

    /**
     * 处理任务请求
     * 使用分布式锁防止同一账号重复查询
     *
     * @param request 通知请求DTO
     * @return 任务响应VO
     */
    @Override
    public PullTaskAckVO process(BaseNoticeRequestDTO request) {
        // 处理WhatsApp特殊情况：无proxyId但类型为WhatsApp的请求
        if (isWhatsAppSpecialRequest(request)) {
            return processWhatsAppSpecialRequest(request);
        }

        // 获取并验证代理账号
        ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (accountConfig == null) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return PullTaskAckVO.emptyAck(request);
        }
        // 构建分布式锁的key，基于账号ID
        String lockKey = buildLockKey(accountConfig.getId());
        try {
            return distributedLockUtil.executeWithLock(lockKey, 500L, 1500L, () -> processTaskWithLock(request));
        } catch (Exception e) {
            // 获取锁失败，返回空结果
            log.warn("获取分布式锁失败，账号ID：{}，返回空结果", accountConfig.getId());
            return PullTaskAckVO.emptyAck(request);
        }
    }

    /**
     * 构建分布式锁的key
     * 
     * @param accountId 账号ID
     * @return 锁key
     */
    private String buildLockKey(Long accountId) {
        return "wetool:pull_task:account:" + accountId;
    }

    /**
     * 在分布式锁保护下处理任务
     * 
     * @param request 请求参数
     * @return 任务响应
     */
    private PullTaskAckVO processTaskWithLock(BaseNoticeRequestDTO request) {
        // 重新获取代理账号配置（因为Function接口需要重新获取）
        ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (accountConfig == null) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return PullTaskAckVO.emptyAck(request);
        }
        
        // 记录日志
        logRequestInfo(accountConfig, request);

        // 异步更新账户更新时间
        accountConfigService.updateOnlineFlagAndUpdateTimeAsync(accountConfig.getId());
        // 获取并处理待处理任务
        return getAndProcessPendingTask(accountConfig.getId(), request);
    }

    /**
     * 判断是否为WhatsApp特殊请求（无proxyId但类型为WhatsApp）
     */
    private boolean isWhatsAppSpecialRequest(BaseNoticeRequestDTO request) {
        return StringUtil.isBlank(request.getProxyId()) &&
                ProxyThirdTypeEnum.WHAT_APP.getVal().equals(request.getProxyThirdType());
    }

    /**
     * 处理WhatsApp特殊请求
     */
    private PullTaskAckVO processWhatsAppSpecialRequest(BaseNoticeRequestDTO request) {
        log.info("whatsapp代理-轮询任务-消息：{}", JacksonUtils.toJsonStr(request));

        PullTaskAckVO taskAckVO = taskBizService.getCreateWhatsAppAccountPendingTask();
        if (taskAckVO == null) {
            log.info("===没有whatsApp待处理的轮询任务===");
            return PullTaskAckVO.emptyAck(request);
        }

        return taskAckVO;
    }

    /**
     * 记录请求信息日志
     */
    private void logRequestInfo(ProxyAccount accountConfig, BaseNoticeRequestDTO request) {
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc + "代理-轮询任务-消息：{}", JacksonUtils.toJsonStr(request));
    }

    /**
     * 获取并处理待处理任务
     */
    private PullTaskAckVO getAndProcessPendingTask(Long accountId, BaseNoticeRequestDTO request) {
        PullTaskAckVO taskAckVO = taskBizService.getAndHandleOnePendingTask(accountId);
        if (taskAckVO == null) {
            log.info("===没有待处理的轮询任务===");
            return PullTaskAckVO.emptyAck(request);
        }
        return taskAckVO;
    }
}
