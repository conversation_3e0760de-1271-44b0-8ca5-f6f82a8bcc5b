package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.enums.websocket.WebSocketEventEnum;

import java.util.Map;

/**
 * WebSocket-业务逻辑
 * <AUTHOR>
 */
public interface IWebSocketBizService {

/*    *//**
     * 主动推送用户
     *
     * @param userId    用户id
     * @param eventEnum 事件类型
     * @param params    额外参数
     *//*
    void pushToUser(Long userId, WebSocketEventEnum eventEnum, Map<String, Object> params);

    *//**
     * 主动推送用户
     *//*
    void pushToUser(Long userId, WebSocketEventEnum eventEnum);*/

}
