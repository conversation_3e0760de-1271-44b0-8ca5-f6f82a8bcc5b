package com.yaowu.alpha.domain.common.biz.impl;

import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.config.exception.AlphaException;
import com.yaowu.alpha.domain.common.biz.ILangDictBizService;
import com.yaowu.alpha.domain.common.service.batis.service.ILangDictService;
import com.yaowu.alpha.model.dto.common.LangDictParentRequest;
import com.yaowu.alpha.model.dto.common.LangDictAddRequest;
import com.yaowu.alpha.model.dto.common.LangDictQueryDTO;
import com.yaowu.alpha.model.dto.common.LangDictQueryRequest;
import com.yaowu.alpha.model.entity.common.LangDict;
import com.yaowu.alpha.model.vo.common.LangDictVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22-19:20
 */
@Service
public class LangDictBizServiceImpl implements ILangDictBizService {

    @Autowired
    private ILangDictService langDictService;

    @Override
    public List<LangDictVO> list(LangDictQueryRequest request) {
        LangDictQueryDTO dto = LangDictMapStruct.INSTANCE.toLangDictQueryDTO(request);
        dto.setChild(true);
        List<LangDict> list = langDictService.listByCondition(dto);
        return CollStreamUtil.toList(list, LangDictMapStruct.INSTANCE::toLangDictVO);
    }

    @Override
    public BasePage<LangDictVO> page(LangDictQueryRequest request) {
        LangDictQueryDTO dto = LangDictMapStruct.INSTANCE.toLangDictQueryDTO(request);
        dto.setParent(request.getParent() == null ? 0L : request.getParent());
        Page<LangDict> page = langDictService.pageByCondition(dto);
        return BasePage.simpleConvert(page, LangDictMapStruct.INSTANCE::toLangDictVO);
    }

    @Override
    public Long addOrUpdate(LangDictAddRequest request) {
        if (request.getId() != null) {
            LangDict langDict = getAndCheck(request.getId());
            langDict.setFieldValue(request.getFieldValue());
            langDict.setCategoryDesc(request.getCategoryDesc());
            langDictService.updateById(langDict);
            return langDict.getId();
        } else {
            request.checkCreateParam();
            LangDict parentDict = getAndCheck(request.getParent());
            // 查询数据
            LangDict langDict = langDictService.getByCondition(request.getLang(), parentDict.getCategoryCode(), request.getFieldName());
            if (langDict != null) {
                throw new AlphaException("字典已经存在");
            }
            langDict = LangDictMapStruct.INSTANCE.toLangDictEntity(request);
            langDict.setCategoryCode(parentDict.getCategoryCode());
            langDictService.save(langDict);
            return langDict.getId();
        }
    }

    @Override
    public Long addOrUpdateParent(LangDictParentRequest request) {
        if (request.getId() != null) {
            LangDict langDict = getAndCheck(request.getId());
            langDict.setCategoryDesc(request.getCategoryDesc());
            langDictService.updateById(langDict);
            return langDict.getId();
        } else {
            // 进行创建
            request.checkCreateParam();
            LangDict parentLangDict = langDictService.getParentLangDict(request.getCategoryCode());
            if (parentLangDict != null) {
                throw new AlphaException("字典已经存在");
            }
            parentLangDict = LangDictMapStruct.INSTANCE.toLangDictEntity(request);
            langDictService.save(parentLangDict);
            return parentLangDict.getId();
        }
    }

    public LangDict getAndCheck(Long id) {
        LangDict parentDict = langDictService.getById(id);
        if (parentDict == null) {
            throw new AlphaException("字典不存在");
        }
        return parentDict;
    }

    @Override
    public boolean delete(Long id) {
        LangDict andCheck = getAndCheck(id);
        if (andCheck.getParent() == 0) {
            // 删除下面所有的数据
            langDictService.remoteByParent(id);
        }
        langDictService.removeById(id);
        return true;
    }


    @Mapper
    interface LangDictMapStruct {

        LangDictMapStruct INSTANCE = Mappers.getMapper(LangDictMapStruct.class);

        LangDictVO toLangDictVO(LangDict langDict);

        LangDictQueryDTO toLangDictQueryDTO(LangDictQueryRequest langDictRequest);

        LangDict toLangDictEntity(LangDictAddRequest request);

        @Mapping(target = "parent", constant = "0L")
        @Mapping(target = "id", ignore = true)
        LangDict toLangDictEntity(LangDictParentRequest request);


        @Mapping(target = "parent", ignore = true)
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "categoryCode", ignore = true)
        @Mapping(target = "fieldName", ignore = true)
        @Mapping(target = "lang", ignore = true)
        void updateLangDict(LangDictAddRequest request, @MappingTarget LangDict langDict);
    }
}
