package com.yaowu.alpha.domain.notice.support.common;

import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/29
 */
@Component
public class EventClazzCache<T> {
    private final Map<Class<?>, Class<T>> clazzCache = new HashMap<>();

    public Class<T> getEventClazz(Class<?> key) {
        Class<T> targetClazz = clazzCache.get(key);
        if (targetClazz != null) {
            return targetClazz;
        }
        Type[] types = ((ParameterizedType) key.getGenericSuperclass()).getActualTypeArguments();
        targetClazz = (Class<T>) types[0];
        clazzCache.put(key, targetClazz);
        return targetClazz;
    }
}
