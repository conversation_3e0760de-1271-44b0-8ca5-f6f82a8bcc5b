package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyMerchantQuotation;

/**
 * <p>
 * 代理平台商户报价单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IProxyMerchantQuotationService extends IService<ProxyMerchantQuotation> {


    /**
     * 获取商户正在进行中的报价单
     * 通过商户id查询,同一个商户不能同时对多个商户进行报价，所以需要查询商户正在进行中的报价单
     *
     * @param merchantId
     * @return
     */
    ProxyMerchantQuotation getProcessingQuotationByMerchantId(Long merchantId);

    /**
     * 查询需求单
     *
     * @return
     */
    ProxyMerchantQuotation getProcessingQuotationByMerchantId(String userProxyId, String agentProxyId);

    /**
     * 查询需求单是否有商户报价单存在
     *
     * @return
     */
    ProxyMerchantQuotation getByCustomerRequirementId(Long customerRequirementId);

}
