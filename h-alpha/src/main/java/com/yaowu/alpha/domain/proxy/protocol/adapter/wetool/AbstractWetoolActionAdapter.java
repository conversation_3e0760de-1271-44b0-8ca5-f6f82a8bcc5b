package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.domain.proxy.protocol.INoticeActionAdapter;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.BaseWetoolRequest;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolAckConstants;
import org.springframework.util.StringUtils;

public abstract class AbstractWetoolActionAdapter<T1 extends BaseWetoolRequest, T2 extends BaseNoticeRequestDTO, R1 extends NoticeBaseResponseVO, R2 extends WetoolResponseData> implements INoticeActionAdapter<T1, T2, R1, R2> {

    public WetoolResponseData transferSuccessResponse(R2 r2,String ackType) {
        if (StringUtils.hasText(ackType)) {
            r2.setAck_type(ackType);
        } else {
            r2.setAck_type(WetoolAckConstants.COMMON_ACK);
        }
        r2.setError_code(0);
        r2.setError_reason("");
        return r2;
    }

    public WetoolResponseData transferFailResponse(String errorReason) {
        WetoolResponseData responseVO = new WetoolResponseData();
        responseVO.setAck_type(WetoolAckConstants.COMMON_ACK);
        responseVO.setError_code(1);
        responseVO.setError_reason(errorReason);
        return responseVO;
    }

    @Override
    public Boolean supportAction(ProxyTypeEnum proxyType, ProxyThirdTypeEnum thirdParty, String action) {
        return ProxyTypeEnum.WE_TOOL.equals(proxyType) && this.supportAction(action);
    }

    protected abstract Boolean supportAction(String action);

    abstract public T2 transferRequest(T1 input);

    abstract public R2 transferResponse(R1 output);
}
