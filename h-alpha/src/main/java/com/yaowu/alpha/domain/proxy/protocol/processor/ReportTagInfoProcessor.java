package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportTagInfoNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 微信标签
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportTagInfoProcessor implements INoticeActionProcessor<ReportTagInfoNoticeRequestDTO, PullTaskAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportTagInfoNoticeRequestDTO;
    }


    @Override
    public PullTaskAckVO process(ReportTagInfoNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return PullTaskAckVO.emptyAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-微信标签-消息：{}", JacksonUtils.toJsonStr(request));

        // 处理标签信息
        proxyAccountConfigBizService.handleTagInfo(request);

        // 默认空响应
        return PullTaskAckVO.emptyAck(request);
    }

}
