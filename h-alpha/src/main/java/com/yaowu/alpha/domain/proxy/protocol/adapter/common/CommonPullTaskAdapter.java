package com.yaowu.alpha.domain.proxy.protocol.adapter.common;

import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.common.CommonPullTaskRequestDTO;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.utils.contants.proxy.CommonProxyActionConstants;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * pull task通知转换处理器
 */
@Component
public class CommonPullTaskAdapter extends AbstractCommonRequestAdapter<CommonPullTaskRequestDTO, BaseNoticeRequestDTO, PullTaskAckVO, PullTaskAckVO> {

    @Override
    protected boolean support(String action) {
        return Objects.equals(CommonProxyActionConstants.PULL_TASK, action);
    }

    @Override
    public BaseNoticeRequestDTO transferRequest(CommonPullTaskRequestDTO input) {
        BaseNoticeRequestDTO requestDTO = new BaseNoticeRequestDTO();
        requestDTO.setProxyType(input.getProxyType().getValue());
        requestDTO.setProxyThirdType(input.getProxyThirdType().getValue());
        requestDTO.setProxyId(input.getAccountProxyId());
        return requestDTO;
    }

    @Override
    public PullTaskAckVO transferResponse(PullTaskAckVO output) {
        return output;
    }
}
