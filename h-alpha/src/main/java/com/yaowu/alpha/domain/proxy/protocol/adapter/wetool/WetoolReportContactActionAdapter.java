package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportContactNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportContactRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinReportContactMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信通讯录上报
 */
@Component
public class WetoolReportContactActionAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportContactRequestDTO, ReportContactNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_CONTACT.equals(action);
    }

    @Override
    public ReportContactNoticeRequestDTO transferRequest(WetoolWexinReportContactRequestDTO input) {
        return WetoolWexinReportContactMapStruct.INSTANCE.toReportContactRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }
}
