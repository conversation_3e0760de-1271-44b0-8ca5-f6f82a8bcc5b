package com.yaowu.alpha.domain.collection.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.freedom.web.exception.BusinessException;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionInstructionBizService;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionTaskBizService;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionRawDataService;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTaskService;
import com.yaowu.alpha.enums.collection.DataCollectionChannelEnum;
import com.yaowu.alpha.enums.collection.DataCollectionTaskStatusEnum;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.bo.collection.GmapDataCollectionParam;
import com.yaowu.alpha.model.bo.collection.TaskDataCountBO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskPageDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskUpdateDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionTask;
import com.yaowu.alpha.model.vo.collection.DataCollectionTaskVO;
import com.yaowu.alpha.utils.TransactionUtils;
import com.yaowu.alpha.utils.common.EnumUtil;
import com.yaowu.alpha.utils.convertor.collection.DataCollectionTaskConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 数据采集任务业务服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionTaskBizServiceImpl implements IDataCollectionTaskBizService {
    
    private final IDataCollectionTaskService dataCollectionTaskService;
    private final IDataCollectionRawDataService dataCollectionRawDataService;
    private final IDataCollectionInstructionBizService dataCollectionInstructionBizService;
    private final TransactionUtils transactionUtils;
    
    @Override
    public BasePage<DataCollectionTaskVO> pageDataCollectionTasks(DataCollectionTaskPageDTO dto) {
        // 调用数据层进行分页查询
        Page<DataCollectionTask> page = dataCollectionTaskService.pageByCondition(dto);
        
        // 转换为VO
        BasePage<DataCollectionTaskVO> result = DataCollectionTaskConverter.INSTANCE.toBasePage(page);
        
        // 批量统计采集数据数量并设置到VO中
        fillCollectedDataCount(result.getRecords());
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDataCollectionTask(DataCollectionTaskCreateDTO dto) {
        // 转换DTO为Entity
        DataCollectionTask task = DataCollectionTaskConverter.INSTANCE.toEntity(dto);
        
        // 转换并验证采集渠道
        DataCollectionChannelEnum collectionChannelEnum = convertAndValidateChannel(dto.getCollectionChannel());
        task.setCollectionChannel(collectionChannelEnum);
        
        // 设置采集参数
        task.setCollectionParam(JSONUtil.toJsonStr(buildCollectionParam(dto)));
        
        // 设置初始状态
        task.setTaskStatus(DataCollectionTaskStatusEnum.PENDING);
        
        // 设置预计开始执行时间，如果未指定则设为当前时间
        if (task.getStartExecuteTime() == null) {
            task.setStartExecuteTime(LocalDateTime.now());
        }
        
        // 保存任务
        dataCollectionTaskService.save(task);
        log.info("成功创建数据采集任务，ID：{}", task.getId());
        return task.getId();
    }
    
    @Override
    public DataCollectionTaskVO getDataCollectionTaskDetail(Long id) {
        DataCollectionTask task = dataCollectionTaskService.getById(id);
        if (task == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据采集任务不存在");
        }
        
        DataCollectionTaskVO taskVO = DataCollectionTaskConverter.INSTANCE.toVO(task);
        
        // 填充采集数据数量
        fillCollectedDataCount(List.of(taskVO));
        
        return taskVO;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDataCollectionTask(DataCollectionTaskUpdateDTO dto) {
        // 获取并验证任务
        DataCollectionTask existingTask = getAndValidateTask(dto.getId());
        
        // 检查任务状态
        validateTaskStatusForEdit(existingTask);
        
        // 转换并设置渠道枚举
        DataCollectionChannelEnum collectionChannelEnum = convertAndValidateChannel(dto.getCollectionChannel());
        
        // 更新任务信息
        updateTaskFields(existingTask, dto, collectionChannelEnum);
        
        // 保存更新
        saveTaskUpdate(existingTask, dto.getId());
        
        return true;
    }
    

    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startDataCollectionTask(Long id) {
        return executeTaskOperation(id, "启动", this::canStartTask, (task, taskId) -> {
            // 更新任务状态为待执行
            task.setTaskStatus(DataCollectionTaskStatusEnum.PENDING_EXECUTION);
            task.setExecuteTime(LocalDateTime.now());
            
            // 解析采集参数并创建GMap数据采集指令任务
            GmapDataCollectionParam gmapParam = JSONUtil.toBean(task.getCollectionParam(), GmapDataCollectionParam.class);
            return dataCollectionInstructionBizService.createGMapDataCollectionInstruction(taskId, gmapParam);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pauseDataCollectionTask(Long id) {
        return executeTaskOperation(id, "暂停", this::canPauseTask, (task, taskId) -> {
            // 更新暂停状态
            task.setPauseFlag(true);
            task.setPauseTime(LocalDateTime.now());
            
            // 创建暂停指令任务
            return dataCollectionInstructionBizService.createPauseInstruction(taskId);
        });
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resumeDataCollectionTask(Long id) {
        return executeTaskOperation(id, "恢复", this::canResumeTask, (task, taskId) -> {
            // 更新恢复状态
            task.setPauseFlag(false);
            
            // 创建恢复指令任务
            return dataCollectionInstructionBizService.createResumeInstruction(taskId);
        });
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean terminateDataCollectionTask(Long id) {
        return executeTaskOperation(id, "终止", this::canTerminateTask, (task, taskId) -> {
            // 更新任务状态为终止
            task.setTaskStatus(DataCollectionTaskStatusEnum.TERMINATION);
            task.setFinishTime(LocalDateTime.now());
            
            // 创建终止指令任务
            return dataCollectionInstructionBizService.createTerminateInstruction(taskId);
        });
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeDataCollectionTask(Long id) {
        return executeTaskOperation(id, "完成", this::canCompleteTask, (task, taskId) -> {
            // 更新任务状态为已完成
            task.setTaskStatus(DataCollectionTaskStatusEnum.COMPLETED);
            task.setFinishTime(LocalDateTime.now());
            
            // 完成操作不需要创建指令任务
            return null;
        });
    }

    @Override
    public void handleCreateDataCollectionTaskResult(Long collectionTaskId, Boolean success, String errorReason) {
        log.info("处理数据采集任务指令执行结果，任务ID：{}，执行结果：{}，错误原因：{}",
                collectionTaskId, success, errorReason);

        try {
            // 获取采集任务
            DataCollectionTask task = dataCollectionTaskService.getById(collectionTaskId);
            if (task == null) {
                log.warn("未找到对应的采集任务，任务ID：{}", collectionTaskId);
                return;
            }

            log.info("开始更新采集任务状态，任务ID：{}，当前状态：{}，指令执行结果：{}",
                    collectionTaskId, task.getTaskStatus(), success);

            if (Boolean.TRUE.equals(success)) {
                // 指令执行成功：将任务状态从"待执行"变更为"执行中"
                handleCreateDataCollectionTaskSuccessResult(task, collectionTaskId);
            } else {
                // 指令执行失败：将任务状态变更为"执行异常"
                handleCreateDataCollectionTaskFailureResult(task, collectionTaskId, errorReason);
            }

        } catch (Exception e) {
            log.error("处理数据采集任务指令执行结果时发生异常，任务ID：{}", collectionTaskId, e);
        }
    }

    /**
     * 验证任务状态是否允许编辑
     */
    private void validateTaskStatusForEdit(DataCollectionTask task) {
        if (task.getTaskStatus() != DataCollectionTaskStatusEnum.PENDING) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(),
                    "只有待启用状态的任务才可编辑，当前状态：" + task.getTaskStatus().getDesc());
        }
    }

    /**
     * 转换并验证采集渠道
     */
    private DataCollectionChannelEnum convertAndValidateChannel(Integer channelValue) {
        DataCollectionChannelEnum collectionChannelEnum = EnumUtil.fromValue(channelValue, DataCollectionChannelEnum.class);
        if (collectionChannelEnum == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "不支持的采集渠道类型：" + channelValue);
        }
        return collectionChannelEnum;
    }

    /**
     * 组装采集参数对象
     */
    private GmapDataCollectionParam buildCollectionParam(DataCollectionTaskUpdateDTO dto) {
        return GmapDataCollectionParam.builder()
                .country(dto.getCountry())
                .cities(dto.getCities())
                .keywords(dto.getKeywords())
                .build();
    }

    /**
     * 组装采集参数对象
     */
    private GmapDataCollectionParam buildCollectionParam(DataCollectionTaskCreateDTO dto) {
        return GmapDataCollectionParam.builder()
                .country(dto.getCountry())
                .cities(dto.getCities())
                .keywords(dto.getKeywords())
                .build();
    }

    /**
     * 更新任务字段
     */
    private void updateTaskFields(DataCollectionTask task, DataCollectionTaskUpdateDTO dto, DataCollectionChannelEnum channel) {
        task.setTaskName(dto.getTaskName());
        task.setCollectionChannel(channel);
        task.setCollectionParam(JSONUtil.toJsonStr(buildCollectionParam(dto)));

        if (dto.getStartExecuteTime() != null) {
            task.setStartExecuteTime(dto.getStartExecuteTime());
        }
    }

    /**
     * 保存任务更新
     */
    private void saveTaskUpdate(DataCollectionTask task, Long taskId) {
        boolean updated = dataCollectionTaskService.updateById(task);
        if (!updated) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "编辑任务失败");
        }
        log.info("成功编辑数据采集任务，ID：{}", taskId);
    }
    
    /**
     * 获取并验证任务是否存在
     */
    private DataCollectionTask getAndValidateTask(Long id) {
        DataCollectionTask task = dataCollectionTaskService.getById(id);
        if (task == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据采集任务不存在");
        }
        return task;
    }
    
    /**
     * 检查任务是否可以启动
     */
    private boolean canStartTask(DataCollectionTask task) {
        return task.getTaskStatus() == DataCollectionTaskStatusEnum.PENDING;
    }

    /**
     * 检查任务是否可以暂停
     */
    private boolean canPauseTask(DataCollectionTask task) {
        return (task.getTaskStatus() == DataCollectionTaskStatusEnum.PENDING_EXECUTION
            || task.getTaskStatus() == DataCollectionTaskStatusEnum.EXECUTING)
            && !Boolean.TRUE.equals(task.getPauseFlag()); // 确保任务当前未处于暂停状态
    }
    
    /**
     * 检查任务是否可以恢复
     */
    private boolean canResumeTask(DataCollectionTask task) {
        return Boolean.TRUE.equals(task.getPauseFlag()) && 
               (task.getTaskStatus() == DataCollectionTaskStatusEnum.PENDING_EXECUTION
                || task.getTaskStatus() == DataCollectionTaskStatusEnum.EXECUTING);
    }
    
    /**
     * 检查任务是否可以终止
     */
    private boolean canTerminateTask(DataCollectionTask task) {
        return task.getTaskStatus() == DataCollectionTaskStatusEnum.PENDING_EXECUTION
            || task.getTaskStatus() == DataCollectionTaskStatusEnum.EXECUTING;
    }

    /**
     * 检查任务是否可以完成
     */
    private boolean canCompleteTask(DataCollectionTask task) {
        return task.getTaskStatus() == DataCollectionTaskStatusEnum.PENDING_EXECUTION
            || task.getTaskStatus() == DataCollectionTaskStatusEnum.EXECUTING;
    }
    
    /**
     * 批量填充采集数据统计
     * 
     * @param voList 任务VO列表
     */
    private void fillCollectedDataCount(List<DataCollectionTaskVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        // 提取任务ID列表
        List<Long> taskIds = voList.stream()
            .map(DataCollectionTaskVO::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(taskIds)) {
            return;
        }
        
        // 批量统计采集数据数量
        List<TaskDataCountBO> countList = dataCollectionRawDataService.countDataByTaskIds(taskIds);
        
        // 设置统计结果到VO中
        voList.forEach(vo -> {
            Long count = countList.stream()
                .filter(taskDataCountBO -> taskDataCountBO.getTaskId().equals(vo.getId()))
                .map(TaskDataCountBO::getDataCount)
                .findFirst()
                .orElse(0L);
            vo.setCollectedDataCount(count);
        });
    }

    /**
     * 通用任务操作执行模板
     * 
     * @param id 任务ID
     * @param operationName 操作名称
     * @param canExecute 状态检查函数
     * @param executeOperation 执行操作函数(任务更新+指令创建)
     * @return 操作结果
     */
    private Boolean executeTaskOperation(Long id, String operationName, 
                                       Predicate<DataCollectionTask> canExecute,
                                       BiFunction<DataCollectionTask, Long, Long> executeOperation) {
        log.info("{}数据采集任务，ID：{}", operationName, id);
        
        // 获取并验证任务
        DataCollectionTask task = getAndValidateTask(id);
        
        // 检查任务状态是否允许操作
        if (!canExecute.test(task)) {
            String errorMessage = "任务状态不允许" + operationName + "，当前状态：" + task.getTaskStatus().getDesc();
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
        }
        
        // 执行操作（任务更新 + 指令创建）
        Long instructionTaskId = executeOperation.apply(task, id);
        
        // 保存任务更新
        boolean updated = dataCollectionTaskService.updateById(task);
        if (!updated) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), 
                operationName + "任务失败");
        }
        
        // 记录指令创建日志（如果有创建指令）
        if (instructionTaskId != null) {
            log.info("已创建{}指令任务，采集任务ID：{}，指令任务ID：{}", operationName, id, instructionTaskId);
        }
        
        log.info("成功{}数据采集任务，ID：{}", operationName, id);
        return true;
    }
    
    /**
     * 处理指令执行成功的情况
     * 
     * @param task 采集任务
     * @param collectionTaskId 采集任务ID
     */
    private void handleCreateDataCollectionTaskSuccessResult(DataCollectionTask task, Long collectionTaskId) {
        // 只有处于"待执行"状态的任务才能变更为"执行中"
        if (task.getTaskStatus() != DataCollectionTaskStatusEnum.PENDING_EXECUTION) {
            log.warn("采集任务状态不是待执行，无法变更为执行中，任务ID：{}，当前状态：{}", 
                task.getId(), task.getTaskStatus());
            return;
        }
        
        // 更新任务状态为执行中
        task.setTaskStatus(DataCollectionTaskStatusEnum.EXECUTING);
        task.setExecuteTime(LocalDateTime.now());
        
        // 保存更新
        boolean updated = dataCollectionTaskService.updateById(task);
        if (updated) {
            log.info("成功将采集任务状态更新为执行中，任务ID：{}", collectionTaskId);
        } else {
            log.error("更新采集任务状态失败，任务ID：{}", collectionTaskId);
        }
    }
    
    /**
     * 处理指令执行失败的情况
     * 
     * @param task 采集任务
     * @param collectionTaskId 采集任务ID
     * @param errorReason 失败原因
     */
    private void handleCreateDataCollectionTaskFailureResult(DataCollectionTask task, Long collectionTaskId, String errorReason) {
        // 更新任务状态为异常
        task.setTaskStatus(DataCollectionTaskStatusEnum.EXCEPTION);
        task.setErrorTime(LocalDateTime.now());
        task.setErrorMsg(errorReason);
        
        // 保存更新
        boolean updated = dataCollectionTaskService.updateById(task);
        if (updated) {
            log.info("成功将采集任务状态更新为执行异常，任务ID：{}，错误原因：{}", 
                collectionTaskId, errorReason);
        } else {
            log.error("更新采集任务状态失败，任务ID：{}", collectionTaskId);
        }
    }
} 