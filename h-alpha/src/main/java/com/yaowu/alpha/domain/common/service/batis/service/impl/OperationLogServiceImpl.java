package com.yaowu.alpha.domain.common.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.OperationLogMapper;
import com.yaowu.alpha.domain.common.service.batis.service.IOperationLogService;
import com.yaowu.alpha.model.entity.common.OperationLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

} 