package com.yaowu.alpha.domain.facebook.service.batis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yaowu.alpha.model.entity.facebook.FacebookGroupMember;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook群组成员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookGroupMemberMapper extends BaseMapper<FacebookGroupMember> {

    /**
     * 根据任务ID列表统计每个任务的群组成员数量
     *
     * @param taskIds 任务ID列表
     * @return 任务ID -> 群组数量的映射
     */
    @MapKey("taskId")
    List<Map<String, Object>> getGroupMemberStatisticByTaskIds(@Param("taskIds") List<Long> taskIds);

    /**
     * 根据群组ID列表统计每个任务的群组成员数量
     *
     * @param groupIds 群组ID列表
     * @return 任务ID -> 群组数量的映射
     */
    @MapKey("groupId")
    List<Map<String, Object>> getGroupMemberStatisticByGroupIds(@Param("groupIds") List<Long> groupIds);
} 