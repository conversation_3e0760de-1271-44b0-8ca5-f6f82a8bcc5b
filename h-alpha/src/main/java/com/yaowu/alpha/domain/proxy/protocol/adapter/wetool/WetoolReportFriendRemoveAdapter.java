package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportFriendRemovedNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportFriendRemoveDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 群人数变化
 */
@Component
public class WetoolReportFriendRemoveAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportFriendRemoveDTO, ReportFriendRemovedNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_FRIEND_REMOVED.equals(action);
    }

    @Override
    public ReportFriendRemovedNoticeRequestDTO transferRequest(WetoolWexinReportFriendRemoveDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportFriendRemovedRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
