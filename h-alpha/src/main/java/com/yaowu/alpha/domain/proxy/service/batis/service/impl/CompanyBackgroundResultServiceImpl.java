package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.CompanyBackgroundResultMapper;
import com.yaowu.alpha.model.entity.proxy.CompanyBackgroundResult;
import com.yaowu.alpha.domain.proxy.service.batis.service.ICompanyBackgroundResultService;
import com.yaowu.alpha.utils.common.StringUtil;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 公司背调结果表服务实现
 *
 * <AUTHOR>
 */
@Service
public class CompanyBackgroundResultServiceImpl extends ServiceImpl<CompanyBackgroundResultMapper, CompanyBackgroundResult> implements ICompanyBackgroundResultService {

    @Override
    public CompanyBackgroundResult findBackgroundResult(Long tenantId, String companyName, String industry) {
        if (Objects.isNull(tenantId) || StringUtil.isBlank(companyName)){
            return null;
        }
        return this.lambdaQuery()
                .eq(CompanyBackgroundResult::getTenantId, tenantId)
                .eq(CompanyBackgroundResult::getCompanyName, companyName)
                .eq(CompanyBackgroundResult::getIndustry, companyName)
                .one();
    }

    @Override
    public CompanyBackgroundResult findBackResultByTaskId(Long taskId) {
        if (Objects.isNull(taskId)){
            return null;
        }
        return this.lambdaQuery()
                .eq(CompanyBackgroundResult::getTaskId,taskId )
                .one();
    }
}