package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.ReportTaskResultNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportTaskResultRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinPullTaskMapStruct;
import org.springframework.stereotype.Component;

/**
 * 微信任务执行成功通知转换处理器
 */
@Component
public class WetoolReportTaskResultAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportTaskResultRequestDTO, ReportTaskResultNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_TASK_RESULT.equals(action);
    }

    @Override
    public ReportTaskResultNoticeRequestDTO transferRequest(WetoolWexinReportTaskResultRequestDTO input) {
        return WetoolWexinPullTaskMapStruct.INSTANCE.toTransferTaskResultRequest(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        super.transferSuccessResponse(responseVO, null);
        return responseVO;
    }
}
