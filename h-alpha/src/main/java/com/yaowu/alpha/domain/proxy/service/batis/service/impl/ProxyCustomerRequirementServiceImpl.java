package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.control.biz.impl.ProxyCustomerRequirementBizServiceImpl.RequirementQueryContext;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyCustomerRequirementMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyCustomerRequirementService;
import com.yaowu.alpha.model.dto.clue.CluePageDTO;
import com.yaowu.alpha.model.dto.proxy.control.ProxyCustomerRequirementQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyCustomerRequirement;
import com.yaowu.alpha.utils.common.LongUtils;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 代理平台用户需求单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
public class ProxyCustomerRequirementServiceImpl extends ServiceImpl<ProxyCustomerRequirementMapper, ProxyCustomerRequirement> implements IProxyCustomerRequirementService {

    /**
     * @param queryDTO
     * @return
     */
    @Override
    public ProxyCustomerRequirement oneByCondition(ProxyCustomerRequirementQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyCustomerRequirement> wrapper = buildConditionWrapper(queryDTO);
        wrapper.last("limit 1");
        List<ProxyCustomerRequirement> requirements = this.list(wrapper);
        if (CollUtil.isEmpty(requirements)) {
            return null;
        }
        return requirements.get(0);
    }

    /**
     * 根据条件获取需求单列表
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ProxyCustomerRequirement> listByCondition(ProxyCustomerRequirementQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyCustomerRequirement> wrapper = buildConditionWrapper(queryDTO);
        return this.list(wrapper);
    }

    /**
     * 获取最后一个未完成的需求单
     *
     * @param userProxyId
     * @param agentProxyId
     * @return
     */
    @Override
    public ProxyCustomerRequirement lastUnClosedOne(String userProxyId, String agentProxyId) {
        return lastUnClosedOne(userProxyId, agentProxyId, null, null);
    }

    @Override
    public ProxyCustomerRequirement lastUnClosedOne(String userProxyId,
                                                    String agentProxyId,
                                                    String phone,
                                                    LocalDateTime startTime) {
        return this.lambdaQuery()
                .eq(ProxyCustomerRequirement::getUserProxyId, userProxyId)
                .eq(ProxyCustomerRequirement::getAgentProxyId, agentProxyId)
                .eq(ProxyCustomerRequirement::getCloseFlag, 0)
                .ge(null != startTime, ProxyCustomerRequirement::getCreateTime, startTime)
                .orderByDesc(ProxyCustomerRequirement::getCreateTime)
                .last("limit 1")
                .one();
    }

    @Override
    public boolean saveToChannelInfo(Long id, Integer toChannel, Long toChannelTicketId) {
        if (LongUtils.isInvalid(id) || Objects.isNull(toChannel) || LongUtils.isInvalid(toChannelTicketId)) {
            return false;
        }
        ProxyCustomerRequirement proxyCustomerRequirement = this.getById(id);
        if (ObjectUtils.isNull(proxyCustomerRequirement)) {
            return false;
        }
        proxyCustomerRequirement.setToChannel(toChannel);
        proxyCustomerRequirement.setToChannelTicketId(toChannelTicketId);
        return this.updateById(proxyCustomerRequirement);
    }

    @Override
    public List<String> listFriendProxyIds() {
        List<ProxyCustomerRequirement> requirements = this.lambdaQuery()
                .select(ProxyCustomerRequirement::getUserProxyId)
                .list();
        return StreamUtil.of(requirements)
                .map(ProxyCustomerRequirement::getUserProxyId)
                .collect(Collectors.toList());

    }

    @Override
    public Page<ProxyCustomerRequirement> getRequirementPage(RequirementQueryContext context, CluePageDTO dto) {
        return buildAndExecuteQuery(context,dto);
    }

    /**
     * 添加搜索条件
     *
     * @param wrapper 查询条件包装器
     * @param context 查询上下文
     */
    private void addSearchCondition(LambdaQueryChainWrapper<ProxyCustomerRequirement> wrapper,
                                    RequirementQueryContext context) {
        List<Long> accountIds = context.getAccountIds();
        List<String> appKeys = context.getAppKeys();

        if (CollUtil.isNotEmpty(appKeys) && CollUtil.isNotEmpty(accountIds)) {
            wrapper.and(w -> w.in(ProxyCustomerRequirement::getExtractorAgentAppKey, appKeys)
                    .or()
                    .in(ProxyCustomerRequirement::getAccountId, accountIds));
        } else if (CollUtil.isNotEmpty(accountIds)) {
            wrapper.in(ProxyCustomerRequirement::getAccountId, accountIds);
        } else if (CollUtil.isNotEmpty(appKeys)) {
            wrapper.in(ProxyCustomerRequirement::getExtractorAgentAppKey, appKeys);
        } else {
            wrapper.eq(ProxyCustomerRequirement::getId, -1L);
        }
    }

    /**
     * 构建查询条件并执行分页查询
     *
     * @param context 查询上下文
     * @param dto 分页查询参数
     * @return 分页结果
     */
    private Page<ProxyCustomerRequirement> buildAndExecuteQuery(RequirementQueryContext context, CluePageDTO dto) {
        // 构建基础查询条件
        LambdaQueryChainWrapper<ProxyCustomerRequirement> wrapper = this.lambdaQuery()
                .eq(Objects.nonNull(context.getTenantId()),ProxyCustomerRequirement::getTenantId, context.getTenantId());

        // 添加搜索条件
        if (StringUtil.isNotBlank(context.getSearch())) {
            addSearchCondition(wrapper, context);
        }

        // 添加好友过滤条件
        if (context.isDisplayTenant() && CollUtil.isNotEmpty(context.getFriendProxyIds())) {
            wrapper.in(ProxyCustomerRequirement::getUserProxyId, context.getFriendProxyIds());
        }

        // 执行分页查询
        return wrapper
                .eq(Objects.nonNull(dto.getCompleteFlag()),ProxyCustomerRequirement::getCompleteFlag, dto.getCompleteFlag())
                .orderByDesc(ProxyCustomerRequirement::getCreateTime)
                .page(dto.pageRequest());
    }


    private LambdaQueryWrapper<ProxyCustomerRequirement> buildConditionWrapper(ProxyCustomerRequirementQueryDTO queryDTO) {
        LambdaQueryWrapper<ProxyCustomerRequirement> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(Objects.nonNull(queryDTO.getUserProxyId()), ProxyCustomerRequirement::getUserProxyId, queryDTO.getUserProxyId())
                .eq(Objects.nonNull(queryDTO.getAgentProxyId()), ProxyCustomerRequirement::getAgentProxyId, queryDTO.getAgentProxyId())
                .eq(queryDTO.getCompleteFlag() != null, ProxyCustomerRequirement::getCompleteFlag, Boolean.TRUE.equals(queryDTO.getCompleteFlag()) ? 1 : 0)
                .eq(queryDTO.getCloseFlag() != null, ProxyCustomerRequirement::getCloseFlag, Boolean.TRUE.equals(queryDTO.getCloseFlag()) ? 1 : 0)
                .eq(queryDTO.getTimeoutFlag() != null, ProxyCustomerRequirement::getTimeoutFlag, Boolean.TRUE.equals(queryDTO.getTimeoutFlag()) ? 1 : 0)
                .ge(queryDTO.getCreateTimeStart() != null, ProxyCustomerRequirement::getCreateTime, queryDTO.getCreateTimeStart())
                .le(queryDTO.getCreateTimeEnd() != null, ProxyCustomerRequirement::getCreateTime, queryDTO.getCreateTimeEnd())
                .ge(queryDTO.getExpectedCloseTimeStart() != null, ProxyCustomerRequirement::getExpectedCloseTime, queryDTO.getExpectedCloseTimeStart())
                .le(queryDTO.getExpectedCloseTimeEnd() != null, ProxyCustomerRequirement::getExpectedCloseTime, queryDTO.getExpectedCloseTimeEnd())
                .eq(queryDTO.getDispatchStatus() != null, ProxyCustomerRequirement::getDispatchStatus, queryDTO.getDispatchStatus())
                .ge(queryDTO.getDispatchTimeStart() != null, ProxyCustomerRequirement::getExpectedDispatchTime, queryDTO.getDispatchTimeStart())
                .le(queryDTO.getDispatchTimeEnd() != null, ProxyCustomerRequirement::getExpectedDispatchTime, queryDTO.getDispatchTimeEnd())
                .ge(queryDTO.getUpdateTimeStart() != null, ProxyCustomerRequirement::getUpdateTime, queryDTO.getUpdateTimeStart())
                .le(queryDTO.getUpdateTimeEnd() != null, ProxyCustomerRequirement::getUpdateTime, queryDTO.getUpdateTimeEnd())
                .orderBy(queryDTO.getOrderByCreateTimeAsc() != null, Boolean.TRUE.equals(queryDTO.getOrderByCreateTimeAsc()), ProxyCustomerRequirement::getCreateTime);
        return wrapper;
    }
}
