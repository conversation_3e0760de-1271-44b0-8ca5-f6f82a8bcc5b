package com.yaowu.alpha.domain.proxy.protocol.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.TaskTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberUpdateNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupInfo;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.utils.contants.RedisConstants;
import com.yaowu.alpha.utils.contants.WeChatAgentConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 上报群成员更新
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportGroupMemberUpdateActionProcessor implements INoticeActionProcessor<ReportRoomMemberUpdateNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyGroupBizService groupBizService;

    private final IProxyTaskBizService iProxyTaskBizService;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportRoomMemberUpdateNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(ReportRoomMemberUpdateNoticeRequestDTO request) {
        log.info("微信代理-上报群成员更新-消息：{}", JacksonUtils.toJsonStr(request));
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-上报群成员更新-消息：{}", JacksonUtils.toJsonStr(request));
        ProxyGroupInfo proxyGroupInfo = groupBizService.getByAccountIdAndGroupProxyId(accountConfig.getId(), request.getRoom_wxid());
        if (null == proxyGroupInfo) {
            updateGroupName(accountConfig, request);
        } else {
            // todo 更新群成员信息
            log.info("群信息已存在，群ID：{}", request.getRoom_wxid());
            // 更新群成员信息
        }
        return NoticeBaseResponseVO.commonAck(request);
    }

    /**
     * 构建确认收款的任务
     *
     * @param accountConfig 代理账户
     * @param request       请求参数
     * @return 任务响应对象
     */
    private void updateGroupName(ProxyAccount accountConfig, ReportRoomMemberUpdateNoticeRequestDTO request) {
        List<String> sourceMembers = new ArrayList<>(request.getMemberInfo_list().size());
        request.getMemberInfo_list().forEach(memberInfo -> {
            // 排除代理账号
            if (!memberInfo.getWxid().equals(accountConfig.getProxyId())) {
                sourceMembers.add(memberInfo.getWxid());
            }
        });
        if (CollUtil.isEmpty(sourceMembers) || sourceMembers.size() != 2) {
            log.info("不是系统自动创建的群，代理账号key：{}", JSONUtil.toJsonStr(request));
        }
        String groupKey = RedisConstants.WECHAT_CREATE_GROUP_ACCOUNT_ID + accountConfig.getId();
        if (Boolean.FALSE.equals(redisTemplate.hasKey(groupKey))) {
            log.info("没有创建群的代理账号信息，代理账号key：{}", groupKey);
            return;
        }
        Long size = redisTemplate.opsForList().size(groupKey);
        if (size > 0) {
            updateGroupName(sourceMembers, request, accountConfig, groupKey, size);
        }
    }

    private void updateGroupName(List<String> sourceMembers, ReportRoomMemberUpdateNoticeRequestDTO request, ProxyAccount accountConfig, String groupKey, Long size) {
        for (int i = 0; i < size; i++) {
            List<String> members = (List<String>) redisTemplate.opsForList()
                    .leftPop(groupKey);
            if (CollUtil.isEmpty(members)) {
                log.info("代理账号信息创建群的成员列表为空，代理账号key：{}", groupKey);
                continue;
            }
            if (members.containsAll(sourceMembers) && sourceMembers.containsAll(members)) {
                log.info("创建代理账号群成功，群ID：{}，群成员：{}", request.getRoom_wxid(), members);
                TaskTypeEnum taskTypeEnum = TaskTypeEnum.MODIFY_GROUP_NAME;
                List<PullTaskAckVO> taskDataList = new ArrayList<>();
                StringBuilder groupName = new StringBuilder(WeChatAgentConstants.NEW_GROUP_SERVICE_NAME);
                request.getMemberInfo_list().forEach(memberInfo -> {
                    // member第一个为客户的微信号
                    if (memberInfo.getWxid().equals(members.get(0))) {
                        groupName.append(memberInfo.getNickname());
                    }
                });
                taskDataList.add(buildUpdateGroupNameTaskAck(request.getRoom_wxid(), groupName.toString()));
                iProxyTaskBizService.createTask(accountConfig.getId(), taskTypeEnum, taskDataList);
                break;
            } else {
                // 重新放回队列
                redisTemplate.opsForList().leftPush(groupKey, members);
            }
        }
        // 删除key执行完以后删除key
        redisTemplate.opsForList().size(groupKey);
        if (size.equals(0L)) {
            redisTemplate.delete(groupKey);
        }
    }

    /**
     * 构建确认收款的任务
     *
     * @param room_wx_id 群ID
     * @param roomName   群名称
     * @return 任务响应对象
     */
    private PullTaskAckVO buildUpdateGroupNameTaskAck(String room_wx_id, String roomName) {
        PullTaskAckVO ack = new PullTaskAckVO();
        final PullTaskAckVO.UpdateGroupName updateGroupName = new PullTaskAckVO.UpdateGroupName()
                // 接收到消息后，给发送者回复消息
                .setRoom_wxid(room_wx_id).setRoom_name(roomName);
        PullTaskAckVO.ReplyTask replyTask = new PullTaskAckVO.ReplyTask()
                .setTask_type(18)
                .setTask_dict(JSONUtil.parseObj(updateGroupName));
        ack.setData(new PullTaskAckVO.TaskAck().setTask_data(replyTask));
        return ack;
    }
}
