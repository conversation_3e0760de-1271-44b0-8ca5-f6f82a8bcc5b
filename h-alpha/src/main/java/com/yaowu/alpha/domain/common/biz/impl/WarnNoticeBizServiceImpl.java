package com.yaowu.alpha.domain.common.biz.impl;

import com.google.common.collect.Maps;
import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.notice.client.NotifyClient;
import com.yaowu.notice.model.param.ChannelParam;
import com.yaowu.notice.model.param.NotifyParam;
import com.yaowu.notice.model.param.Receiver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/19 19:57
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarnNoticeBizServiceImpl implements IWarnNoticeBizService {


    private final NotifyClient notifyClient;

    @Override
    public void sendWechat(String wechatOpenId, String msg) {
        try {
            // 构建通知发送参数
            NotifyParam param = new NotifyParam();
            // 发送方式 PUSH：推送, SMS：短信, TTS：语音, EMAIL: 邮件, WX_BOT: 微信机器人
            param.getChannelParamMap().put("WX_BOT", new ChannelParam());
            // 内容支持 Markdown [这是百度](https://www.baidu.com)
            param.setContent(msg);
            // 创建接收用户
            Receiver receiver = new Receiver();
            // 设置微信机器人的Key
            receiver.setOpenId(wechatOpenId);
            param.getReceivers().add(receiver);
            // 发送
            notifyClient.send(param);
        } catch (Exception e) {
            log.warn("发送微信通知异常", e);
        }
    }

    @Override
    public void sendNoticeToWechat(String msg, String wechatOpenId, List<String> phoneList) {
        doSendWechat(msg, wechatOpenId, phoneList, "text");
    }


    private void doSendWechat(String msg, String wechatOpenId, List<String> phoneList, String msgType) {
        if (StringUtils.isNotBlank(TraceContext.traceId())) {
            msg = msg + "\n\ntraceId: " + TraceContext.traceId();
        }
        // 构建通知发送参数
        NotifyParam param = new NotifyParam();
        // 发送方式 PUSH：推送, SMS：短信, TTS：语音, EMAIL: 邮件, WX_BOT: 微信机器人
        param.getChannelParamMap().put("WX_BOT", new ChannelParam());
        // 内容支持 Markdown [这是百度](https://www.baidu.com)
        param.setContent(msg);
        if (!CollectionUtils.isEmpty(phoneList)) {
            for (String phone : phoneList) {
                //创建接收用户
                Receiver receiver = new Receiver();
                //设置微信机器人的Key
                receiver.setOpenId(wechatOpenId);
                receiver.setPhone(phone);
                param.getReceivers().add(receiver);
            }
        } else {
            Receiver receiver = new Receiver();
            receiver.setOpenId(wechatOpenId);
            param.getReceivers().add(receiver);
        }
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.putIfAbsent("msgtype", msgType);
        param.setExtParams(extParams);
        // 发送
        try {
            notifyClient.send(param);
        } catch (Exception e) {
            log.warn("发送企微通知异常", e);
        }
    }

}
