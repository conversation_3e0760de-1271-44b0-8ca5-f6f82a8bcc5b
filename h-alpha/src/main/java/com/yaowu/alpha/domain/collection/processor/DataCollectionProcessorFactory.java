package com.yaowu.alpha.domain.collection.processor;

import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.enums.collection.DataCollectionChannelEnum;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据采集处理器工厂
 * 根据采集渠道获取对应的处理器实现
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataCollectionProcessorFactory {
    
    private final List<IDataCollectionProcessor> processors;
    
    /**
     * 根据采集渠道获取对应的处理器
     * 
     * @param channel 采集渠道
     * @return 数据处理器
     */
    public IDataCollectionProcessor getProcessor(DataCollectionChannelEnum channel) {
        return processors.stream()
            .filter(processor -> processor.supports(channel))
            .findFirst()
            .orElseThrow(() -> new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), 
                "不支持的采集渠道：" + channel.getDesc()));
    }
} 