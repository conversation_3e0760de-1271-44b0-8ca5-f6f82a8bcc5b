package com.yaowu.alpha.domain.collection.processor.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.collection.processor.IDataCollectionProcessor;
import com.yaowu.alpha.enums.collection.DataCollectionChannelEnum;
import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import com.yaowu.alpha.model.bo.collection.CollectionDataContactInfoBO;
import com.yaowu.alpha.model.bo.collection.CollectionDataLocationBO;
import com.yaowu.alpha.model.bo.collection.GmapData;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Google Maps数据采集处理器
 * 处理来自Google Maps的数据格式
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
public class GmapDataCollectionProcessor implements IDataCollectionProcessor {
    
    private static final Pattern RATE_PATTERN = Pattern.compile("([0-9.]+)\\(([0-9]+)\\)");
    private static final Pattern PHONE_CLEAN_PATTERN = Pattern.compile("[^+0-9]");
    
    @Override
    public boolean supports(DataCollectionChannelEnum channel) {
        return DataCollectionChannelEnum.G_MAP == channel;
    }
    
    @Override
    public DataCollectionRawData processData(Long taskId, String rawJsonData) {
        try {
            // 先将JSON转换为具体的实体
            GmapData gmapData = parseJsonToEntity(rawJsonData);
            // 转换为DataCollectionRaw实体
            return convertToDataCollectionRawData(taskId, rawJsonData, gmapData);
            
        } catch (Exception e) {
            log.error("解析Google Maps数据失败：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据解析失败：" + e.getMessage());
        }
    }
    
    /**
     * 将JSON数据解析为GmapDataEntity实体
     */
    private GmapData parseJsonToEntity(String rawJsonData) {
        try {
            return JSONUtil.toBean(rawJsonData, GmapData.class);
        } catch (Exception e) {
            log.error("JSON转换为GmapDataEntity失败：{}", e.getMessage());
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "数据格式不正确");
        }
    }
    
    /**
     * 将GmapDataEntity转换为DataCollectionRaw实体
     */
    private DataCollectionRawData convertToDataCollectionRawData(Long taskId, String rawJsonData, GmapData gmapData) {
        DataCollectionRawData entity = new DataCollectionRawData();
        
        // 设置基础信息
        entity.setTaskId(taskId);
        entity.setTitle(gmapData.getTitle());
        entity.setCategory(gmapData.getCategory());
        entity.setCoverImageUrl(gmapData.getCoverImageUrl());
        entity.setWebSite(gmapData.getWebsite());
        entity.setRawData(rawJsonData);
        
        // 设置位置信息
        CollectionDataLocationBO location = CollectionDataLocationBO.builder()
            .address(gmapData.getAddress())
            .latitude(gmapData.getLatitude())
            .longitude(gmapData.getLongitude())
            .plusCode(gmapData.getPlusCode())
            .build();
        entity.setLocation(location);
        
        // 同时设置独立的country和city字段用于查询
        entity.setCountry(gmapData.getCountry());
        entity.setCity(gmapData.getCity());
        
        // 解析好评率和评论数
        parseRatingInfo(gmapData.getRate(), entity);
        
        // 处理联系方式
        CollectionDataContactInfoBO contactInfo = buildContactInfo(gmapData);
        entity.setContact(contactInfo);
        
        // 处理邮箱信息
        processEmailInfo(gmapData.getAllEmails(), entity);
        
        return entity;
    }
    
    /**
     * 解析好评率信息
     */
    private void parseRatingInfo(String rateStr, DataCollectionRawData entity) {
        if (StrUtil.isBlank(rateStr)) {
            return;
        }
        
        // 处理"No reviews"情况
        if ("No reviews".equalsIgnoreCase(rateStr.trim())) {
            entity.setPositiveRate(null);
            entity.setCommentCount(0L);
            return;
        }
        
        // 处理"4.5(19)"格式
        Matcher matcher = RATE_PATTERN.matcher(rateStr);
        if (matcher.find()) {
            try {
                BigDecimal positiveRate = new BigDecimal(matcher.group(1));
                Long commentCount = Long.parseLong(matcher.group(2));
                
                entity.setPositiveRate(positiveRate);
                entity.setCommentCount(commentCount);
            } catch (NumberFormatException e) {
                log.warn("解析好评率信息失败：{}", rateStr);
                // 设置默认值
                entity.setPositiveRate(null);
                entity.setCommentCount(0L);
            }
        } else {
            log.warn("好评率格式不匹配：{}", rateStr);
            entity.setPositiveRate(null);
            entity.setCommentCount(0L);
        }
    }
    
    /**
     * 处理联系方式信息
     */
    private CollectionDataContactInfoBO buildContactInfo(GmapData gmapData) {
        if (StrUtil.isBlank(gmapData.getPhone())) {
            return new CollectionDataContactInfoBO(); // 返回一个空的ContactInfoBO
        }
        
        // 创建联系方式信息
        CollectionDataContactInfoBO contactInfo = CollectionDataContactInfoBO.builder()
            .contacts(createContactList(gmapData.getPhone(), gmapData.getWhatsAppVerified() != null ? gmapData.getWhatsAppVerified() : false))
            .build();
        
        return contactInfo;
    }
    
    /**
     * 创建联系方式列表
     */
    private List<CollectionDataContactInfoBO.ContactItem> createContactList(String originalPhone, boolean whatsAppVerified) {
        List<CollectionDataContactInfoBO.ContactItem> contacts = new ArrayList<>();
        
        // 清理电话号码格式，移除非数字字符（保留+号）
        String cleanedPhone = PHONE_CLEAN_PATTERN.matcher(originalPhone).replaceAll("");
        
        // 只保存处理后的号码
        if (StrUtil.isNotBlank(cleanedPhone)) {
            CollectionDataContactInfoBO.ContactItem contactItem = CollectionDataContactInfoBO.ContactItem.builder()
                .phone(cleanedPhone)
                .whatsAppVerified(whatsAppVerified) // 使用传递进来的whatsAppVerified值
                .build();
            contacts.add(contactItem);
        }
        
        return contacts;
    }
    
    /**
     * 处理邮箱信息
     */
    private void processEmailInfo(List<String> emails, DataCollectionRawData entity) {
        if (emails.isEmpty()) {
            return;
        }
        entity.setEmails(emails);
    }
} 