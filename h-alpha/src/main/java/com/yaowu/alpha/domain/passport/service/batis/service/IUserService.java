package com.yaowu.alpha.domain.passport.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.passport.User;

/**
 * <p>
 * 用户服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface IUserService extends IService<User> {

    User findByUsername(String username);

    User findByPhone(String crownCode, String phone);

    User findByEmail(String email);
}
