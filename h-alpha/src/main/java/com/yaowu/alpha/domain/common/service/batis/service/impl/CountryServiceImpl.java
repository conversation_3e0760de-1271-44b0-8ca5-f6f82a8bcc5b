package com.yaowu.alpha.domain.common.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.common.service.batis.mapper.CountryMapper;
import com.yaowu.alpha.domain.common.service.batis.service.ICountryService;
import com.yaowu.alpha.model.entity.common.Country;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
public class CountryServiceImpl extends ServiceImpl<CountryMapper, Country> implements ICountryService {

    @Override
    public String getPhoneCodeByCountryName(String countryName) {
        if (countryName == null || countryName.trim().isEmpty()) {
            log.warn("国家名称为空，无法查询区号");
            return null;
        }
        
        String trimmedName = countryName.trim();
        
        // 使用OR查询同时匹配中文名和英文名
        Country country = lambdaQuery()
            .eq(Country::getCountryEnName, trimmedName)
            .or()
            .eq(Country::getCountryZhName, trimmedName)
            .one();
        
        if (country == null) {
            log.warn("未找到国家名称为[{}]的国家信息", countryName);
            return null;
        }
        
        log.info("找到国家[{}]，对应的区号为[{}]", countryName, country.getPhoneCode());
        return country.getPhoneCode();
    }
}
