package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;

import java.util.List;

/**
 * <p>
 * 联系方式验证记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IContactVerificationTaskService extends IService<ContactVerificationTask> {

    /**
     * 根据联系方式和类型查询验证任务
     * @param contacts 联系方式列表
     * @param contactType 联系方式类型
     * @return 验证任务列表
     */
    List<ContactVerificationTask> listByContactsAndType(List<String> contacts, ContactVerificationContactTypeEnum contactType);

    /**
     * 查询待验证的任务
     * @param batchSize 批次大小
     * @return 待验证任务列表
     */
    List<ContactVerificationTask> queryPendingTasks(int batchSize);

}
