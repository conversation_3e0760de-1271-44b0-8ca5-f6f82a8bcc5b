package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.proxy.request.AccountFlowQueryDTO;
import com.yaowu.alpha.model.entity.user.UserBusinessAccountFlow;

/**
 * <AUTHOR>
 * @date 2025/4/14-17:15
 */
public interface IUserBusinessAccountFlowService extends IService<UserBusinessAccountFlow> {

    /**
     * 根据条件分页查询流水
     *
     * @param dto
     * @return
     */
    BasePage<UserBusinessAccountFlow> pageByCondition(AccountFlowQueryDTO dto);

    /**
     * 根据条件查询流水
     *
     * @param dto
     * @return
     */
    UserBusinessAccountFlow getByCondition(AccountFlowQueryDTO dto);
}
