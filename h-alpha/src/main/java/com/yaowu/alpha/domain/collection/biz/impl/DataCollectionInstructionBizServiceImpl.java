package com.yaowu.alpha.domain.collection.biz.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.yaowu.alpha.domain.collection.biz.IDataCollectionInstructionBizService;
import com.yaowu.alpha.domain.common.service.batis.service.ICountryService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyTaskService;
import com.yaowu.alpha.enums.proxy.ProxyTaskStatusEnum;
import com.yaowu.alpha.enums.proxy.TaskTypeEnum;
import com.yaowu.alpha.event.collection.DataCollectionInstructionResultEvent;
import com.yaowu.alpha.model.bo.collection.DataCollectionInstruction;
import com.yaowu.alpha.model.bo.collection.GmapDataCollectionParam;
import com.yaowu.alpha.model.dto.collection.DataCollectionInstructionReportDTO;
import com.yaowu.alpha.model.dto.common.DistributedTaskDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyTask;
import com.yaowu.alpha.utils.DistributedLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 数据采集任务指令业务服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionInstructionBizServiceImpl implements IDataCollectionInstructionBizService {
    
    private final IProxyTaskService proxyTaskService;
    private final DistributedLockUtil distributedLockUtil;
    private final ApplicationEventPublisher eventPublisher;
    private final ICountryService countryService;

    /**
     * 创建GMap数据采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @param gmapParam GMap数据采集参数
     * @return 指令任务ID
     */
    @Override
    public Long createGMapDataCollectionInstruction(Long collectionTaskId, GmapDataCollectionParam gmapParam) {
        // 默认立即执行，7天过期
        return createGMapDataCollectionInstruction(collectionTaskId, gmapParam, 
            LocalDateTime.now(), 7L, TimeUnit.DAYS);
    }

    /**
     * 创建GMap数据采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param gmapParam GMap数据采集参数
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    @Override
    public Long createGMapDataCollectionInstruction(Long collectionTaskId, GmapDataCollectionParam gmapParam,
                                                   LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit) {
        log.info("创建GMap数据采集指令，采集任务ID：{}，国家：{}，城市数量：{}，关键词数量：{}", 
            collectionTaskId, gmapParam.getCountry(), gmapParam.getCities().size(), gmapParam.getKeywords().size());
        
        // 生成指令ID
        Long instructionId = IdWorker.getId();

        // 根据国家英文名查询区号
        String phoneCode = countryService.getPhoneCodeByCountryName(gmapParam.getCountry());
        log.info("查询到国家[{}]的区号为[{}]", gmapParam.getCountry(), phoneCode);

        // 创建指令对象，传入区号参数
        DataCollectionInstruction instruction = DataCollectionInstruction.buildCreateGMapInstruction(
            instructionId, collectionTaskId, gmapParam, phoneCode);
        
        // 创建并保存代理任务，使用GMap采集任务类型
        createInstructionTask(instructionId, instruction, "创建GMap数据采集任务",
            TaskTypeEnum.DATA_COLLECTION_TASK_GMAP, expectExecuteTime, expireValue, expireTimeUnit);
        
        log.info("成功创建GMap数据采集指令，指令ID：{}", instructionId);
        return instructionId;
    }

    /**
     * 创建暂停采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    @Override
    public Long createPauseInstruction(Long collectionTaskId) {
        // 默认立即执行，1天过期
        return createPauseInstruction(collectionTaskId, LocalDateTime.now(), 1L, TimeUnit.DAYS);
    }

    /**
     * 创建暂停采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    @Override
    public Long createPauseInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit) {
        log.info("创建暂停采集任务指令，采集任务ID：{}", collectionTaskId);
        
        // 生成指令ID
        Long instructionId = IdWorker.getId();
        
        // 创建指令对象
        DataCollectionInstruction instruction = DataCollectionInstruction.buildPauseInstruction(
            instructionId, collectionTaskId);
        
        // 创建并保存代理任务，使用指令类型
        createInstructionTask(instructionId, instruction, "暂停采集任务",
            TaskTypeEnum.DATA_COLLECTION_TASK_INSTRUCTION, expectExecuteTime, expireValue, expireTimeUnit);
        
        log.info("成功创建暂停采集任务指令，指令ID：{}", instructionId);
        return instructionId;
    }

    /**
     * 创建恢复采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    @Override
    public Long createResumeInstruction(Long collectionTaskId) {
        // 默认立即执行，1天过期
        return createResumeInstruction(collectionTaskId, LocalDateTime.now(), 1L, TimeUnit.DAYS);
    }

    /**
     * 创建恢复采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    @Override
    public Long createResumeInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit) {
        log.info("创建恢复采集任务指令，采集任务ID：{}", collectionTaskId);
        
        // 生成指令ID
        Long instructionId = IdWorker.getId();
        
        // 创建指令对象
        DataCollectionInstruction instruction = DataCollectionInstruction.buildResumeInstruction(
            instructionId, collectionTaskId);
        
        // 创建并保存代理任务，使用指令类型
        createInstructionTask(instructionId, instruction, "恢复采集任务",
            TaskTypeEnum.DATA_COLLECTION_TASK_INSTRUCTION, expectExecuteTime, expireValue, expireTimeUnit);
        
        log.info("成功创建恢复采集任务指令，指令ID：{}", instructionId);
        return instructionId;
    }

    /**
     * 创建终止采集任务指令
     *
     * @param collectionTaskId 采集任务ID
     * @return 指令任务ID
     */
    @Override
    public Long createTerminateInstruction(Long collectionTaskId) {
        // 默认立即执行，1天过期
        return createTerminateInstruction(collectionTaskId, LocalDateTime.now(), 1L, TimeUnit.DAYS);
    }

    /**
     * 创建终止采集任务指令（带时间参数）
     *
     * @param collectionTaskId 采集任务ID
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 指令任务ID
     */
    @Override
    public Long createTerminateInstruction(Long collectionTaskId, LocalDateTime expectExecuteTime, Long expireValue, TimeUnit expireTimeUnit) {
        log.info("创建终止采集任务指令，采集任务ID：{}", collectionTaskId);
        
        // 生成指令ID
        Long instructionId = IdWorker.getId();
        
        // 创建指令对象
        DataCollectionInstruction instruction = DataCollectionInstruction.buildTerminateInstruction(
            instructionId, collectionTaskId);
        
        // 创建并保存代理任务，使用指令类型
        createInstructionTask(instructionId, instruction, "终止采集任务",
            TaskTypeEnum.DATA_COLLECTION_TASK_INSTRUCTION, expectExecuteTime, expireValue, expireTimeUnit);
        
        log.info("成功创建终止采集任务指令，指令ID：{}", instructionId);
        return instructionId;
    }

    /**
     * 获取并处理一个待执行的GMap数据采集任务（需要分布式锁）
     * 注意：此方法会获取分布式锁，确保多机部署时不重复执行
     *
     * @return 数据采集指令，如果没有待执行任务则返回null
     */
    @Override
    public DataCollectionInstruction pullOnePendingGMapTask() {
        String lockKey = "data_collection:gmap:pending_task";
        
        // 使用分布式锁确保多机部署时不重复执行
        Integer retryTimes = 5;
        Long retryStepMills = 200L;
        return distributedLockUtil.execute("",
                DistributedTaskDTO.builder().taskId(lockKey).taskName("获取并处理待执行的GMap数据采集任务").build(),
                retryTimes, retryStepMills, (param) -> {
            log.info("获取并处理待执行的GMap数据采集任务");
            
            // 调用Service层方法查询待执行的GMap采集任务
            ProxyTask proxyTask = proxyTaskService.selectOnePendingTaskByType(TaskTypeEnum.DATA_COLLECTION_TASK_GMAP);
            if (Objects.isNull(proxyTask)) {
                log.info("没有待执行的GMap数据采集任务");
                return null;
            }
            
            // 更新任务状态为处理中
            proxyTask.setTaskStatus(ProxyTaskStatusEnum.EXECUTING.getValue());
            proxyTaskService.updateById(proxyTask);
            
            // 解析并返回指令对象
            DataCollectionInstruction instruction = JSONUtil.toBean(proxyTask.getTaskData(), DataCollectionInstruction.class);
            log.info("成功获取GMap数据采集任务，任务ID：{}，指令ID：{}", proxyTask.getId(), instruction.getInstructionId());
            
            return instruction;
        });
    }

    /**
     * 获取并处理一个待执行的采集任务控制指令（不需要分布式锁）
     * 用于处理暂停、恢复、终止等控制指令
     *
     * @return 数据采集指令，如果没有待执行任务则返回null
     */
    @Override
    public DataCollectionInstruction pullPendingControlInstruction(Long collectionTaskId) {
        log.info("获取并处理待执行的采集任务控制指令");
        
        // 调用Service层方法查询待执行的控制指令任务
        ProxyTask proxyTask = proxyTaskService.selectOnePendingTaskByType(TaskTypeEnum.DATA_COLLECTION_TASK_INSTRUCTION);
        if (Objects.isNull(proxyTask)) {
            log.info("没有待执行的采集任务控制指令");
            return null;
        }
        // 解析并返回指令对象
        DataCollectionInstruction instruction = JSONUtil.toBean(proxyTask.getTaskData(), DataCollectionInstruction.class);
        log.info("成功获取采集任务控制指令，任务ID：{}，指令ID：{}", proxyTask.getId(), instruction.getInstructionId());
        
        return instruction;
    }

    /**
     * 处理客户端上报的指令执行结果
     * 处理指令执行结果，更新任务状态并发布事件
     *
     * @param reportDTO 指令执行结果上报DTO
     */
    @Override
    public void handleInstructionResult(DataCollectionInstructionReportDTO reportDTO) {
        log.info("处理指令执行结果，指令ID：{}，执行结果：{}", reportDTO.getInstructionId(), reportDTO.getSuccess());

        try {
            ProxyTask proxyTask = findTaskByInstructionId(reportDTO.getInstructionId());
            if (proxyTask == null) {
                log.warn("未找到指令对应的任务，指令ID：{}", reportDTO.getInstructionId());
                return;
            }
            // 查找并更新任务状态
            updateTaskStatus(reportDTO, proxyTask);

            // 发布执行结果事件
            publishResultEvent(reportDTO, proxyTask);

        } catch (Exception e) {
            log.error("处理指令执行结果时发生异常，指令ID：{}", reportDTO.getInstructionId(), e);
            throw new RuntimeException("处理指令执行结果失败", e);
        }
    }

    /**
     * 创建指令任务
     *
     * @param instruction 指令对象
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param expectExecuteTime 预计执行时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 任务ID
     */
    private void createInstructionTask(Long instructionId,
                                       DataCollectionInstruction instruction,
                                       String taskName,
                                       TaskTypeEnum taskType,
                                       LocalDateTime expectExecuteTime,
                                       Long expireValue,
                                       TimeUnit expireTimeUnit) {
        // 计算过期时间
        LocalDateTime expireTime = calculateExpireTime(expectExecuteTime, expireValue, expireTimeUnit);
        
        // 创建代理任务
        ProxyTask proxyTask = new ProxyTask();
        proxyTask.setId(instructionId);
        proxyTask.setTaskName(taskName);
        proxyTask.setTaskType(taskType);
        proxyTask.setTaskData(JSONUtil.toJsonStr(instruction));
        proxyTask.setTaskStatus(ProxyTaskStatusEnum.WAIT.getValue());
        proxyTask.setExpectExecuteTime(expectExecuteTime);
        proxyTask.setExpireTime(expireTime);
        
        // 保存任务
        proxyTaskService.save(proxyTask);
    }

    /**
     * 计算过期时间
     *
     * @param baseTime 基础时间
     * @param expireValue 过期时间数值
     * @param expireTimeUnit 过期时间单位
     * @return 过期时间
     */
    private LocalDateTime calculateExpireTime(LocalDateTime baseTime, Long expireValue, TimeUnit expireTimeUnit) {
        if (baseTime == null) {
            baseTime = LocalDateTime.now();
        }
        
        return switch (expireTimeUnit) {
            case SECONDS -> baseTime.plusSeconds(expireValue);
            case MINUTES -> baseTime.plusMinutes(expireValue);
            case HOURS -> baseTime.plusHours(expireValue);
            case DAYS -> baseTime.plusDays(expireValue);
            default -> baseTime.plusDays(7); // 默认7天
        };
    }

    /**
     * 更新任务状态
     *
     * @param reportDTO 指令执行结果上报DTO
     */
    private void updateTaskStatus(DataCollectionInstructionReportDTO reportDTO, ProxyTask proxyTask) {
        // 更新任务状态和错误信息
        if (Boolean.TRUE.equals(reportDTO.getSuccess())) {
            proxyTask.setTaskStatus(ProxyTaskStatusEnum.SUCCESS.getValue());
            log.info("指令执行成功，任务ID：{}，指令ID：{}", proxyTask.getId(), reportDTO.getInstructionId());
        } else {
            proxyTask.setTaskStatus(ProxyTaskStatusEnum.FAILURE.getValue());
            proxyTask.setErrorMsg(reportDTO.getErrorReason());
            log.warn("指令执行失败，任务ID：{}，指令ID：{}，失败原因：{}", 
                proxyTask.getId(), reportDTO.getInstructionId(), reportDTO.getErrorReason());
        }
        
        // 保存任务更新
        proxyTaskService.updateById(proxyTask);
    }

    /**
     * 发布执行结果事件
     *
     * @param reportDTO 指令执行结果上报DTO
     */
    private void publishResultEvent(DataCollectionInstructionReportDTO reportDTO, ProxyTask proxyTask) {
        // 解析指令数据获取采集任务ID
        DataCollectionInstruction instruction = JSONUtil.toBean(proxyTask.getTaskData(), DataCollectionInstruction.class);
        
        // 发布事件
        DataCollectionInstructionResultEvent event = DataCollectionInstructionResultEvent.builder()
                .instructionId(reportDTO.getInstructionId())
                .instructionData(JSONUtil.toBean(JSONUtil.toJsonStr(instruction.getInstructionData()), DataCollectionInstruction.BaseInstructionData.class))
                .taskType(proxyTask.getTaskType())
                .success(reportDTO.getSuccess())
                .errorReason(reportDTO.getErrorReason())
                .build();
        
        eventPublisher.publishEvent(event);
        log.info("已发布指令执行结果事件，事件内容：{}", JSONUtil.toJsonStr(event));
    }

    /**
     * 根据指令ID查找对应的任务
     *
     * @param instructionId 指令ID
     * @return 代理任务，如果未找到则返回null
     */
    private ProxyTask findTaskByInstructionId(Long instructionId) {
        return proxyTaskService.getById(instructionId);
    }
} 