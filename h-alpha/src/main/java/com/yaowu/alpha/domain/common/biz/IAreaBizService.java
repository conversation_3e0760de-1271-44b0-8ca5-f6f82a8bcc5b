package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.model.dto.common.CityQueryDTO;
import com.yaowu.alpha.model.dto.common.ParentAreaQueryDTO;
import com.yaowu.alpha.model.vo.common.AreaVO;
import com.yaowu.alpha.model.vo.common.CityVO;
import com.yaowu.alpha.model.vo.common.CountryVO;

import java.util.List;

/**
 * 地区业务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IAreaBizService {

    /**
     * 查询国家列表
     *
     * @return 国家信息列表
     */
    List<CountryVO> listCountries();

    /**
     * 根据国家查询城市列表
     *
     * @param dto 城市查询参数
     * @return 城市信息列表
     */
    List<CityVO> listCitiesByCountry(CityQueryDTO dto);

    /**
     * 根据父区域查询子区域列表（通用方法）
     *
     * @param dto 父区域查询参数
     * @return 子区域信息列表
     */
    List<AreaVO> listChildrenByParent(ParentAreaQueryDTO dto);
}
