package com.yaowu.alpha.domain.common.remote;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.heraapi.model.dto.clue.ClueTicketInfoDTO;
import com.yaowu.heraapi.model.dto.content.*;
import com.yaowu.heraapi.model.dto.mtl.RemoteCategoryConditionDTO;
import com.yaowu.heraapi.model.dto.mtl.agent.RemoteAgentSubmitMtlLeadInfoDTO;
import com.yaowu.heraapi.model.dto.omk.RemoteOmkGeneralCustomerAddDTO;
import com.yaowu.heraapi.model.dto.schedule.PageQueryClueTicketDTO;
import com.yaowu.heraapi.model.vo.clue.ClueTicketVO;
import com.yaowu.heraapi.model.vo.content.RemoteContentTagVO;
import com.yaowu.heraapi.model.vo.content.RemoteFloorCardVO;
import com.yaowu.heraapi.model.vo.content.RemoteSimpleContentDetailVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteLeadsSubmitVO;
import com.yaowu.heraapi.model.vo.mtl.RemoteWebCategoryDetailVO;
import com.yaowu.heraapi.model.vo.omk.RemoteClueTicketVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/2 21:09
 */
public interface IHeraRemoteBizService {

    Boolean addGeneralCustomerInfo(RemoteOmkGeneralCustomerAddDTO remoteDTO);

    RemoteClueTicketVO pushClueTicket(ClueTicketInfoDTO remoteDTO);

    BasePage<ClueTicketVO> pageQueryClueTicket(PageQueryClueTicketDTO queryClueTicketDTO);

    RemoteLeadsSubmitVO submitMtlLeadsInfo(RemoteAgentSubmitMtlLeadInfoDTO remoteDTO);

    BasePage<RemoteFloorCardVO> floorCardPage(RemoteFloorCardQueryDTO dto);

    Long addFloorCard(RemoteFloorCardAddDTO dto);

    boolean deleteFloorCard(Long id);

    boolean modifyFloorCard(RemoteFloorCardModifyDTO dto);

    boolean sortFloorCard(RemoteFloorCardSortDTO dto);

    List<RemoteFloorCardVO> listFloorCard(RemoteFloorCardQueryDTO dto);

    RemoteFloorCardVO getFloorCard(Long id);

    Map<Long, RemoteSimpleContentDetailVO> batchGetSimpleContentDetail(List<Long> contentIds);

    /**
     * 获取内容标签
     *
     * @param dto
     * @return
     */
    List<RemoteContentTagVO> contentTags(RemoteContentTagsQueryDTO dto);

    boolean viewFloorCard(RemoteFloorCardViewedDTO dto);

    /**
     * 获取MTL设备品类
     */
    List<RemoteWebCategoryDetailVO> getCategoryByCondition(RemoteCategoryConditionDTO dto);

}
