package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.domain.proxy.protocol.processor.ReportRoomMemberInfoProcessor.UpdateGroupMemberInfo;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupInfo;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupMember;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 群成员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyGroupMemberService extends IService<ProxyGroupMember> {

    /**
     * 保存或更新群成员
     *
     * @param proxyGroupInfo 群id
     * @param memberList   群成员
     */
    void saveOrUpdateMembers(ProxyGroupInfo proxyGroupInfo, List<String> memberList);

    /**
     * 查询群成员
     *
     * @param proxyGroupId 群id
     * @param memberList   群成员
     */
    Boolean isMember(Long proxyGroupId, List<String> memberList);

    List<ProxyGroupMember> listByProxyGroupId(Long proxyGroupId);

    List<Long> listAllGroupIdByMember(String memberWxId);

    /**
     * 保存或更新群成员
     *
     * @param proxyGroupInfo 群id
     * @param memberList   群成员
     */
    void saveOrUpdateMemberDetails(ProxyGroupInfo proxyGroupInfo, List<UpdateGroupMemberInfo> memberList);

    Map<String, ProxyGroupMember> getByMemberProxyIds(List<String> memberProxyIds);

}
