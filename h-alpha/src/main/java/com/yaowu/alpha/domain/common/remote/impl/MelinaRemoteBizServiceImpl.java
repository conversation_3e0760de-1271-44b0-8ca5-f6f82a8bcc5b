package com.yaowu.alpha.domain.common.remote.impl;

import com.freedom.feign.utils.FeignInvokeUtils;
import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.domain.common.remote.IMelinaRemoteBizService;
import com.yaowu.melinaapi.feign.melina.IRemoteMelinaMerchantInfoServiceFeign;
import com.yaowu.melinaapi.model.dto.wms.RemoteIdObj;
import com.yaowu.melinaapi.model.vo.merchant.RemoteMerchantInfoSimpleVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/16 14:37
 */
@RequiredArgsConstructor
@Service
public class MelinaRemoteBizServiceImpl implements IMelinaRemoteBizService {

    private final IRemoteMelinaMerchantInfoServiceFeign remoteMelinaMerchantInfoServiceFeign;

    @Override
    public RemoteMerchantInfoSimpleVO getMerchantById(Long merchantId) {
        BaseResult<RemoteMerchantInfoSimpleVO> result = remoteMelinaMerchantInfoServiceFeign.getMerchantInfoById(new RemoteIdObj(merchantId));
        return FeignInvokeUtils.convert(result, RemoteMerchantInfoSimpleVO.class);
    }

}
