package com.yaowu.alpha.domain.common.biz;

import com.yaowu.alpha.enums.common.SystemOperationLogBizCodeEnum;
import com.yaowu.alpha.model.bo.common.OperationContent;

/**
 * <p>
 * 操作日志业务服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface IOperationLogBizService {

    /**
     * 记录操作日志
     *
     * @param operateBizCode 操作业务编码
     * @param operateBizDesc 操作业务描述
     * @param operateBizId 操作业务id
     * @param operateContent 操作内容
     */
    void recordOperationLog(String operateBizCode, String operateBizDesc, String operateBizId, String operateContent);

    /**
     * 记录操作日志（使用枚举和实体）
     *
     * @param operationBizCode 操作业务编码枚举
     * @param operateBizId 操作业务id
     * @param operationContent 操作内容实体
     */
    void recordOperationLog(SystemOperationLogBizCodeEnum operationBizCode, String operateBizId, OperationContent operationContent);

    /**
     * 记录代理账号状态变更日志
     *
     * @param accountId 账号ID
     * @param beforeStatus 变更前状态
     * @param afterStatus 变更后状态
     */
    void recordProxyAccountStatusLog(Long accountId, Integer beforeStatus, Integer afterStatus);

    /**
     * 记录好友状态变更日志
     *
     * @param friendId 好友ID
     * @param beforeStatus 变更前状态
     * @param afterStatus 变更后状态
     */
    void recordFriendStatusLog(Long friendId, Integer beforeStatus, Integer afterStatus);

    /**
     * 记录代理账号删除日志
     *
     * @param accountId 账号ID
     * @param statusBeforeDelete 删除前状态
     * @param deleteReason 删除原因
     */
    void recordProxyAccountDeleteLog(Long accountId, Integer statusBeforeDelete, String deleteReason);

} 