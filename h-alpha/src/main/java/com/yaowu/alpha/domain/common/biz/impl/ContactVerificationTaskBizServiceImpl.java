package com.yaowu.alpha.domain.common.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.common.biz.IContactVerificationTaskBizService;
import com.yaowu.alpha.domain.common.processor.IContactVerificationProcessor;
import com.yaowu.alpha.domain.common.service.batis.service.IContactVerificationTaskService;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.enums.common.ContactVerificationStatusEnum;
import com.yaowu.alpha.model.dto.common.ContactVerificationCreateDTO;
import com.yaowu.alpha.model.dto.common.ContactVerificationQueryDTO;
import com.yaowu.alpha.model.dto.common.DistributedTaskDTO;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;
import com.yaowu.alpha.model.event.common.ContactVerificationTaskCreatedEvent;
import com.yaowu.alpha.model.vo.common.ContactVerificationCreateResultVO;
import com.yaowu.alpha.model.vo.common.ContactVerificationStatusVO;
import com.yaowu.alpha.utils.DistributedLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 联系方式验证任务业务服务类
 * 负责处理联系方式验证的核心业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-24 20:30:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContactVerificationTaskBizServiceImpl implements IContactVerificationTaskBizService {

    private final IContactVerificationTaskService contactVerificationTaskService;
    private final List<IContactVerificationProcessor> processors;
    private final DistributedLockUtil distributedLockUtil;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 批量创建验证任务
     * 如果任务已存在且状态为验证失败，则更新状态为待验证
     * @param dto 创建请求
     * @return 创建结果统计
     */
    @Override
    public ContactVerificationCreateResultVO createVerificationTasks(ContactVerificationCreateDTO dto) {
        // 构建任务列表
        List<ContactVerificationTask> allTasks = buildTaskList(dto);

        // 查询已存在的任务
        List<ContactVerificationTask> existingTasks = findExistingTasks(allTasks);

        // 处理已存在任务，分离新任务和失败任务
        TaskProcessResult processResult = processExistingTasks(allTasks, existingTasks);

        // 批量保存新任务
        int newTaskCount = saveNewTasks(processResult.getNewTasks());

        // 更新失败任务状态
        int updatedTaskCount = updateFailedTasksStatus(processResult.getFailedTasks());

        // 构建并返回结果
        ContactVerificationCreateResultVO result = buildCreateResult(allTasks.size(), newTaskCount, updatedTaskCount, 
                               existingTasks.size() - processResult.getFailedTasks().size());
        
        // 发布任务创建事件，触发异步验证处理
        publishTaskCreatedEvent(newTaskCount, updatedTaskCount, allTasks.size(), 
                               processResult.getNewTasks().isEmpty() ? null : processResult.getNewTasks().get(0).getBatchId());
        
        return result;
    }

    /**
     * 查询验证状态
     * @param dto 查询条件
     * @return 验证状态列表
     */
    @Override
    public List<ContactVerificationStatusVO> queryVerificationStatus(ContactVerificationQueryDTO dto) {
        // 参数校验和类型转换
        ContactVerificationContactTypeEnum contactType = validateAndParseContactType(dto);

        // 查询验证任务
        List<ContactVerificationTask> tasks = queryVerificationTasks(dto.getContacts(), contactType);

        // 构建联系方式到任务的映射
        Map<String, ContactVerificationTask> taskMap = buildContactTaskMap(tasks);

        // 为每个联系方式构建状态VO
        return buildStatusVOList(dto.getContacts(), contactType, taskMap);
    }

    /**
     * 处理验证任务
     * 从数据库获取待验证的任务并执行验证
     * 使用分布式锁确保同一时间只有一个实例执行验证任务处理
     *
     * @param batchSize 批次大小，默认50
     * @return 处理的任务数量
     */
    @Override
    public int processVerificationTasks(int batchSize) {
        log.info("开始处理验证任务，批次大小: {}", batchSize);
        
        // 构建分布式任务
        DistributedTaskDTO taskDTO = DistributedTaskDTO.builder()
                .taskId("contact:verification:process")
                .taskName("联系方式验证任务处理")
                .build();
        
        try {
            return distributedLockUtil.execute(batchSize, taskDTO, this::executeVerificationTasks);
        } catch (Exception e) {
            log.warn("验证任务处理失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    
    /**
     * 执行验证任务的核心逻辑
     */
    private int executeVerificationTasks(int batchSize) {
        // 查询待验证的任务
        List<ContactVerificationTask> pendingTasks = queryPendingTasks(batchSize);
        if (CollUtil.isEmpty(pendingTasks)) {
            log.info("没有待验证的任务");
            return 0;
        }
        log.info("找到 {} 个待验证任务", pendingTasks.size());

        // 逐个处理任务
        int processedCount = 0;
        for (ContactVerificationTask task : pendingTasks) {
            try {
                if (processVerificationTask(task)) {
                    processedCount++;
                }
            } catch (Exception e) {
                log.error("处理验证任务异常: taskId={}, contact={}, error={}",
                        task.getId(), task.getContact(), e.getMessage(), e);
            }
        }
        log.info("验证任务处理完成，总数: {}, 成功: {}", pendingTasks.size(), processedCount);
        return processedCount;
    }

    /**
     * 参数校验和类型转换
     */
    private ContactVerificationContactTypeEnum validateAndParseContactType(ContactVerificationQueryDTO dto) {
        if (dto == null || CollUtil.isEmpty(dto.getContacts())) {
            throw new BusinessException("查询参数不能为空");
        }

        if (StrUtil.isBlank(dto.getContactType())) {
            throw new BusinessException("联系方式类型不能为空");
        }

        try {
            return ContactVerificationContactTypeEnum.valueOf(dto.getContactType());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("不支持的联系方式类型: " + dto.getContactType());
        }
    }

    /**
     * 查询验证任务
     */
    private List<ContactVerificationTask> queryVerificationTasks(List<String> contacts,
                                                                ContactVerificationContactTypeEnum contactType) {
        return contactVerificationTaskService.listByContactsAndType(contacts, contactType);
    }

    /**
     * 构建联系方式到任务的映射
     */
    private Map<String, ContactVerificationTask> buildContactTaskMap(List<ContactVerificationTask> tasks) {
        return tasks.stream()
                .collect(Collectors.toMap(
                    ContactVerificationTask::getContact,
                    Function.identity(),
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
    }

    /**
     * 构建状态VO列表
     */
    private List<ContactVerificationStatusVO> buildStatusVOList(List<String> contacts,
                                                               ContactVerificationContactTypeEnum contactType,
                                                               Map<String, ContactVerificationTask> taskMap) {
        return contacts.stream()
                .map(contact -> buildStatusVOForContact(contact, contactType, taskMap.get(contact)))
                .collect(Collectors.toList());
    }

    /**
     * 构建任务列表
     */
    private List<ContactVerificationTask> buildTaskList(ContactVerificationCreateDTO dto) {
        Long batchId = IdWorker.getId();
        return dto.getContacts().stream()
                .flatMap(contactInfo -> contactInfo.getContact().stream()
                        .map(contact -> buildVerificationTask(
                                batchId,
                                contact,
                                ContactVerificationContactTypeEnum.valueOf(contactInfo.getContactType())
                        )))
                .collect(Collectors.toList());
    }

    /**
     * 查询已存在的任务
     */
    private List<ContactVerificationTask> findExistingTasks(List<ContactVerificationTask> allTasks) {
        return queryExistingTasksByType(allTasks);
    }

    /**
     * 处理已存在的任务，分离新任务和失败任务
     */
    private TaskProcessResult processExistingTasks(List<ContactVerificationTask> allTasks,
                                                   List<ContactVerificationTask> existingTasks) {
        // 构建已存在任务的映射
        Map<String, ContactVerificationTask> existingTaskMap = existingTasks.stream()
                .collect(Collectors.toMap(
                    task -> buildContactKey(task.getContact(), task.getContactType()),
                    Function.identity()
                ));

        // 分离新任务和失败任务
        List<ContactVerificationTask> newTasks = allTasks.stream()
                .filter(task -> !existingTaskMap.containsKey(
                    buildContactKey(task.getContact(), task.getContactType())))
                .collect(Collectors.toList());

        List<ContactVerificationTask> failedTasks = existingTasks.stream()
                .filter(task -> ContactVerificationStatusEnum.FAILED.equals(task.getValidationStatus()))
                .collect(Collectors.toList());

        return new TaskProcessResult(newTasks, failedTasks);
    }

    /**
     * 批量保存新任务
     */
    private int saveNewTasks(List<ContactVerificationTask> newTasks) {
        if (CollUtil.isEmpty(newTasks)) {
            return 0;
        }

        boolean saveResult = contactVerificationTaskService.saveBatch(newTasks);
        return saveResult ? newTasks.size() : 0;
    }

    /**
     * 构建验证任务
     */
    private ContactVerificationTask buildVerificationTask(Long batchId, String contact, ContactVerificationContactTypeEnum contactType) {
        ContactVerificationTask task = new ContactVerificationTask();
        task.setBatchId(batchId);
        task.setContact(contact);
        task.setContactType(contactType);
        task.setValidationStatus(ContactVerificationStatusEnum.PENDING);
        return task;
    }
    
    /**
     * 更新失败任务状态为待验证
     */
    private int updateFailedTasksStatus(List<ContactVerificationTask> failedTasks) {
        if (CollUtil.isEmpty(failedTasks)) {
            return 0;
        }

        failedTasks.forEach(task -> task.setValidationStatus(ContactVerificationStatusEnum.PENDING));
        boolean updateResult = contactVerificationTaskService.updateBatchById(failedTasks);
        return updateResult ? failedTasks.size() : 0;
    }

    /**
     * 构建创建结果统计
     */
    private ContactVerificationCreateResultVO buildCreateResult(int totalCount, int newTaskCount, 
                                                               int updatedTaskCount, int unchangedCount) {
        return new ContactVerificationCreateResultVO()
                .setTotalCount(totalCount)
                .setSuccessCount(newTaskCount + updatedTaskCount)
                .setExistCount(unchangedCount)
                .setFailCount(0);
    }
    
    /**
     * 发布任务创建事件
     * 
     * @param newTaskCount 新创建任务数量
     * @param updatedTaskCount 更新任务数量
     * @param totalContactCount 总联系方式数量
     * @param batchId 批次ID
     */
    private void publishTaskCreatedEvent(int newTaskCount, int updatedTaskCount, 
                                       int totalContactCount, Long batchId) {
        try {
            ContactVerificationTaskCreatedEvent event = new ContactVerificationTaskCreatedEvent(
                    this, newTaskCount, updatedTaskCount, totalContactCount, batchId);
            eventPublisher.publishEvent(event);
            log.info("发布联系方式验证任务创建事件: 新任务={}, 更新任务={}, 总数={}, 批次ID={}", 
                    newTaskCount, updatedTaskCount, totalContactCount, batchId);
        } catch (Exception e) {
            log.error("发布联系方式验证任务创建事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 任务处理结果内部类
     */
    private static class TaskProcessResult {
        private final List<ContactVerificationTask> newTasks;
        private final List<ContactVerificationTask> failedTasks;

        public TaskProcessResult(List<ContactVerificationTask> newTasks, 
                               List<ContactVerificationTask> failedTasks) {
            this.newTasks = newTasks;
            this.failedTasks = failedTasks;
        }

        public List<ContactVerificationTask> getNewTasks() {
            return newTasks;
        }

        public List<ContactVerificationTask> getFailedTasks() {
            return failedTasks;
        }
    }

    /**
     * 按联系方式类型分组查询已存在的记录
     * 优化查询效率，减少数据库查询次数
     */
    private List<ContactVerificationTask> queryExistingTasksByType(List<ContactVerificationTask> allTasks) {
        // 按联系方式类型分组
        return allTasks.stream()
                .collect(Collectors.groupingBy(ContactVerificationTask::getContactType))
                .entrySet().stream()
                .flatMap(entry -> {
                    ContactVerificationContactTypeEnum contactType = entry.getKey();
                    List<String> contacts = entry.getValue().stream()
                            .map(ContactVerificationTask::getContact)
                            .distinct()
                            .collect(Collectors.toList());

                    // 批量查询该类型下的所有联系方式
                    return contactVerificationTaskService.listByContactsAndType(contacts, contactType).stream();
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建联系方式唯一标识key（联系方式 + 类型）
     */
    private String buildContactKey(String contact, ContactVerificationContactTypeEnum contactType) {
        return contact + "_" + contactType.name();
    }
    
    /**
     * 为指定联系方式构建状态VO
     */
    private ContactVerificationStatusVO buildStatusVOForContact(String contact,
                                                                ContactVerificationContactTypeEnum contactType,
                                                                ContactVerificationTask task) {
        return new ContactVerificationStatusVO()
                .setContact(contact)
                .setContactType(contactType.name())
                .setValidationStatus(determineValidationStatus(task));
    }

    /**
     * 根据验证任务确定验证状态
     * 1. 当验证任务不存在，则验证状态为null
     * 2. 当验证任务状态存在，则返回对应的状态值
     * 
     * @param task 验证任务
     * @return 验证状态枚举的value值
     */
    private Integer determineValidationStatus(ContactVerificationTask task) {
        if (task == null) {
            return null;
        }
        
        ContactVerificationStatusEnum status = task.getValidationStatus();
        if (status == null) {
            return null;
        }

        return status.getValue();
    }
    
    /**
     * 查询待验证的任务
     */
    private List<ContactVerificationTask> queryPendingTasks(int batchSize) {
        return contactVerificationTaskService.queryPendingTasks(batchSize);
    }
    
    /**
     * 处理单个验证任务
     */
    private boolean processVerificationTask(ContactVerificationTask task) {
        Optional<IContactVerificationProcessor> processorOption = findSupportedProcessor(task.getContactType());

        return processorOption
                .map(processor -> executeVerificationWithProcessor(task, processor))
                .orElseGet(() -> handleNoProcessorFound(task));

    }
    
    /**
     * 查找支持指定联系方式类型的处理器
     */
    private Optional<IContactVerificationProcessor> findSupportedProcessor(ContactVerificationContactTypeEnum contactType) {
        return processors.stream()
                .filter(processor -> processor.supports(contactType))
                .findFirst();
    }
    
    /**
     * 处理未找到处理器的情况
     */
    private boolean handleNoProcessorFound(ContactVerificationTask task) {
        log.warn("未找到支持的验证处理器: contact={}, type={}", 
            task.getContact(), task.getContactType());

        task.setValidationStatus(ContactVerificationStatusEnum.FAILED)
                .setErrorMessage("未找到支持的验证处理器");

        contactVerificationTaskService.updateById(task);
        return false;
    }
    
    /**
     * 使用处理器执行验证
     */
    private boolean executeVerificationWithProcessor(ContactVerificationTask task, 
                                                    IContactVerificationProcessor processor) {
        processor.process(task);
        return true;
    }
}