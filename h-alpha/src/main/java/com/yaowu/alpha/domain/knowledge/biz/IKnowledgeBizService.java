package com.yaowu.alpha.domain.knowledge.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.knowledge.*;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeDetailVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgeFilePageVO;
import com.yaowu.alpha.model.vo.knowledge.KnowledgePageVO;

import java.io.IOException;
import java.util.List;

/**
 * 知识库业务服务
 *
 * <AUTHOR>
 */
public interface IKnowledgeBizService {

    /**
     * 创建知识库
     *
     * @return
     */
    Long createKnowledge(KnowledgeAddDTO dto);

    /**
     * 知识库分页
     *
     * @return
     */
    BasePage<KnowledgePageVO> knowledgePage(KnowledgePageDTO dto);

    /**
     * 知识库详情
     *
     * @return
     */
    KnowledgeDetailVO getKnowledgeDetail(Long id);

    /**
     * 更新知识库
     *
     * @return
     */
    Boolean updateKnowledge(KnowledgeEditDTO dto);

    /**
     * 删除知识库
     *
     * @return
     */
    Boolean deleteKnowledge(Long id);

    /**
     * 知识库文件分页
     *
     * @return
     */
    BasePage<KnowledgeFilePageVO> filePage(KnowledgeFilePageDTO dto);

    /**
     * 创建知识库文件
     *
     * @return
     */
    List<Long> createKnowledgeFile(KnowledgeFileAddDTO dto) throws IOException;


    /**
     * 编辑知识库文件
     *
     * @return
     */
    Boolean editKnowledgeFile(KnowledgeFileEditDTO dto);

    /**
     * 删除知识库文件
     *
     * @return
     */
    Boolean deleteKnowledgeFile(Long id);
}
