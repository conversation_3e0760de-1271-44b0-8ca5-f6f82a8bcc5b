package com.yaowu.alpha.domain.collection.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskPageDTO;
import com.yaowu.alpha.model.dto.collection.ListDataCollectionTaskDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionTask;

import java.util.List;

/**
 * <p>
 * 数据采集任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IDataCollectionTaskService extends IService<DataCollectionTask> {
    
    /**
     * 分页查询数据采集任务
     * 
     * @param dto 查询条件
     * @return 分页结果
     */
    Page<DataCollectionTask> pageByCondition(DataCollectionTaskPageDTO dto);

    /**
     * 根据条件查询数据采集任务列表
     * @param dto
     * @return
     */
    List<DataCollectionTask> listByCondition(ListDataCollectionTaskDTO dto);
}
