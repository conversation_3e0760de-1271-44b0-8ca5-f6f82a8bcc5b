package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyGreetingTask;

import java.util.List;
import java.util.Set;

/**
 * 代理问候任务服务接口
 */
public interface IProxyGreetingTaskService extends IService<ProxyGreetingTask> {
    /**
     * 获取待发送的发送人列表
     *
     * @param thirdTypes 三方类型
     * @return 发送人列表
     */
    List<String> listPendingSenders(Set<Integer> thirdTypes);

    /**
     * 获取发送人的待发送任务
     *
     * @param sender    发送人
     * @param thirdType 三方类型
     * @param limit     限制数量
     * @return 任务列表
     */
    List<ProxyGreetingTask> listPendingTasksBySender(String sender, Integer limit);

    /**
     * 获取发送人的待发送任务（支持多种三方类型）
     *
     * @param sender     发送人
     * @param thirdTypes 三方类型集合
     * @param limit      限制数量
     * @return 任务列表
     */
    List<ProxyGreetingTask> listPendingTasksBySenderAndTypes(String sender, Set<Integer> thirdTypes, Integer limit);

    /**
     * 获取待生成内容的任务列表
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    List<ProxyGreetingTask> listPendingGenerateTasks(Long limit);

    /**
     * 根据营销邮件任务ID获取对应的打招呼任务（proxy_task_id 关联）
     * @param marketingTaskId 营销任务ID
     * @return 匹配到的打招呼任务，可能为null
     */
    ProxyGreetingTask getByMarketingTaskId(Long marketingTaskId);
} 