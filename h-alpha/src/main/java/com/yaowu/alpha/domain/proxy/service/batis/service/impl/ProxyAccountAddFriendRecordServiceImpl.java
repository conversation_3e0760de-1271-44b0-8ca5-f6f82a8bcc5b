package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyAccountAddFriendRecordMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountAddFriendRecordService;
import com.yaowu.alpha.model.dto.proxy.control.AddFriendRecordRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountAddFriendRecord;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 托管账户添加好友列表记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class ProxyAccountAddFriendRecordServiceImpl extends ServiceImpl<ProxyAccountAddFriendRecordMapper, ProxyAccountAddFriendRecord> implements IProxyAccountAddFriendRecordService {

    @Override
    public ProxyAccountAddFriendRecord selectAddRecord(Long proxyAccountId, String friendProxyId) {
        return this.lambdaQuery()
                .eq(ProxyAccountAddFriendRecord::getProxyAccountId, proxyAccountId)
                .eq(ProxyAccountAddFriendRecord::getAddFriendId, friendProxyId)
                .last("limit 1")
                .one();
    }

    @Override
    public List<ProxyAccountAddFriendRecord> queryByCondition(AddFriendRecordRequestDTO requestDTO) {
        return this.lambdaQuery()
                .eq(null != requestDTO.getProxyAccountId(), ProxyAccountAddFriendRecord::getProxyAccountId, requestDTO.getProxyAccountId())
                .ne(null != requestDTO.getNotEqProxyAccountId(), ProxyAccountAddFriendRecord::getProxyAccountId, requestDTO.getNotEqProxyAccountId())
                .eq(null != requestDTO.getAddFriendId(), ProxyAccountAddFriendRecord::getAddFriendId, requestDTO.getAddFriendId())
                .eq(null != requestDTO.getFriendIdType(), ProxyAccountAddFriendRecord::getFriendIdType, requestDTO.getFriendIdType())
                .eq(null != requestDTO.getAddStatus(), ProxyAccountAddFriendRecord::getAddStatus, requestDTO.getAddStatus())
                .ge(null != requestDTO.getLastAddTimeStart(), ProxyAccountAddFriendRecord::getLastAddTime, requestDTO.getLastAddTimeStart())
                .ge(null != requestDTO.getAddCountMore(), ProxyAccountAddFriendRecord::getAddCount, requestDTO.getAddCountMore())
                .le(null != requestDTO.getAddCountLess(), ProxyAccountAddFriendRecord::getAddCount, requestDTO.getAddCountLess())
                .in(CollectionUtil.isNotEmpty(requestDTO.getAddFriendIds()), ProxyAccountAddFriendRecord::getAddFriendId, requestDTO.getAddFriendIds())
                .in(CollectionUtil.isNotEmpty(requestDTO.getAddStatusList()), ProxyAccountAddFriendRecord::getAddStatus, requestDTO.getAddStatusList())
                .orderByAsc(requestDTO.getOrderAscAddToTask(), ProxyAccountAddFriendRecord::getAddStatus, ProxyAccountAddFriendRecord::getAddCount)
                .last(null != requestDTO.getLimit(), "limit " + requestDTO.getLimit())
                .list();
    }
}
