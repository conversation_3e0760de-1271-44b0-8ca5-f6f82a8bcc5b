package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyChatMessageBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportNewMsgNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.ReportNewMsgAckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 接收到新的消息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportNewMsgNoticeActionProcessor implements INoticeActionProcessor<ReportNewMsgNoticeRequestDTO, ReportNewMsgAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyChatMessageBizService chatMessageBizService;



    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportNewMsgNoticeRequestDTO;
    }


    @Override
    public ReportNewMsgAckVO process(ReportNewMsgNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return ReportNewMsgAckVO.emptyAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-接收到新的消息-消息：{}", JacksonUtils.toJsonStr(request));

        return chatMessageBizService.processNewMessage(accountConfig, request);
    }

}
