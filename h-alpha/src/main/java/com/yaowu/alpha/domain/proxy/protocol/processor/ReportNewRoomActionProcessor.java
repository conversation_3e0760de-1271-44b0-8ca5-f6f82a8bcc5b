package com.yaowu.alpha.domain.proxy.protocol.processor;

import cn.hutool.json.JSONUtil;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.*;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportNewRoomNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.GroupInfo;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupInfo;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 上报新群或者被拉入新群
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportNewRoomActionProcessor implements INoticeActionProcessor<ReportNewRoomNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyGroupBizService groupBizService;

    private final IProxyAccountFriendBizService accountFriendBizService;

    private final IProxyTaskBizService taskBizService;

    private final IProxyCustomerRequirementBizService proxyCustomerRequirementService;

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportNewRoomNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(ReportNewRoomNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }

        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-上报新群或者被拉进新群-消息：{}", JacksonUtils.toJsonStr(request));

        ProxyGroupInfo proxyGroupInfo = groupBizService.getByAccountIdAndGroupProxyId(accountConfig.getId(), request.getWxid());
        // 群主是代理账号
        if (null == proxyGroupInfo && request.getOwner_wxid().equals(accountConfig.getProxyId())) {
            newGroup(request, accountConfig);
//            toCompleteMsg(request, accountConfig);
        } else {
            log.info("群主不是代理账号或者是群已经存在，不做处理：{}", null != proxyGroupInfo ? JSONUtil.toJsonStr(proxyGroupInfo) : "");
        }

        // 通用响应
        return NoticeBaseResponseVO.commonAck(request);
    }

    private void toCompleteMsg(ReportNewRoomNoticeRequestDTO request, ProxyAccount accountConfig) {
//        ProxyAccountFriend proxyAccountFriend = accountFriendBizService.getFriend(accountConfig.getSupportFriendId());
//        if (proxyAccountFriend == null) {
//            log.info("ReportNewRoomActionProcessor#toCompleteMsg accountConfig.getSupportFriendId 没有找到该好友：{}", JSONUtil.toJsonStr(accountConfig));
//            return;
//        }
//        // todo 正常建群是三个人，除去电销和代理账号，剩下的就是客户
//        List<ReportNewRoomNoticeRequestDTO.MemberInfo> customerProxyId = request.getMemberInfo_list().stream()
//                .filter(memberInfo -> !memberInfo.getWxid()
//                        .equals(proxyAccountFriend.getFriendProxyId()) && !memberInfo.getWxid()
//                        .equals(accountConfig.getProxyId()))
//                .collect(Collectors.toList());
//        if (CollUtil.isEmpty(customerProxyId)) {
//            log.info("没有找到客户wxid：{}", JSONUtil.toJsonStr(request));
//            return;
//        }
//        log.info("客户wxid：{}", JSONUtil.toJsonStr(customerProxyId));
//        ProxyCustomerRequirement requirementEntity = proxyCustomerRequirementService.oneUnCompleteRequirementDataByFriendId(accountConfig.getProxyId(), customerProxyId.get(0)
//                .getWxid(), null, null);
//        if (null == requirementEntity) {
//            log.info("没有找到客户需求单：{}", JSONUtil.toJsonStr(request));
//            return;
//        }
//        // todo 向群里发消息 @电销接管流程
//        SendMsgTask sendMsgTask = new SendMsgTask();
//        sendMsgTask.setAccountConfigId(accountConfig.getId());
//        sendMsgTask.setToUserId(request.getWxid());
//        sendMsgTask.setAtUserList(Lists.newArrayList(proxyAccountFriend.getFriendProxyId()));
//        ProxyRentalClueCustomerRequirementData requirement = JSONUtil.toBean(requirementEntity.getRequirement(), ProxyRentalClueCustomerRequirementData.class);
//        String msg = requirement.buildComplate();
//        if (redisTemplate.hasKey(RedisConstants.WECHAT_PAY_AMOUNT_CUSTOMER_ID + customerProxyId)) {
//            String payAmount = (String) redisTemplate.opsForValue()
//                    .get(RedisConstants.WECHAT_PAY_AMOUNT_CUSTOMER_ID + customerProxyId);
//            msg += "收到客户转账" + payAmount;
//            redisTemplate.delete(RedisConstants.WECHAT_PAY_AMOUNT_CUSTOMER_ID + customerProxyId);
//        }
//        sendMsgTask.setMsg(msg);
//        taskBizService.createSendTextMsgTask(sendMsgTask);
    }

    private void newGroup(ReportNewRoomNoticeRequestDTO request, ProxyAccount accountConfig) {
        GroupInfo group = new GroupInfo();
        group.setWxid(request.getWxid());
        group.setRoom_wxid(request.getWxid());
        group.setName(request.getName());
        group.setOwner_wxid(request.getOwner_wxid());
        group.setMember_count(request.getMemberInfo_list().size());
        group.setHead_img(request.getHead_img());
        List<String> member_wxid_list = new ArrayList<>();
        for (ReportNewRoomNoticeRequestDTO.MemberInfo memberInfo : request.getMemberInfo_list()) {
            member_wxid_list.add(memberInfo.getWxid());
        }
        group.setMember_wxid_list(member_wxid_list);

        groupBizService.updateGroupAsync(Collections.singletonList(group), accountConfig);

        // 查询群详细信息
        log.info("微信代理-上报新群或者被拉进新群-新增群：{}",request.getWxid());
        groupBizService.submitQueryGroupMembersDetailTask(Set.of(request.getWxid()));

    }

}
