package com.yaowu.alpha.domain.customer.biz.impl.node;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 24天问候节点处理器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
public class TwentyFourDayGreetingNodeProcessor extends AbstractNurtureCustomerFlowNodeProcessor {

    private static final String NODE_CODE = "24_DAY_GREETING_NODE";

    @Override
    public boolean supports(String nodeCode) {
        return NODE_CODE.equals(nodeCode);
    }

    @Override
    protected String getNodeCode() {
        return NODE_CODE;
    }

    @Override
    protected String nextNode() {
        // 24天节点是最后一个节点，没有下一个节点
        return null;
    }
} 