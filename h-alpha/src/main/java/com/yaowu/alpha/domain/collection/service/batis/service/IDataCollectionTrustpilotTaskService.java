package com.yaowu.alpha.domain.collection.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotTask;

import java.util.List;

/**
 * <p>
 * Trustpilot数据采集任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface IDataCollectionTrustpilotTaskService extends IService<DataCollectionTrustpilotTask> {
    
    /**
     * 分页查询Trustpilot数据采集任务
     * 
     * @param taskName 任务名称
     * @param taskStatus 任务状态
     * @param page 分页参数
     * @return 分页结果
     */
    Page<DataCollectionTrustpilotTask> pageByCondition(String taskName, Integer taskStatus, Page<DataCollectionTrustpilotTask> page);

    /**
     * 根据任务状态查询任务列表
     * @param taskStatus 任务状态
     * @return 任务列表
     */
    List<DataCollectionTrustpilotTask> listByTaskStatus(Integer taskStatus);

    /**
     * 启动任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean startTask(Long taskId);

    /**
     * 暂停任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean pauseTask(Long taskId);

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean cancelTask(Long taskId);
} 