package com.yaowu.alpha.domain.collection.processor;

import com.yaowu.alpha.enums.collection.DataCollectionChannelEnum;
import com.yaowu.alpha.model.entity.collection.DataCollectionRawData;

/**
 * 数据采集处理器接口
 * 使用策略模式支持不同渠道的数据解析
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IDataCollectionProcessor {
    
    /**
     * 判断是否支持指定的采集渠道
     * 
     * @param channel 采集渠道
     * @return 是否支持
     */
    boolean supports(DataCollectionChannelEnum channel);
    
    /**
     * 解析原始数据并转换为标准格式
     * 
     * @param taskId 任务ID
     * @param rawJsonData 原始JSON数据
     * @return 解析后的数据采集实体
     */
    DataCollectionRawData processData(Long taskId, String rawJsonData);
} 