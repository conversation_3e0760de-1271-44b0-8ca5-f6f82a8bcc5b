package com.yaowu.alpha.domain.collection.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotRawData;

import java.util.List;

/**
 * <p>
 * Trustpilot数据采集原始数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface IDataCollectionTrustpilotRawDataService extends IService<DataCollectionTrustpilotRawData> {
    
    /**
     * 分页查询Trustpilot数据采集原始数据
     * 
     * @param taskId 任务ID
     * @param companyName 公司名称
     * @param aiAnalysisStatus AI分析状态
     * @param page 分页参数
     * @return 分页结果
     */
    Page<DataCollectionTrustpilotRawData> pageByCondition(Long taskId, String companyName, Integer aiAnalysisStatus, Page<DataCollectionTrustpilotRawData> page);

    /**
     * 根据任务ID查询原始数据列表
     * @param taskId 任务ID
     * @return 原始数据列表
     */
    List<DataCollectionTrustpilotRawData> listByTaskId(Long taskId);

    /**
     * 根据AI分析状态查询原始数据列表
     * @param aiAnalysisStatus AI分析状态
     * @return 原始数据列表
     */
    List<DataCollectionTrustpilotRawData> listByAiAnalysisStatus(Integer aiAnalysisStatus);

    /**
     * 批量更新AI分析状态
     * @param ids 数据ID列表
     * @param aiAnalysisStatus AI分析状态
     * @return 是否成功
     */
    boolean batchUpdateAiAnalysisStatus(List<Long> ids, Integer aiAnalysisStatus);

    /**
     * 根据任务id和公司名称查询源数据
     *
     * @param taskId 任务id
     * @param companyName 公司名
     * @return DataCollectionTrustpilotRawData
     */
    DataCollectionTrustpilotRawData getByTaskIdAndCompanyName(Long taskId, String companyName);
} 