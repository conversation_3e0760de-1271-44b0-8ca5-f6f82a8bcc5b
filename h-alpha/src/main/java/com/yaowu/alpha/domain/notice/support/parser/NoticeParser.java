package com.yaowu.alpha.domain.notice.support.parser;


import com.yaowu.alpha.domain.notice.support.event.NoticeEvent;
import com.yaowu.alpha.model.bo.common.NoticeParam;

import java.util.Collections;
import java.util.List;

/**
 * 业务只需要实现该接口，就可以完成相应的渠道发送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/28
 */
public interface NoticeParser<T> {

    /**
     * 是否异步，默认异步发送
     *
     * @return
     */
    default boolean isAsync() {
        return true;
    }

    /**
     * 支持对应的事件
     *
     * @param event
     * @return
     */
    boolean support(NoticeEvent<?> event);

    /**
     * 把事件数据解析成通知参数
     * parse、parseList两个只能实现其中一个
     *
     * @param data
     * @return
     */
    default NoticeParam parse(T data) {
        return null;
    }

    /**
     * 把事件数据解析成通知参数List
     * parse、parseList两个只能实现其中一个
     *
     * @param data
     * @return
     */
    default List<NoticeParam> parseList(T data) {
        return Collections.emptyList();
    }
}
