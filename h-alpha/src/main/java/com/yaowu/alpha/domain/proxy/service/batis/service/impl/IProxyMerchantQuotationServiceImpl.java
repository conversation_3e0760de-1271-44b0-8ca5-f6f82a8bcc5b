package com.yaowu.alpha.domain.proxy.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.proxy.service.batis.mapper.ProxyMerchantQuotationMapper;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyMerchantQuotationService;
import com.yaowu.alpha.model.entity.proxy.ProxyMerchantQuotation;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 代理平台商户报价单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
public class IProxyMerchantQuotationServiceImpl extends ServiceImpl<ProxyMerchantQuotationMapper, ProxyMerchantQuotation> implements IProxyMerchantQuotationService {

    @Override
    public ProxyMerchantQuotation getProcessingQuotationByMerchantId(Long merchantId) {
        return this.lambdaQuery().eq(ProxyMerchantQuotation::getMerchantId, merchantId)
                .eq(ProxyMerchantQuotation::getCompleteFlag, false)
                .last("limit 1")
                .one();
    }

    @Override
    public ProxyMerchantQuotation getProcessingQuotationByMerchantId(String userProxyId, String agentProxyId) {
        return this.lambdaQuery().eq(ProxyMerchantQuotation::getAgentProxyId, agentProxyId)
                .eq(ProxyMerchantQuotation::getUserProxyId, userProxyId)
                .eq(ProxyMerchantQuotation::getCompleteFlag, false)
                .last("limit 1")
                .one();
    }

    @Override
    public ProxyMerchantQuotation getByCustomerRequirementId(Long customerRequirementId) {
        return this.lambdaQuery()
                .eq(ProxyMerchantQuotation::getRequirementId, customerRequirementId)
                .last("limit 1")
                .one();
    }
}
