package com.yaowu.alpha.domain.passport.biz.impl;

import com.freedom.security.common.SecurityContext;
import com.freedom.security.common.UserDetailsDto;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.domain.captcha.biz.ICaptchaBizService;
import com.yaowu.alpha.domain.passport.biz.ILoginBizService;
import com.yaowu.alpha.domain.passport.biz.IOAuth2BizService;
import com.yaowu.alpha.domain.passport.service.batis.service.IUserService;
import com.yaowu.alpha.domain.passport.service.batis.service.IUserUserLoginLogService;
import com.yaowu.alpha.model.dto.passport.LoginByEmailDTO;
import com.yaowu.alpha.model.dto.passport.LoginByPwdDTO;
import com.yaowu.alpha.model.dto.passport.LoginBySmsDTO;
import com.yaowu.alpha.model.entity.passport.User;
import com.yaowu.alpha.model.entity.passport.UserLoginLog;
import com.yaowu.alpha.model.vo.passport.TokenVO;
import com.yaowu.alpha.model.vo.passport.UserInfoVO;
import com.yaowu.alpha.utils.common.HttpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Slf4j
@Service
public class LoginBizServiceImpl implements ILoginBizService {

    @Autowired
    private IOAuth2BizService ioAuth2BizService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ICaptchaBizService captchaBizService;

    @Autowired
    private IUserUserLoginLogService userLoginLogService;

    @Override
    public TokenVO loginByPwd(LoginByPwdDTO dto) {
        User user = userService.findByUsername(dto.getUsername());
        return ioAuth2BizService.oauthTokenByPwd(user.getId(), dto.getUsername(), dto.getPassword());
    }

    @Override
    public TokenVO loginBySms(LoginBySmsDTO dto) {
        // 判断验证码是否正确
        Boolean flag = captchaBizService.verifySmsCaptcha(dto.getCrownCode(), dto.getPhone(), "LOGIN", dto.getCaptcha());
        if (!flag) {
            User user = userService.findByPhone(dto.getCrownCode(), dto.getPhone());
            saveSmsLoginLog(user != null ? user.getId() : 0L, dto, "failure", "验证码错误");
            throw new BusinessException("验证码错误");
        }
        User user = userService.findByPhone(dto.getCrownCode(), dto.getPhone());
        if (user == null) {
            user = addUser(dto.getCrownCode(), dto.getPhone(), "");
        }
        try {
            TokenVO tokenVO = ioAuth2BizService.oauthTokenBySms(user.getId(), dto.getPhone(), dto.getCaptcha());
            saveSmsLoginLog(user.getId(), dto, "success", "登录成功");
            return tokenVO;
        } catch (Exception e) {
            saveSmsLoginLog(user.getId(), dto, "failure", e.getMessage() != null ? e.getMessage().substring(0, Math.min(e.getMessage().length(), 1000)) : "登录失败");
            throw e;
        }
    }

    private User addUser(String crownCode, String phone, String email) {
        User user = new User();
        user.setUsername("");
        user.setRealName("");
        user.setPassword("");
        user.setEmail(email);
        user.setCrownCode(crownCode);
        user.setPhone(phone);
        user.setStatus(1);
        userService.save(user);
        return user;
    }

    @Override
    public TokenVO loginByEmail(LoginByEmailDTO dto) {
        // 判断验证码是否正确
        Boolean flag = captchaBizService.verifyEmailCaptcha(dto.getEmail(), "LOGIN", dto.getCaptcha());
        if (!flag) {
            User user = userService.findByEmail(dto.getEmail());
            saveEmailLoginLog(user != null ? user.getId() : 0L, dto, "failure", "验证码错误");
            throw new BusinessException("验证码错误");
        }
        User user = userService.findByEmail(dto.getEmail());
        if (user == null) {
            user = addUser("", "", dto.getEmail());
        }
        try {
            TokenVO tokenVO = ioAuth2BizService.oauthTokenByEmail(user.getId(), dto.getEmail(), dto.getCaptcha());
            saveEmailLoginLog(user.getId(), dto, "success", "登录成功");
            return tokenVO;
        } catch (Exception e) {
            saveEmailLoginLog(user.getId(), dto, "failure", e.getMessage() != null ? e.getMessage().substring(0, Math.min(e.getMessage().length(), 1000)) : "登录失败");
            throw e;
        }
    }

    @Override
    public TokenVO refreshToken(String refreshToken) {
        return ioAuth2BizService.refreshToken(refreshToken);
    }

    private String getIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String ipAddress = request.getHeader("X-Real-IP");
        if (ipAddress != null && !ipAddress.isEmpty() && !"unknown".equalsIgnoreCase(ipAddress)) {
            return ipAddress;
        }
        ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress != null && !ipAddress.isEmpty() && !"unknown".equalsIgnoreCase(ipAddress)) {
            // X-Forwarded-For可能包含多个IP地址，取第一个非unknown的有效IP字符串
            return ipAddress.split(",")[0];
        }
        // 如果没有通过代理，直接获取远程地址
        return request.getRemoteAddr();
    }

    @Override
    public UserInfoVO getUserInfo() {
        UserDetailsDto currentUser = SecurityContext.getCurrentUser();
        Long userId = currentUser.getUserId();
        User user = userService.getById(userId);
        if (user == null) {
            return new UserInfoVO();
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setId(user.getId());
        userInfoVO.setUsername(user.getUsername());
        userInfoVO.setRealName(user.getRealName());
        userInfoVO.setCrownCode(user.getCrownCode());
        userInfoVO.setPhone(user.getPhone());
        userInfoVO.setEmail(user.getEmail());
        return userInfoVO;
    }

    private String getUserAgent(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String userAgent = request.getHeader("User-Agent");
        return StringUtils.hasText(userAgent) ? userAgent : "";
    }

    private String getAppVersion(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String appVersion = request.getHeader("appversion");
        return StringUtils.hasText(appVersion) ? appVersion : "";
    }

    private String getModel(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String model = request.getHeader("model");
        return StringUtils.hasText(model) ? model : "";
    }

    private String getPlatform(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        String platform = request.getHeader("platform");
        return StringUtils.hasText(platform) ? platform : "";
    }

    private void saveSmsLoginLog(Long userId, LoginBySmsDTO dto, String result, String remark) {
        UserLoginLog loginLog = new UserLoginLog();
        loginLog.setUserId(userId);
        loginLog.setAccount(dto.getCrownCode() + dto.getPhone());
        loginLog.setLoginMethod("sms");
        loginLog.setLoginParam(dto);
        loginLog.setLoginResult(result);
        loginLog.setLoginRemark(remark);
        fillLoginLogField(loginLog);
        userLoginLogService.save(loginLog);
    }

    private void saveEmailLoginLog(Long userId, LoginByEmailDTO dto, String result, String remark) {
        UserLoginLog loginLog = new UserLoginLog();
        loginLog.setUserId(userId);
        loginLog.setAccount(dto.getEmail());
        loginLog.setLoginMethod("email");
        loginLog.setLoginParam(dto);
        loginLog.setLoginResult(result);
        loginLog.setLoginRemark(remark);
        fillLoginLogField(loginLog);
        userLoginLogService.save(loginLog);
    }

    private void fillLoginLogField(UserLoginLog loginLog) {
        loginLog.setLoginTime(LocalDateTime.now());
        HttpServletRequest request = HttpUtils.getServletRequest();
        loginLog.setUserAgent(getUserAgent(request));
        loginLog.setIpAddress(getIpAddress(request));
        loginLog.setPlatform(getPlatform(request));
        loginLog.setAppVersion(getAppVersion(request));
        loginLog.setModel(getModel(request));
    }
}
