package com.yaowu.alpha.domain.proxy.protocol.processor;

import cn.hutool.json.JSONUtil;
import com.freedom.redis.utils.RedisHelper;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.common.processor.impl.WhatsAppVerificationProcessor;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.SearchWhatsappContactNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.SearchWhatsappContactNoticeRequestDTO.Contact;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.ProxySearchStrangerVO;
import com.yaowu.alpha.utils.common.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 搜索WhatsApp联系人结果处理器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SearchWhatsappContactNoticeActionProcessor implements INoticeActionProcessor<SearchWhatsappContactNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;
    private final RedisHelper redisHelper;
    private final WhatsAppVerificationProcessor whatsAppVerificationProcessor;



    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof SearchWhatsappContactNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(SearchWhatsappContactNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc + "代理-搜索WhatsApp联系人结果-消息：{}", JacksonUtils.toJsonStr(request));
        // 检查任务执行结果并上报验证结果
        reportVerificationResult(request);
        // 处理联系人信息
        SearchWhatsappContactNoticeRequestDTO.Contact contact = request.getContact();
        if (Objects.nonNull(contact) && Objects.nonNull(contact.getNumber())) {
            processContact(request, accountConfig, contact);
        }
        return NoticeBaseResponseVO.commonAck(request);
    }

    /**
     * 上报验证结果
     */
    private void reportVerificationResult(SearchWhatsappContactNoticeRequestDTO request) {
        // 格式化电话号码，确保带有+号
        String targetPhone = formatPhoneNumber(request.getTargetPhone());
        
        // 检查电话号码是否为空
        if (targetPhone == null) {
            log.warn("WhatsApp验证结果上报跳过: targetPhone为空, request={}", JSONUtil.toJsonStr(request));
            return;
        }
        
        Boolean success = StringUtil.isBlank(request.getError());
        String errorMsg = StringUtil.isBlank(request.getMessage()) ? request.getError() : request.getMessage();
        Boolean isRegistered = request.getIsRegistered();
        String rawResult = JSONUtil.toJsonStr(request);
        
        whatsAppVerificationProcessor.reportWhatsappVerificationResult(
            targetPhone, success, errorMsg, isRegistered, rawResult
        );
    }
    
    /**
     * 格式化电话号码，确保带有+号
     */
    private String formatPhoneNumber(String phoneNumber) {
        if (Objects.isNull(phoneNumber)) {
            return null;
        }
        return phoneNumber.startsWith("+") ? phoneNumber : "+" + phoneNumber;
    }

    /**
     * 处理联系人信息
     */
    private void processContact(SearchWhatsappContactNoticeRequestDTO request, ProxyAccount accountConfig, 
                               SearchWhatsappContactNoticeRequestDTO.Contact contact) {
        String phone = accountConfig.getPhone();
        if (StringUtil.equals(phone, "+"+contact.getNumber())){
            log.info("WhatsApp联系人搜索结果，账户ID：{}，联系人号码：{}，姓名：{}，为自身",accountConfig.getId(),"+"+contact.getNumber(),contact.getName());
            return;
        }

        // 缓存联系人信息
        cacheContactSearchResult(accountConfig, contact);
        
        log.info("成功处理WhatsApp联系人搜索结果，账户ID：{}，联系人号码：{}，姓名：{}", 
                 accountConfig.getId(), contact.getNumber(), contact.getName());
    }

    /**
     * 缓存联系人搜索结果
     */
    private void cacheContactSearchResult(ProxyAccount accountConfig, Contact contact) {
        ProxySearchStrangerVO result = buildSearchResult(contact);
        String key = String.format("SEARCH_STRANGER:%s%s", accountConfig.getId(), contact.getNumber());
        cacheContactInfo(key, result);
    }

    /**
     * 构建搜索结果对象
     */
    private ProxySearchStrangerVO buildSearchResult(Contact contact) {
        ProxySearchStrangerVO result = new ProxySearchStrangerVO();
        result.setName(contact.getName());
        result.setNumber(contact.getNumber());
        result.setFormattedNumber(contact.getFormattedNumber());
        result.setIsMyContact(contact.getIsMyContact());
        result.setIsBusiness(contact.getIsBusiness());
        result.setIsGroup(contact.getIsGroup());
        result.setProfilePicUrl(contact.getProfilePicUrl());
        return result;
    }

    /**
     * 缓存二维码授权信息
     *
     * @param key
     * @param value
     */
    private void cacheContactInfo(String key, Object value) {
        redisHelper.strSet(key, value);
        redisHelper.setExpire(key, 10L, TimeUnit.MINUTES);
    }
}
