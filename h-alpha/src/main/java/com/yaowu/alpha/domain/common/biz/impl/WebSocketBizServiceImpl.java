package com.yaowu.alpha.domain.common.biz.impl;

import com.yaowu.alpha.domain.common.biz.IWebSocketBizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * WebSocket-业务逻辑
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketBizServiceImpl implements IWebSocketBizService {

//    private final SimpMessagingTemplate messagingTemplate;
//
//
//    @Override
//    public void pushToUser(Long userId, WebSocketEventEnum eventEnum, Map<String, Object> params) {
//        messagingTemplate.convertAndSendToUser(String.valueOf(userId),
//                "/queue/messages",
//                new WebSocketEventVO(IdUtil.fastSimpleUUID(), eventEnum, params).toJsonStr()
//        );
//    }
//
//    @Override
//    public void pushToUser(Long userId, WebSocketEventEnum eventEnum) {
//        pushToUser(userId, eventEnum, null);
//    }

}
