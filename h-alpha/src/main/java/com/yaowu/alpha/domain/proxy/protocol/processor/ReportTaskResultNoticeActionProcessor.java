package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportTaskResultNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.utils.common.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 任务执行结果通知处理器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportTaskResultNoticeActionProcessor implements INoticeActionProcessor<ReportTaskResultNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyTaskBizService taskBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportTaskResultNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(ReportTaskResultNoticeRequestDTO request) {
        //  whatsApp拉取二维码的任务
        if (StringUtil.isBlank(request.getProxyId()) && ProxyThirdTypeEnum.WHAT_APP.getVal().equals(request.getProxyThirdType())){
            log.info("whatsapp代理-二维码拉取任务上报-消息：{}", JacksonUtils.toJsonStr(request));
            taskBizService.finishTask(request);
            return NoticeBaseResponseVO.commonAck(request);
        }
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-轮询任务-消息：{}", JacksonUtils.toJsonStr(request));

        taskBizService.finishTask(request);
        return NoticeBaseResponseVO.commonAck(request);
    }

}
