package com.yaowu.alpha.domain.collection.biz.listener;

import com.yaowu.alpha.domain.collection.biz.IDataCollectionTaskBizService;
import com.yaowu.alpha.enums.proxy.TaskTypeEnum;
import com.yaowu.alpha.event.collection.DataCollectionInstructionResultEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 数据采集任务结果事件监听器
 * <p>
 * 监听数据采集指令执行结果事件，根据任务类型进行相应的处理：
 * <ul>
 *   <li><strong>GMap采集任务</strong>：调用业务服务更新采集任务状态</li>
 *   <li><strong>其他任务类型</strong>：暂不处理</li>
 * </ul>
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataCollectionTaskResultEventListener {
    
    private final IDataCollectionTaskBizService dataCollectionTaskBizService;
    
    /**
     * 监听数据采集指令执行结果事件
     * <p>
     * 异步处理事件，避免阻塞主流程
     * 
     * @param event 数据采集指令执行结果事件
     */
    @Async("commonExecutor")
    @EventListener
    public void handleCreateDataCollectionTaskResult(DataCollectionInstructionResultEvent event) {
        log.info("接收到数据采集指令执行结果事件，指令ID：{}，任务类型：{}，执行结果：{}", 
            event.getInstructionId(), event.getTaskType(), event.getSuccess());
        
        try {
            // 只处理GMap数据采集任务
            if (!isGMapCollectionTask(event.getTaskType())) {
                log.info("跳过非GMap采集任务的指令结果，任务类型：{}", event.getTaskType());
                return;
            }
            
            // 获取采集任务ID
            Long collectionTaskId = extractCollectionTaskId(event);
            if (collectionTaskId == null) {
                log.warn("无法从事件中获取采集任务ID，跳过处理，指令ID：{}", event.getInstructionId());
                return;
            }
            
            // 调用业务服务处理采集任务状态更新
            dataCollectionTaskBizService.handleCreateDataCollectionTaskResult(
                collectionTaskId, event.getSuccess(), event.getErrorReason());
            
        } catch (Exception e) {
            log.error("处理数据采集指令执行结果事件时发生异常，指令ID：{}", event.getInstructionId(), e);
        }
    }
    
    /**
     * 判断是否为GMap数据采集任务
     * 
     * @param taskType 任务类型
     * @return 是否为GMap采集任务
     */
    private boolean isGMapCollectionTask(TaskTypeEnum taskType) {
        return TaskTypeEnum.DATA_COLLECTION_TASK_GMAP == taskType;
    }
    
    /**
     * 从事件中提取采集任务ID
     * 
     * @param event 事件对象
     * @return 采集任务ID，如果无法提取则返回null
     */
    private Long extractCollectionTaskId(DataCollectionInstructionResultEvent event) {
        if (event.getInstructionData() == null) {
            return null;
        }
        return event.getInstructionData().getCollectionTaskId();
    }
}