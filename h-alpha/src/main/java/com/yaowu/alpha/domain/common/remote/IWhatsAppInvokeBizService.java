package com.yaowu.alpha.domain.common.remote;

import com.yaowu.alpha.model.dto.proxy.RemoteSearchContactDTO;
import com.yaowu.alpha.model.dto.whatsapp.WhatsappQrcodeAuthModel;
import com.yaowu.alpha.model.dto.whatsapp.WhatsappUnbindAccountModel;
import com.yaowu.alpha.model.vo.whatsapp.RemoteSearchContactResultVO;

/**
 * <AUTHOR>
 * @date 2025/3/19 11:21
 */
public interface IWhatsAppInvokeBizService {

    /**
     * 发起二维码授权流程
     */
    Boolean createAccountOrStartQrcodeAuth(WhatsappQrcodeAuthModel model);

    /**
     * 发起解绑流程
     */
    Boolean unbindAccount(WhatsappUnbindAccountModel model);

    /**
     * 根据完整手机号搜索陌生人
     */
    RemoteSearchContactResultVO searchContact(RemoteSearchContactDTO dto);

}
