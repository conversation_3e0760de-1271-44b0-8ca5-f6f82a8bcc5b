package com.yaowu.alpha.domain.customer.biz.impl.node;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 首个问候节点处理器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Component
public class FirstGreetingNodeProcessor extends AbstractNurtureCustomerFlowNodeProcessor {

    private static final String NODE_CODE = "FIRST_GREETING_NODE";
    private static final String NEXT_NODE_CODE = "3_DAY_GREETING_NODE";

    @Override
    public boolean supports(String nodeCode) {
        return NODE_CODE.equals(nodeCode);
    }

    @Override
    protected String getNodeCode() {
        return NODE_CODE;
    }

    @Override
    protected String nextNode() {
        return NEXT_NODE_CODE;
    }
} 