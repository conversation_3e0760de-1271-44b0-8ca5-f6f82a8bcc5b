package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.ProxyAccountQueryDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 三方代理账户配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyAccountConfigService extends IService<ProxyAccount> {

    /**
     * 根据账户统计数量
     */
    long countByProxyId(String proxyId);

    /**
     * 查询账户
     */
    ProxyAccount getOne(String proxyId, Integer proxyType, Integer thirdParty);

    /**
     * 查询账户
     */
    ProxyAccount getOneByProxyId(String proxyId);

    /**
     * 查询账户
     */
    ProxyAccount getOneByKeyword(String keyword);

    /**
     * 获取所有代理账号
     */
    List<String> listAllProxyId();

    /**
     * 获取所有代理账号
     */
    List<Long> listAllProxyAccountId();

    /**
     * 获取所有代理账号
     */
    List<Long> listAllProxyAccountId(ProxyAccountQueryDTO queryDTO);

    BasePage<ProxyAccount> pageByCondition(ProxyAccountQueryDTO dto);

    List<ProxyAccount> listByCondition(ProxyAccountQueryDTO queryDTO);

    /**
     * 更新更新时间
     */
    void updateOnlineFlagAndUpdateTimeAsync(Long accountConfigId);

    /**
     * 获取掉线的账号
     */
    List<ProxyAccount> listOfflineAccountConfig(LocalDateTime gtUpdateTime,
                                                LocalDateTime ltUpdateTime,
                                                List<ProxyTypeEnum> proxyTypes,
                                                List<ProxyThirdTypeEnum> proxyThirdTypes);

    /**
     * 判断是否都是代理账号
     */
    boolean isAllProxyAccounts(Set<String> proxyIds);

    /**
     * 根据条件获取
     *
     * @param dto
     * @return
     */
    ProxyAccount getByCondition(ProxyAccountQueryDTO dto);

    /**
     * 根据代理账号id获取
     *
     * @param ids
     * @return
     */
    Map<Long, ProxyAccount> getByIds(List<Long> ids);

    /**
     * 检查智能体是否被代理账户使用
     *
     * @param appKey 智能体应用Key
     * @return 使用该智能体的代理账户数量
     */
    Long countAccountsByAgentAppKey(String appKey);
    
    List<Long> getIdsByLikeAccountName(String accountName);

    void updateOffline(Set<Long> accountIds);
}
