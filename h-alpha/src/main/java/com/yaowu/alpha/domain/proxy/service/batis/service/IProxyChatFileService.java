package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyChatFile;

import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 智能体聊天文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyChatFileService extends IService<ProxyChatFile> {

    long countByFileIndex(String fileIndex);

    ProxyChatFile selectByFileIndex(String fileIndex);

    Map<String,ProxyChatFile> listByFileIndex(Set<String> fileIndex);
}
