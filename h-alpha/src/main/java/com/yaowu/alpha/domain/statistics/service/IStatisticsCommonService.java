package com.yaowu.alpha.domain.statistics.service;

import com.yaowu.alpha.model.dto.statistics.StatisticsCommonDTO;
import com.yaowu.alpha.model.vo.statistics.ChatStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.DailyEmailStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.EmailStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.GreetingTaskStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.DailyGreetingTaskStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.CustomerNurtureFlowStatisticsVO;
import com.yaowu.alpha.model.vo.statistics.DailyChatStatisticsVO;

import java.util.List;

/**
 * 统计通用服务接口
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface IStatisticsCommonService {

    /**
     * 查询邮件统计信息
     * 按租户分组统计各状态邮件数量
     *
     * @param queryDTO 查询条件
     * @return 邮件统计结果列表
     */
    EmailStatisticsVO getEmailStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询每日邮件统计信息
     * 按租户和日期分组统计各状态邮件数量
     *
     * @param queryDTO 查询条件
     * @return 每日邮件统计结果列表
     */
    List<DailyEmailStatisticsVO> getDailyEmailStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询对话统计信息
     * 按租户和三方类型分组统计对话总数
     *
     * @param queryDTO 查询条件
     * @return 对话统计结果列表
     */
    List<ChatStatisticsVO> getChatStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询渠道对话回复统计信息
     * 按租户和三方类型分组统计对话回复总数
     *
     * @param queryDTO 查询条件
     * @return 渠道对话统计结果列表
     */
    List<ChatStatisticsVO> getChatReplyStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询打招呼各渠道任务统计信息
     * 按租户和三方类型分组统计各状态任务数量
     *
     * @param queryDTO 查询条件
     * @return 打招呼任务统计结果列表
     */
    List<GreetingTaskStatisticsVO> getGreetingTaskStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询每日打招呼任务执行情况统计信息
     * 按租户、三方类型和日期分组统计已执行和失败的任务数量
     *
     * @param queryDTO 查询条件
     * @return 每日打招呼任务统计结果列表
     */
    List<DailyGreetingTaskStatisticsVO> getDailyGreetingTaskStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询客户培育流程统计信息
     * 按租户和客户联系方式类型分组统计各状态流程数量
     *
     * @param queryDTO 查询条件
     * @return 客户培育流程统计结果
     */
    List<CustomerNurtureFlowStatisticsVO> getCustomerNurtureFlowStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询每日对话客户数统计信息
     * 按租户、三方类型和日期分组统计每日对话客户数
     *
     * @param queryDTO 查询条件
     * @return 每日对话统计结果列表
     */
    List<DailyChatStatisticsVO> getDailyChatStatistics(StatisticsCommonDTO queryDTO);

    /**
     * 查询每日回复客户数统计信息
     * 按租户、三方类型和日期分组统计每日回复客户数
     *
     * @param queryDTO 查询条件
     * @return 每日回复统计结果列表
     */
    List<DailyChatStatisticsVO> getDailyChatReplyStatistics(StatisticsCommonDTO queryDTO);
}
