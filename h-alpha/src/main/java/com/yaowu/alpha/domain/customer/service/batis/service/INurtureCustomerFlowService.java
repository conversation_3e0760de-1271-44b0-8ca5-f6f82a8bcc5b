package com.yaowu.alpha.domain.customer.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.bo.customer.TenantContactTypePair;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 客户培育流程状态管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface INurtureCustomerFlowService extends IService<NurtureCustomerFlow> {

    /**
     * 检查客户是否存在未完成的培育流程
     * 
     * @param customerId 客户ID
     * @return true-存在未完成流程，false-不存在
     */
    boolean hasUnfinishedFlow(Long customerId);

    /**
     * 批量检查客户是否存在未完成的培育流程
     * 
     * @param customerIds 客户ID列表
     * @return 存在未完成流程的客户ID集合
     */
    Set<Long> getCustomersWithUnfinishedFlow(List<Long> customerIds);

    /**
     * 获取待启动的发送人列表
     * 
     * @return 待启动的发送人列表
     */
    List<String> listPendingSenders();

    /**
     * 获取发送人的待启动流程列表
     * 
     * @param sender 发送人ID
     * @param limit 限制数量
     * @return 待启动流程列表
     */
    List<NurtureCustomerFlow> listPendingFlowsBySender(String sender, int limit);

    /**
     * 根据好友ID查询正在进行的培育流程
     * 
     * @param friendId 好友ID
     * @return 正在进行的培育流程，可能为null
     */
    NurtureCustomerFlow getActiveFlowByFriendId(Long friendId);

    /**
     * 获取有待启动流程的租户ID列表
     * 
     * @return 租户ID列表
     */
    List<Long> listPendingTenantIds();

    /**
     * 按租户查询未分配sender的待启动流程
     * 
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 待启动流程列表
     */
    List<NurtureCustomerFlow> listPendingFlowsByTenant(Long tenantId, int limit);

    /**
     * 获取有待启动流程的租户和联系方式组合列表
     * 
     * @return 租户和联系方式组合列表
     */
    List<TenantContactTypePair> listPendingTenantContactTypePairs();

    /**
     * 按租户和联系方式查询未分配sender的待启动流程
     * 
     * @param tenantId 租户ID
     * @param contactType 联系方式类型
     * @param limit 限制数量
     * @return 待启动流程列表
     */
    List<NurtureCustomerFlow> listPendingFlowsByTenantAndContactType(Long tenantId, ProxyThirdTypeEnum contactType, int limit);
}
