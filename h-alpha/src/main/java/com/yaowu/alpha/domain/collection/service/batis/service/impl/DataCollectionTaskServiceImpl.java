package com.yaowu.alpha.domain.collection.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.collection.service.batis.mapper.DataCollectionTaskMapper;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTaskService;
import com.yaowu.alpha.model.dto.collection.DataCollectionTaskPageDTO;
import com.yaowu.alpha.model.dto.collection.ListDataCollectionTaskDTO;
import com.yaowu.alpha.model.entity.collection.DataCollectionTask;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 数据采集任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class DataCollectionTaskServiceImpl extends ServiceImpl<DataCollectionTaskMapper, DataCollectionTask> implements IDataCollectionTaskService {

    @Override
    public Page<DataCollectionTask> pageByCondition(DataCollectionTaskPageDTO dto) {
        LambdaQueryWrapper<DataCollectionTask> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        buildBasicConditions(wrapper, dto);
        buildKeywordConditions(wrapper, dto);
        buildTimeRangeConditions(wrapper, dto);
        
        // 排序：最新创建的在前
        wrapper.orderByDesc(DataCollectionTask::getCreateTime);
        
        // 分页查询
        return page(dto.pageRequest(), wrapper);
    }

    @Override
    public List<DataCollectionTask> listByCondition(ListDataCollectionTaskDTO dto) {
        if (dto.isEmpty()) {
            return List.of();
        }
        LambdaQueryWrapper<DataCollectionTask> wrapper = new LambdaQueryWrapper<>();
        // 构建查询条件
        wrapper.like(StrUtil.isNotBlank(dto.getTaskName()), DataCollectionTask::getTaskName, dto.getTaskName());

        // 排序：最新创建的在前
        wrapper.orderByDesc(DataCollectionTask::getCreateTime);
        return list(wrapper);
    }

    /**
     * 构建基础查询条件
     */
    private void buildBasicConditions(LambdaQueryWrapper<DataCollectionTask> wrapper, DataCollectionTaskPageDTO dto) {
        wrapper.like(StrUtil.isNotBlank(dto.getTaskName()), DataCollectionTask::getTaskName, dto.getTaskName())
               .eq(dto.getCollectionChannel() != null, DataCollectionTask::getCollectionChannel, dto.getCollectionChannel())
               .eq(dto.getTaskStatus() != null, DataCollectionTask::getTaskStatus, dto.getTaskStatus())
               .in(CollUtil.isNotEmpty(dto.getTaskStatusSet()), DataCollectionTask::getTaskStatus, dto.getTaskStatusSet());
    }
    
    /**
     * 构建关键词查询条件
     */
    private void buildKeywordConditions(LambdaQueryWrapper<DataCollectionTask> wrapper, DataCollectionTaskPageDTO dto) {
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            wrapper.and(qw -> qw
                .like(DataCollectionTask::getTaskName, dto.getKeyword())
                .or().like(DataCollectionTask::getCollectionParam, dto.getKeyword())
            );
        }
    }
    
    /**
     * 构建时间范围查询条件
     */
    private void buildTimeRangeConditions(LambdaQueryWrapper<DataCollectionTask> wrapper, DataCollectionTaskPageDTO dto) {
        wrapper.ge(dto.getCreateTimeStart() != null, DataCollectionTask::getCreateTime, dto.getCreateTimeStart())
               .le(dto.getCreateTimeEnd() != null, DataCollectionTask::getCreateTime, dto.getCreateTimeEnd())
               .ge(dto.getStartExecuteTimeStart() != null, DataCollectionTask::getStartExecuteTime, dto.getStartExecuteTimeStart())
               .le(dto.getStartExecuteTimeEnd() != null, DataCollectionTask::getStartExecuteTime, dto.getStartExecuteTimeEnd());
    }
}
