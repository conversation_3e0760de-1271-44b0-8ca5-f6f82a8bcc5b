package com.yaowu.alpha.domain.common.feign;

import com.freedom.web.model.resp.BaseResult;
import com.yaowu.alpha.config.feign.LLMFeignConfig;
import com.yaowu.alpha.domain.common.feign.fallback.RemoteLLMServiceFeignFactory;
import com.yaowu.alpha.model.dto.remote.llm.RemoteAudioExtractDTO;
import com.yaowu.alpha.model.dto.remote.llm.RemoteLlmRequestDTO;
import com.yaowu.alpha.model.dto.remote.llm.agent.RemoteLLMChatAgentSubmitDTO;
import com.yaowu.alpha.model.dto.remote.llm.agent.RemoteLLMFileAgentSubmitDTO;
import com.yaowu.alpha.model.vo.remote.llm.RemoteAudioExtractVO;
import com.yaowu.alpha.model.vo.remote.llm.RemoteLLMAgentChatVO;
import com.yaowu.alpha.model.vo.remote.llm.RemoteLlmResponseVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2024/8/3 18:33
 */
@FeignClient(
        value = "mstar-llm-rebot",
        url = "${remote.service.llm}",
        contextId = "IRemoteLlmServiceFeign",
        fallbackFactory = RemoteLLMServiceFeignFactory.class,
        configuration = LLMFeignConfig.class
)
public interface IRemoteLLMBizServiceFeign {

    @PostMapping("/v1/api/agent/chat/message/submit/invoke")
    @Operation(summary = "提交聊天agent消息")
    RemoteLlmResponseVO<BaseResult<RemoteLLMAgentChatVO>> submitChatMessage(@RequestBody @Validated RemoteLlmRequestDTO<RemoteLLMChatAgentSubmitDTO> dto);

    @PostMapping("/v1/api/agent/file/message/submit/invoke")
    @Operation(summary = "提交文件解析agent消息")
    RemoteLlmResponseVO<BaseResult<RemoteLLMAgentChatVO>> submitFileMessage(@RequestBody @Validated RemoteLlmRequestDTO<RemoteLLMFileAgentSubmitDTO> dto);


    @PostMapping("/mstar/transcribe/audio/invoke")
    @Operation(summary = "语音转写")
    RemoteLlmResponseVO<BaseResult<RemoteAudioExtractVO>> transcribeAudio(@RequestBody @Validated RemoteLlmRequestDTO<RemoteAudioExtractDTO> dto);

}
