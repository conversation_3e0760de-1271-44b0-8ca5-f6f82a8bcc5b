package com.yaowu.alpha.domain.collection.biz;

import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotRawDataCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotTaskCreateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotTaskUpdateDTO;
import com.yaowu.alpha.model.dto.collection.DataCollectionTrustpilotRawDataUpdateDTO;
import com.yaowu.alpha.model.vo.collection.DataCollectionTrustpilotTaskVO;
import com.yaowu.alpha.model.vo.collection.DataCollectionTrustpilotRawDataVO;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotRawData;

/**
 * Trustpilot数据采集任务业务服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface IDataCollectionTrustpilotTaskBizService {
    
    /**
     * 1. 创建任务
     * 
     * @param dto 创建参数
     * @return 任务ID
     */
    Long createTask(DataCollectionTrustpilotTaskCreateDTO dto);
    
    /**
     * 2. 拉取单个到达预期执行时间的任务
     * 
     * @return 待执行的任务，如果没有则返回null
     */
    DataCollectionTrustpilotTaskVO pullNextTask();
    
    /**
     * 3. 更新任务信息接口（状态，数据页数、当前页数、错误信息）
     * 
     * @param dto 更新参数
     * @return 是否成功
     */
    Boolean updateTask(DataCollectionTrustpilotTaskUpdateDTO dto);
    
    /**
     * 4. 上报row data数据接口
     * 
     * @param dto 原始数据创建参数
     * @return 数据ID
     */
    Long reportRawData(DataCollectionTrustpilotRawDataCreateDTO dto);
    
    /**
     * 5. 获取单个row data ai待分析数据
     * 
     * @return 待分析的原始数据，如果没有则返回null
     */
    DataCollectionTrustpilotRawDataVO getNextAiAnalysisData();
    
    /**
     * 6. 更新row data ai结果
     * 
     * @param dto 更新参数
     * @return 是否成功
     */
    Boolean updateAiAnalysisResult(DataCollectionTrustpilotRawDataUpdateDTO dto);
} 