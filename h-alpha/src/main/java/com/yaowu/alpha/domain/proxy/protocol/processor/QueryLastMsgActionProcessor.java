package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyChatMessageBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyChatMessageService;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.QueryLastMsgRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessage;
import com.yaowu.alpha.model.vo.proxy.QueryLastMsgVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 查询最新消息
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class QueryLastMsgActionProcessor implements INoticeActionProcessor<QueryLastMsgRequestDTO, QueryLastMsgVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyChatMessageService proxyChatMessageService;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof QueryLastMsgRequestDTO;
    }

    @Override
    public QueryLastMsgVO process(QueryLastMsgRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        QueryLastMsgVO vo = new QueryLastMsgVO();
        vo.setProxyId(request.getProxyId());
        vo.setProxyType(request.getProxyType());
        vo.setThirdParty(request.getProxyThirdType());
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return vo;
        }
        ProxyChatMessage lastChatMessage = proxyChatMessageService.getLastChatMessage(accountConfig.getId(), request.getWxid_to());
        if (lastChatMessage == null) {
            return vo;
        }
        QueryLastMsgVO.QueryLastMsg lastMsg = new QueryLastMsgVO.QueryLastMsg();
        lastMsg.setMsg_type(lastChatMessage.getMessageType());
        lastMsg.setMsg_timestamp(lastChatMessage.getMessageTimestamp());
        lastMsg.setWxid_from(lastChatMessage.getChatUserProxyId());
        lastMsg.setWxid_to(lastChatMessage.getToUserProxyId());
        lastMsg.setMsg_id(lastChatMessage.getOriginMessageId());
        lastMsg.setMsg(lastChatMessage.getContent());
        vo.setData(lastMsg);
        return vo;
    }

}
