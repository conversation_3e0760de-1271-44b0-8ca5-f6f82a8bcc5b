package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyGroupInfoService;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberInfoNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyGroupInfo;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.convertor.proxy.ProxyGroupMapStruct;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 群人数变化
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportRoomMemberInfoProcessor implements INoticeActionProcessor<ReportRoomMemberInfoNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyGroupInfoService proxyGroupInfoService;

    private final IProxyGroupBizService proxyGroupBizService;

    private final WetoolCommonMapStruct wetoolCommonMapStruct;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportRoomMemberInfoNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(ReportRoomMemberInfoNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-群成员信息详情-消息：{}", JacksonUtils.toJsonStr(request));

        StreamUtil.of(request.getRoom_data_list()).forEach(roomData -> {
            //更新对应的群消息
            ProxyGroupInfo proxyGroupInfo = proxyGroupInfoService.getByAccountIdAndGroupProxyId(accountConfig.getId(), roomData.getRoom_wxid());
            if (Objects.nonNull(proxyGroupInfo)){
                proxyGroupInfo.setTenantId(accountConfig.getTenantId());
                proxyGroupInfo.setGroupName(roomData.getName());
                proxyGroupInfo.setMemberCount(roomData.getMember_count());
                proxyGroupInfo.setOwnerProxyId(roomData.getOwner_wxid());
                proxyGroupInfo.setGroupInfo(JacksonUtils.toJsonStr(ProxyGroupMapStruct.INSTANCE.toGroupInfoJson(roomData)));
                proxyGroupInfoService.updateById(proxyGroupInfo);
                //更新群成员消息
                List<UpdateGroupMemberInfo> updateGroupMemberInfos  = wetoolCommonMapStruct.toUpdateGroupMemberInfoList(roomData.getMemberInfo_list());
                proxyGroupBizService.saveOrUpdateMemberDetailsAsync(proxyGroupInfo,updateGroupMemberInfos);
            }
        });
        return NoticeBaseResponseVO.commonAck(request);
    }

    @Data
    public static class UpdateGroupMemberInfo {
        @Schema(title = "微信号")
        private String wxid;

        @Schema(title = "微信别名")
        private String wx_alias;

        @Schema(title = "昵称")
        private String nickname;

        @Schema(title = "群昵称")
        private String room_nickname;

        @Schema(title = "头像url")
        private String head_img;
    }


}
