package com.yaowu.alpha.domain.facebook.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTaskKeyword;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Facebook搜索任务关键字 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface FacebookSearchTaskKeywordService extends IService<FacebookSearchTaskKeyword> {

    List<FacebookSearchTaskKeyword> likeByKeyword(String keyword);

    List<Long> listIdsByLikeByKeyword(String keyword);

    Map<Long, List<String>> getKeywordsMap(List<Long> taskIds);

    Map<Long, FacebookSearchTaskKeyword> getKeywordMap(List<Long> ids);

    Boolean delByTaskId(Long taskId);
}