package com.yaowu.alpha.domain.common.biz;

import com.freedom.web.model.resp.BasePage;
import com.yaowu.alpha.model.dto.common.LangDictAddRequest;
import com.yaowu.alpha.model.dto.common.LangDictParentRequest;
import com.yaowu.alpha.model.dto.common.LangDictQueryRequest;
import com.yaowu.alpha.model.vo.common.LangDictVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/22-19:20
 */
public interface ILangDictBizService {

    /**
     * 分页查询语言字典
     *
     * @param request
     * @return
     */
    BasePage<LangDictVO> page(LangDictQueryRequest request);

    /**
     * 获取语言字典
     *
     * @param request
     * @return
     */
    List<LangDictVO> list(LangDictQueryRequest request);

    /**
     * 添加语言字典
     *
     * @param request
     * @return
     */
    Long addOrUpdate(LangDictAddRequest request);

    /**
     * 添加一级字典
     *
     * @param request
     * @return
     */
    Long addOrUpdateParent(LangDictParentRequest request);

    /**
     * 删除语言字典
     *
     * @param id
     * @return
     */
    boolean delete(Long id);
}
