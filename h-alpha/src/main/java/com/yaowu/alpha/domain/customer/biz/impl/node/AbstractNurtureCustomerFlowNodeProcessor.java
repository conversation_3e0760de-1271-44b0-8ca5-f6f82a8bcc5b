package com.yaowu.alpha.domain.customer.biz.impl.node;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.freedom.objectstorage.utils.CloudUploadUtil;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.nacos.NurtureCustomerFlowConfig;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowNodeService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerService;
import com.yaowu.alpha.domain.llm.biz.ILLMAgentBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.CommonTranslateAgent;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.generator.content.AbstractGreetingContentGenerator;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.generator.content.GreetingContentGeneratorFactory;
import com.yaowu.alpha.enums.customer.*;
import com.yaowu.alpha.model.bo.customer.NurtureCustomerFlowNodeChatMessage;
import com.yaowu.alpha.model.bo.proxy.SendMsgTask;
import com.yaowu.alpha.model.bo.proxy.greet.WhatsAppGreetingMessage;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.utils.GreetingTaskSendTimeCalculator;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 培育客户流程节点处理器抽象基类
 * 使用模板方法模式，提供通用的节点创建和执行流程
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
public abstract class AbstractNurtureCustomerFlowNodeProcessor {

    /**
     * 默认消极节点代码
     */
    protected static final String DEFAULT_NEGATIVE_NODE_CODE = "7_DAY_NEGATIVE_NODE";

    @Autowired
    protected NurtureCustomerFlowConfig nurtureCustomerFlowConfig;
    
    @Autowired
    protected INurtureCustomerFlowNodeService nurtureCustomerFlowNodeService;
    
    @Autowired
    protected INurtureCustomerFlowService nurtureCustomerFlowService;
    
    @Autowired
    protected INurtureCustomerService nurtureCustomerService;
    
    @Autowired
    protected IProxyTaskBizService proxyTaskBizService;

    @Autowired
    protected IProxyAccountConfigBizService proxyAccountConfigBizService;
    
    @Autowired
    protected ILLMAgentBizService llmAgentBizService;

    @Autowired
    protected GreetingContentGeneratorFactory greetingContentGeneratorFactory;

    @Autowired
    protected CommonTranslateAgent commonTranslateAgent;

    @Autowired
    protected ApplicationContext applicationContext;

    /**
     * 判断是否支持指定的节点代码
     *
     * @param nodeCode 节点代码
     * @return 是否支持
     */
    public abstract boolean supports(String nodeCode);

    /**
     * 创建节点 - 模板方法
     * 定义了节点创建的标准流程，子类可重写钩子方法进行定制
     *
     * @param flow 流程信息
     */
    public final void createNode(NurtureCustomerFlow flow) {
        String nodeCode = getNodeCode();
        log.info("开始创建节点，flowId: {}, nodeCode: {}", flow.getId(), nodeCode);
        
        try {
            NurtureCustomerFlowNode node = buildFlowNode(flow, nodeCode);
            saveFlowNode(node);

            log.info("节点创建完成，flowId: {}, nodeCode: {}, templateCode: {}, executeTime: {}", 
                    flow.getId(), nodeCode, node.getTemplateCode(), node.getProxyTaskExecuteTime());
        } catch (Exception e) {
            log.error("创建节点失败，flowId: {}, nodeCode: {}", flow.getId(), nodeCode, e);
            throw e;
        }
    }

    /**
     * 执行节点 - 模板方法
     * 定义了节点执行的标准流程
     *
     * @param node 待执行的节点
     */
    public final void execute(NurtureCustomerFlowNode node) {
        log.info("开始执行节点，nodeId: {}, nodeCode: {}, templateCode: {}", 
                 node.getId(), node.getNodeCode(), node.getTemplateCode());

        // 获取流程信息和客户信息
        NurtureCustomerFlow flow = getFlowById(node.getFlowId());
        ProxyAccount proxyAccount = getProxyAccountByProxyId(flow.getSender());
        NurtureCustomer customer = getCustomerById(flow.getCustomerId());

        // 获取模板配置
        NurtureCustomerFlowConfig.MessageTemplateConfig templateConfig = getTemplateConfig(node.getTemplateCode());
        NurtureCustomerFlowConfig.MessageTemplateTenantConfig tenantConfig = getTenantConfig(templateConfig, flow.getTenantId());
        // 生成待发送消息内容
        NurtureCustomerFlowNodeChatMessage chatMessage = generateMessageContent(customer, flow, tenantConfig);
        
        // 生成待发送消息任务
        LocalDateTime sendTime = calculateSendTime(customer);
        List<Long> taskIds = createMessageTasks(proxyAccount, chatMessage, customer, flow, sendTime);

        // 更新节点状态为待回复
        updateNodePendingReply(
                node,
                taskIds,
                sendTime,
                JSONUtil.toJsonStr(tenantConfig),
                JSONUtil.toJsonStr(chatMessage)
        );
        log.info("节点执行完成，nodeId: {}, taskIds: {}, sendTime: {}",
                node.getId(), taskIds, sendTime);
    }

    /**
     * 统一处理客户回复
     * 根据不同回复类型执行相应的处理逻辑
     *
     * @param node 节点信息
     * @param replyType 回复类型
     * @param msgIds 回复消息ID列表（可为空）
     */
    public final void processReply(NurtureCustomerFlowNode node,
                                   NurtureCustomerReplyTypeEnum replyType,
                                   List<Long> msgIds) {
        log.info("开始处理客户回复，nodeId: {}, nodeCode: {}, replyType: {}",
                node.getId(), node.getNodeCode(), replyType.getValue());

        // 提前获取流程信息，避免子方法重复获取
        NurtureCustomerFlow flow = getFlowByNodeId(node.getId());

        // 调用内部统一处理方法
        processReplyInternal(node, flow, replyType, msgIds);

        log.info("客户回复处理完成，nodeId: {}, nodeCode: {}, replyType: {}",
                node.getId(), node.getNodeCode(), replyType.getValue());
    }

    /**
     * 获取当前处理器对应的节点代码
     *
     * @return 节点代码
     */
    protected abstract String getNodeCode();

    /**
     * 获取下一个节点代码
     * 子类需要根据业务逻辑实现具体的节点流转规则
     *
     * @return 下一个节点代码，如果没有下一个节点则返回null
     */
    protected abstract String nextNode();


    /**
     * 处理消极回复的特殊逻辑
     * 子类可以重写此方法实现特殊的消极回复处理逻辑
     * 默认实现：创建7天消极节点
     *
     * @param node 当前节点
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    protected void handleNegativeReplySpecialLogic(NurtureCustomerFlowNode node,
                                                   NurtureCustomerFlow flow,
                                                   List<Long> msgIds) {
        // 默认实现：创建7天消极节点
        createNextNode(flow, DEFAULT_NEGATIVE_NODE_CODE);
        log.info("消极回复处理：创建7天消极节点，nodeId: {}, flowId: {}", node.getId(), flow.getId());
    }

    /**
     * 结束流程并设置客户分类
     *
     * @param flow 流程信息
     * @param classificationType 客户分类类型
     */
    protected void finishFlowWithClassification(NurtureCustomerFlow flow,
                                                NurtureCustomerClassificationTypeEnum classificationType) {
        // 1. 更新流程状态为已结束，并设置客户分类
        flow.setFlowStatus(NurtureCustomerFlowStatusEnum.FINISHED);
        flow.setFlowFinishTime(LocalDateTime.now());
        flow.setCustomerClassification(classificationType);

        boolean flowUpdateResult = nurtureCustomerFlowService.updateById(flow);
        if (!flowUpdateResult) {
            throw new BusinessException("更新流程状态失败，flowId: " + flow.getId());
        }

        log.info("流程结束并设置客户分类成功，flowId: {}, customerId: {}, classificationType: {}",
                flow.getId(), flow.getCustomerId(), classificationType.getDesc());
    }

    /**
     * 钩子方法：自定义节点属性
     * 子类可重写此方法来定制特定节点的属性
     *
     * @param node 节点对象
     * @param nodeConfig 节点配置
     */
    protected void customizeNode(NurtureCustomerFlowNode node, NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig) {
        // 默认实现为空，子类可选择性重写
    }



    /**
     * 统一回复处理内部逻辑（模板方法）
     *
     * @param node 节点信息
     * @param flow 流程信息（已获取，避免重复查询）
     * @param replyType 回复类型
     * @param msgIds 回复消息ID列表
     */
    private void processReplyInternal(NurtureCustomerFlowNode node, 
                                    NurtureCustomerFlow flow, 
                                    NurtureCustomerReplyTypeEnum replyType, 
                                    List<Long> msgIds) {
        switch (replyType) {
            case NO_REPLY:
                handleNoReplyProcess(node, flow);
                break;
            case NEGATIVE_REPLY:
                handleNegativeReplyProcess(node, flow, msgIds);
                break;
            case POSITIVE_REPLY:
                handlePositiveReplyProcess(node, flow, msgIds);
                break;
            case VALID_INQUIRY:
                handleValidInquiryProcess(node, flow, msgIds);
                break;
            case EXPLICIT_REFUSAL:
                handleExplicitRefusalProcess(node, flow, msgIds);
                break;
            default:
                throw new BusinessException("不支持的回复类型: " + replyType.getValue());
        }
    }

    /**
     * 处理无回复场景
     * 
     * @param node 节点信息
     */
    private void handleNoReplyProcess(NurtureCustomerFlowNode node, NurtureCustomerFlow flow) {
        log.info("处理无回复，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
        
        // 1. 更新当前节点状态为已过期
        updateNodeStatus(node, NurtureCustomerFlowNodeStatusEnum.EXPIRED, 
                        NurtureCustomerReplyTypeEnum.NO_REPLY, null);
        
        // 2. 获取下一个节点
        String nextNodeCode = nextNode();
        if (StrUtil.isNotBlank(nextNodeCode)) {
            // 3. 创建下一个节点
            createNextNode(flow, nextNodeCode);
        } else {
            // 4. 没有下一个节点，结束流程
            finishFlowWithClassification(flow, NurtureCustomerClassificationTypeEnum.SILENT_POOL);
        }
    }

    /**
     * 处理消极回复
     *
     * @param node 节点信息
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    private void handleNegativeReplyProcess(NurtureCustomerFlowNode node,
                                          NurtureCustomerFlow flow, 
                                          List<Long> msgIds) {
        log.info("处理消极回复，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
        
        // 1. 更新当前节点状态为已完成
        updateNodeStatus(node,
                NurtureCustomerFlowNodeStatusEnum.COMPLETED,
                NurtureCustomerReplyTypeEnum.NEGATIVE_REPLY,
                msgIds
        );
        
        // 2. 调用子类的特殊处理逻辑（钩子方法）
        handleNegativeReplySpecialLogic(node, flow, msgIds);
    }

    /**
     * 处理积极回复
     *
     * @param node 节点信息
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    private void handlePositiveReplyProcess(NurtureCustomerFlowNode node, 
                                          NurtureCustomerFlow flow, 
                                          List<Long> msgIds) {
        log.info("处理积极回复，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
        
        // 1. 更新当前节点状态为已完成
        updateNodeStatus(node, NurtureCustomerFlowNodeStatusEnum.COMPLETED, 
                        NurtureCustomerReplyTypeEnum.POSITIVE_REPLY, msgIds);
        
        // 2. 设置客户为持续培育池，结束流程
        finishFlowWithClassification(flow, NurtureCustomerClassificationTypeEnum.CONTINUOUS_NURTURING_POOL);
    }

    /**
     * 处理有效询盘
     *
     * @param node 节点信息
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    private void handleValidInquiryProcess(NurtureCustomerFlowNode node, 
                                         NurtureCustomerFlow flow, 
                                         List<Long> msgIds) {
        log.info("处理有效询盘，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
        
        // 1. 更新当前节点状态为已完成
        updateNodeStatus(node, NurtureCustomerFlowNodeStatusEnum.COMPLETED, 
                        NurtureCustomerReplyTypeEnum.VALID_INQUIRY, msgIds);
        
        // 2. 设置客户为有效客户池，结束流程
        finishFlowWithClassification(flow, NurtureCustomerClassificationTypeEnum.VALID_CUSTOMER_POOL);
    }

    /**
     * 处理明确拒绝
     *
     * @param node 节点信息
     * @param flow 流程信息
     * @param msgIds 消息ID列表
     */
    private void handleExplicitRefusalProcess(NurtureCustomerFlowNode node, 
                                            NurtureCustomerFlow flow, 
                                            List<Long> msgIds) {
        log.info("处理明确拒绝，nodeId: {}, nodeCode: {}", node.getId(), node.getNodeCode());
        
        // 1. 更新当前节点状态为已完成
        updateNodeStatus(node, NurtureCustomerFlowNodeStatusEnum.COMPLETED, 
                        NurtureCustomerReplyTypeEnum.EXPLICIT_REFUSAL, msgIds);
        
        // 2. 设置客户为冷处理池，结束流程
        finishFlowWithClassification(flow, NurtureCustomerClassificationTypeEnum.COLD_PROCESSING_POOL);
    }

    /**
     * 翻译文本
     */
    private String translateText(String text, NurtureCustomer customer, NurtureCustomerFlow flow) {
        try {
            String country = customer.getCustomerCountry();
            if (StringUtil.isBlank(country)) {
                country = "美国";
            }
            return commonTranslateAgent.translateByUserCountry(flow.getTenantId(), flow.getSender(), customer.getCustomerContact(), text, country);
        } catch (Exception e) {
            log.error("翻译文本失败，text: {}", text, e);
        }
        return text;
    }

    /**
     * 根据代理id获取代理账号信息
     * @param sender
     * @return
     */
    private ProxyAccount getProxyAccountByProxyId(String sender) {
        ProxyAccount proxyAccount = proxyAccountConfigBizService.getOneByProxyId(sender);
        if (proxyAccount == null) {
            throw new BusinessException("未找到对应的代理账号");
        }
        return proxyAccount;
    }


    /**
     * 生成待发送消息内容
     * 根据配置类型判断是文本消息还是文件消息，调用相应的处理方法
     * 
     * @param customer 客户信息
     * @param flow 流程信息  
     * @param tenantConfig 租户配置
     * @return JSON格式的消息内容
     */
    private NurtureCustomerFlowNodeChatMessage generateMessageContent(NurtureCustomer customer,
                                          NurtureCustomerFlow flow,
                                          NurtureCustomerFlowConfig.MessageTemplateTenantConfig tenantConfig) {
        // 判断是文件消息还是文本消息
        if (tenantConfig.getFileConfig() != null) {
            // 处理文件消息
            return processFileTemplate(tenantConfig.getFileConfig(), customer, flow);
        }

        if (tenantConfig.getMessageConfig() != null) {
            // 处理文本消息
            return processTextTemplate(tenantConfig.getMessageConfig(), customer, flow);
        }
        throw new BusinessException("未找到有效的消息配置或文件配置");
    }

    /**
     * 创建消息任务
     * 解析消息内容并根据类型创建相应的发送任务
     * 
     * @param chatMessage 消息内容
     * @param customer 客户信息
     * @param flow 流程信息
     * @param sendTime 发送时间
     * @return 任务ID列表
     */
    private List<Long> createMessageTasks(ProxyAccount proxyAccount,
                                          NurtureCustomerFlowNodeChatMessage chatMessage,
                                          NurtureCustomer customer,
                                          NurtureCustomerFlow flow,
                                          LocalDateTime sendTime) {
        List<Long> taskIds = new ArrayList<>();
        try {
            // 创建文件消息任务
            if (StrUtil.isNotBlank(chatMessage.getFileUrl())) {
                // 文件消息 - 构建文件配置并创建任务
                taskIds.addAll(createFileMessageTasks(proxyAccount, chatMessage.getFileUrl(), chatMessage.getFileName(), chatMessage.getFileType(), customer, sendTime));

                log.info("创建文件消息任务完成，customerId: {}, taskCount: {}",
                        customer.getId(), taskIds.size());
            }

            // 创建消息任务
            if (StrUtil.isNotBlank(chatMessage.getMessage())) {
                // 文本消息 - 创建文本任务
                Long taskId = createTextMessageTask(proxyAccount, chatMessage.getMessage(), customer, sendTime);
                taskIds.add(taskId);
            }
        } catch (Exception e) {
            log.error("创建消息任务失败，chatMessage: {}", JSONUtil.toJsonStr(chatMessage), e);
            throw new BusinessException("创建消息任务失败: " + e.getMessage());
        }
        
        return taskIds;
    }

    /**
     * 获取流程信息
     */
    private NurtureCustomerFlow getFlowById(Long flowId) {
        NurtureCustomerFlow flow = nurtureCustomerFlowService.getById(flowId);
        if (flow == null) {
            throw new BusinessException("流程不存在，flowId: " + flowId);
        }
        return flow;
    }

    /**
     * 获取客户信息
     */
    private NurtureCustomer getCustomerById(Long customerId) {
        NurtureCustomer customer = nurtureCustomerService.getById(customerId);
        if (customer == null) {
            throw new BusinessException("客户不存在，customerId: " + customerId);
        }
        return customer;
    }

    /**
     * 获取模板配置
     */
    private NurtureCustomerFlowConfig.MessageTemplateConfig getTemplateConfig(String templateCode) {
        NurtureCustomerFlowConfig.MessageTemplateConfig templateConfig = 
            nurtureCustomerFlowConfig.getMessageTemplateConfig(templateCode);
        if (templateConfig == null) {
            throw new BusinessException("未找到模板配置: " + templateCode);
        }
        return templateConfig;
    }

    /**
     * 获取租户配置
     */
    private NurtureCustomerFlowConfig.MessageTemplateTenantConfig getTenantConfig(
            NurtureCustomerFlowConfig.MessageTemplateConfig templateConfig, Long tenantId) {
        NurtureCustomerFlowConfig.MessageTemplateTenantConfig tenantConfig = StreamUtil.of(templateConfig.getTenantConfig())
                .filter(config -> config.getTenantIds() != null && config.getTenantIds().contains(tenantId))
                .findFirst()
                .orElse(null);
        if (tenantConfig == null) {
            throw new BusinessException("未找到租户配置: tenantId=" + tenantId);
        }
        return tenantConfig;
    }

    /**
     * 计算发送时间
     */
    private LocalDateTime calculateSendTime(NurtureCustomer customer) {
        LocalDateTime sendTime = GreetingTaskSendTimeCalculator.calculateSendTime(customer.getCustomerCountry());
        if (sendTime == null) {
            log.warn("计算发送时间失败，使用当前时间作为发送时间，country: {}", customer.getCustomerCountry());
            sendTime = LocalDateTime.now();
        }

        return addRandomBuffer(sendTime);
    }

    /**
     * 为发送时间增加1-5分钟的随机buffer时间
     *
     * @param originalTime 原始发送时间
     * @return 增加随机buffer后的发送时间
     */
    private LocalDateTime addRandomBuffer(LocalDateTime originalTime) {
        // 生成1-5之间的随机整数（包含1和5）
        int randomMinutes = 1 + (int)(Math.random() * 5);
        return originalTime.plusMinutes(randomMinutes);
    }

    /**
     * 处理文件模板
     */
    private NurtureCustomerFlowNodeChatMessage processFileTemplate(NurtureCustomerFlowConfig.FileConfig fileConfig,
                                     NurtureCustomer customer, NurtureCustomerFlow flow) {
        NurtureCustomerFlowNodeChatMessage chatMessage = new NurtureCustomerFlowNodeChatMessage();
        chatMessage.setFileUrl(fileConfig.getFileUrl());
        chatMessage.setFileType(fileConfig.getFileType());

        String fileDesc = fileConfig.getFileDesc();
        if (StrUtil.isNotBlank(fileConfig.getFileName())) {
            String translatedFileName = translateText(fileConfig.getFileName(), customer, flow);
            chatMessage.setFileName(translatedFileName);
        }
        if (StrUtil.isNotBlank(fileDesc)) {
            String translatedFileDesc = translateText(fileDesc, customer, flow);
            chatMessage.setMessage(translatedFileDesc);
        }
        return chatMessage;
    }

    /**
     * 处理文本模板
     */
    private NurtureCustomerFlowNodeChatMessage processTextTemplate(NurtureCustomerFlowConfig.MessageConfig messageConfig,
                                       NurtureCustomer customer,
                                       NurtureCustomerFlow flow) {
        NurtureCustomerFlowNodeChatMessage chatMessage = new NurtureCustomerFlowNodeChatMessage();

        String llmExample = messageConfig.getLlmExample();
        AbstractGreetingContentGenerator generator = greetingContentGeneratorFactory.getContentGenerator(customer.getContactType());

        AbstractGreetingContentGenerator.GreetingCustomer greetingCustomer = AbstractGreetingContentGenerator.GreetingCustomer.builder()
                .country(customer.getCustomerCountry())
                .city(customer.getCustomerCity())
                .customerInfo(customer.getCustomerInfo())
                .build();
        String message = generator.generateContent(flow.getTenantId(), flow.getSender(), customer.getCustomerContact(), greetingCustomer, llmExample);
        WhatsAppGreetingMessage whatsAppGreetingMessage = JSONUtil.toBean(message, WhatsAppGreetingMessage.class);
        chatMessage.setMessage(whatsAppGreetingMessage.getContent());
        return chatMessage;
    }


    /**
     * 创建文件消息任务
     */
    private List<Long> createFileMessageTasks(ProxyAccount proxyAccount,
                                              String fileUrl,
                                              String fileName,
                                              NurtureCustomerMessageFileTypeEnum fileType,
                                              NurtureCustomer customer,
                                              LocalDateTime sendTime) {
        List<Long> taskIds = new ArrayList<>();
        try {
            // 文件加签
            String signedUrl = CloudUploadUtil.generateSignedUrl(fileUrl);

            SendMsgTask sendTask = new SendMsgTask();
            sendTask.setAccountConfigId(proxyAccount.getId());
            sendTask.setToUserId(customer.getCustomerContact());
            sendTask.setMsg(signedUrl);
            sendTask.setMediaFileName(fileName);
            sendTask.setExpectExecuteTime(sendTime);

            // 根据文件类型创建不同的任务
            if (NurtureCustomerMessageFileTypeEnum.FILE.equals(fileType)) {
                // 文件
                taskIds.addAll(proxyTaskBizService.createSendVideoFileMsgTask(sendTask));
            } else {
                // 图片
                taskIds.addAll(proxyTaskBizService.createSendImageMsgTask(sendTask));
            }
            log.info("创建文件消息任务成功，fileUrl: {}, sendTime: {}", fileUrl, sendTime);
        } catch (Exception e) {
            log.error("创建文件消息任务失败，fileUrl: {}", fileUrl, e);
            throw new BusinessException("创建文件消息任务失败: " + e.getMessage());
        }
        return taskIds;
    }

    /**
     * 创建文本消息任务
     */
    private Long createTextMessageTask(ProxyAccount proxyAccount,
                                       String messageContent,
                                       NurtureCustomer customer,
                                       LocalDateTime sendTime) {
        try {
            SendMsgTask sendTask = new SendMsgTask();
            sendTask.setAccountConfigId(proxyAccount.getId());
            sendTask.setToUserId(customer.getCustomerContact());
            sendTask.setMsg(messageContent);
            sendTask.setExpectExecuteTime(sendTime);
            
            Long taskId = proxyTaskBizService.createSendTextMsgTask(sendTask);
            
            log.info("创建文本消息任务成功，messageContent: {}, sendTime: {}, taskId: {}", 
                     messageContent, sendTime, taskId);
            
            return taskId;
        } catch (Exception e) {
            log.error("创建文本消息任务失败，messageContent: {}", messageContent, e);
            throw new BusinessException("创建文本消息任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新节点状态为待回复
     */
    private void updateNodePendingReply(NurtureCustomerFlowNode node,
                                        List<Long> taskIds,
                                        LocalDateTime sendTime,
                                        String templateConfigSnapshot,
                                        String messageContent) {
        // 获取节点配置
        NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig = getNodeConfig(node.getNodeCode());
        
        // 计算回复过期时间
        LocalDateTime replyExpireTime = calculateReplyExpireTime(nodeConfig, sendTime);
        
        node.setNodeStatus(NurtureCustomerFlowNodeStatusEnum.PENDING_REPLY);
        node.setProxyTaskIds(taskIds);
        node.setProxyTaskExecuteTime(sendTime);
        node.setReplyExpireTime(replyExpireTime);
        node.setMessageContent(messageContent);
        node.setTemplateConfigSnapshot(templateConfigSnapshot);
        
        nurtureCustomerFlowNodeService.updateById(node);
        log.info("已将节点状态更新为待回复，nodeId: {}, taskIds: {}, replyExpireTime: {}", 
                 node.getId(), taskIds, replyExpireTime);
    }


    /**
     * 根据节点代码获取节点配置
     *
     * @param nodeCode 节点代码
     * @return 节点配置
     */
    private NurtureCustomerFlowConfig.FlowNodeConfig getNodeConfig(String nodeCode) {
        return nurtureCustomerFlowConfig.getFlowNodeConfig(nodeCode);
    }

    /**
     * 计算节点的执行时间
     *
     * @param nodeConfig 节点配置
     * @return 执行时间
     */
    private LocalDateTime calculateExecuteTime(NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig) {
        LocalDateTime now = LocalDateTime.now();
        return addTimeByUnit(now, nodeConfig.getDelayTime(), nodeConfig.getDelayTimeUnit(), now);
    }

    /**
     * 计算回复过期时间
     *
     * @param nodeConfig 节点配置
     * @param executeTime 执行时间
     * @return 回复过期时间
     */
    private LocalDateTime calculateReplyExpireTime(NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig,
                                                     LocalDateTime executeTime) {
        return addTimeByUnit(executeTime, nodeConfig.getReplyTtl(), nodeConfig.getReplyTtlTimeUnit(), null);
    }

    /**
     * 根据时间单位为基准时间添加指定的时间量
     *
     * @param baseTime 基准时间
     * @param timeAmount 时间数量
     * @param timeUnit 时间单位
     * @param defaultValue 默认值（当时间数量或单位为空时返回）
     * @return 计算后的时间，如果参数无效则返回默认值
     */
    private LocalDateTime addTimeByUnit(LocalDateTime baseTime, 
                                       Integer timeAmount, 
                                       java.util.concurrent.TimeUnit timeUnit, 
                                       LocalDateTime defaultValue) {
        if (timeAmount == null || timeUnit == null) {
            return defaultValue;
        }
        
        return switch (timeUnit) {
            case SECONDS -> baseTime.plusSeconds(timeAmount);
            case MINUTES -> baseTime.plusMinutes(timeAmount);
            case HOURS -> baseTime.plusHours(timeAmount);
            case DAYS -> baseTime.plusDays(timeAmount);
            default -> defaultValue;
        };
    }


    /**
     * 构建流程节点
     *
     * @param flow 流程信息
     * @param nodeCode 节点代码
     * @return 流程节点
     */
    private NurtureCustomerFlowNode buildFlowNode(NurtureCustomerFlow flow, String nodeCode) {
        // 获取节点配置并验证
        NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig = validateAndGetNodeConfig(nodeCode);
        
        // 选择模板
        String templateCode = selectTemplateByWeight(nodeConfig.getTemplateConfig());
        if (templateCode == null) {
            throw new BusinessException("未找到可用的模板配置: " + nodeCode);
        }
        
        // 计算执行时间
        LocalDateTime executeTime = calculateExecuteTime(nodeConfig);
        
        // 创建节点并允许子类定制
        NurtureCustomerFlowNode node = createFlowNode(flow, nodeCode, templateCode, executeTime);
        customizeNode(node, nodeConfig);
        
        return node;
    }

    /**
     * 验证并获取节点配置
     *
     * @param nodeCode 节点代码
     * @return 节点配置
     */
    private NurtureCustomerFlowConfig.FlowNodeConfig validateAndGetNodeConfig(String nodeCode) {
        NurtureCustomerFlowConfig.FlowNodeConfig nodeConfig = getNodeConfig(nodeCode);
        if (nodeConfig == null) {
            throw new BusinessException("未找到节点配置: " + nodeCode);
        }
        return nodeConfig;
    }

    /**
     * 保存流程节点
     *
     * @param node 流程节点
     */
    private void saveFlowNode(NurtureCustomerFlowNode node) {
        boolean saved = nurtureCustomerFlowNodeService.save(node);
        if (!saved) {
            throw new BusinessException("创建流程节点失败");
        }
    }

    /**
     * 根据权重选择模板代码
     *
     * @param templateConfigs 模板配置列表
     * @return 选中的模板代码
     */
    private String selectTemplateByWeight(List<NurtureCustomerFlowConfig.FlowNodeTemplateConfig> templateConfigs) {
        if (CollUtil.isEmpty(templateConfigs)) {
            return null;
        }
        
        // 计算总权重
        int totalWeight = templateConfigs.stream()
                .mapToInt(config -> Optional.ofNullable(config.getWeight()).orElse(0))
                .sum();
                
        if (totalWeight <= 0) {
            // 如果总权重为0，随机选择一个
            return templateConfigs.get(RandomUtil.randomInt(templateConfigs.size())).getTemplateCode();
        }
        
        // 按权重随机选择
        int randomWeight = RandomUtil.randomInt(totalWeight);
        int currentWeight = 0;
        
        for (NurtureCustomerFlowConfig.FlowNodeTemplateConfig config : templateConfigs) {
            currentWeight += Optional.ofNullable(config.getWeight()).orElse(0);
            if (randomWeight < currentWeight) {
                return config.getTemplateCode();
            }
        }
        
        // 兜底：返回第一个模板
        return templateConfigs.get(0).getTemplateCode();
    }

    /**
     * 创建流程节点记录
     *
     * @param flow 流程信息
     * @param nodeCode 节点代码
     * @param templateCode 模板代码
     * @param executeTime 执行时间
     * @return 创建的节点记录
     */
    private NurtureCustomerFlowNode createFlowNode(NurtureCustomerFlow flow,
                                                     String nodeCode, 
                                                     String templateCode,
                                                     LocalDateTime executeTime) {
        NurtureCustomerFlowNode node = new NurtureCustomerFlowNode();
        node.setFlowId(flow.getId());
        node.setNodeCode(nodeCode);
        node.setTemplateCode(templateCode);
        node.setProxyTaskExecuteTime(executeTime);
        node.setNodeStatus(NurtureCustomerFlowNodeStatusEnum.PENDING);
        
        return node;
    }


    /**
     * 创建下一个节点
     *
     * @param flow 流程信息
     * @param nextNodeCode 下一个节点代码
     */
    private void createNextNode(NurtureCustomerFlow flow, String nextNodeCode) {
        // 获取下一个节点的处理器
        AbstractNurtureCustomerFlowNodeProcessor nextProcessor = findNodeProcessor(nextNodeCode);
        if (nextProcessor == null) {
            throw new BusinessException("未找到支持节点[" + nextNodeCode + "]的处理器");
        }
        
        // 使用处理器创建下一个节点
        nextProcessor.createNode(flow);
        log.info("创建下一个节点成功，flowId: {}, nextNodeCode: {}", flow.getId(), nextNodeCode);
    }

    /**
     * 查找支持指定节点代码的处理器
     *
     * @param nodeCode 节点代码
     * @return 节点处理器
     */
    private AbstractNurtureCustomerFlowNodeProcessor findNodeProcessor(String nodeCode) {
        // 通过Spring容器获取所有节点处理器
        return applicationContext.getBeansOfType(AbstractNurtureCustomerFlowNodeProcessor.class)
                .values()
                .stream()
                .filter(processor -> processor.supports(nodeCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据节点ID获取流程信息
     *
     * @param nodeId 节点ID
     * @return 流程信息
     */
    private NurtureCustomerFlow getFlowByNodeId(Long nodeId) {
        NurtureCustomerFlowNode nodeInfo = nurtureCustomerFlowNodeService.getById(nodeId);
        if (nodeInfo == null) {
            throw new BusinessException("节点不存在，nodeId: " + nodeId);
        }
        
        NurtureCustomerFlow flow = nurtureCustomerFlowService.getById(nodeInfo.getFlowId());
        if (flow == null) {
            throw new BusinessException("流程不存在，flowId: " + nodeInfo.getFlowId());
        }
        
        return flow;
    }

    /**
     * 更新节点状态
     *
     * @param node 节点信息
     * @param status 新状态
     * @param replyType 回复类型
     * @param msgIds 消息ID列表
     */
    private void updateNodeStatus(NurtureCustomerFlowNode node, 
                                NurtureCustomerFlowNodeStatusEnum status,
                                NurtureCustomerReplyTypeEnum replyType, 
                                List<Long> msgIds) {
        node.setNodeStatus(status);
        node.setReplyTypeCode(replyType.getValue());
        node.setReplyTypeDesc(replyType.getDesc());
        node.setReplyTime(LocalDateTime.now());
        
        if (CollUtil.isNotEmpty(msgIds)) {
            node.setMsgIds(msgIds);  // 直接设置List，不转JSON
        }
        
        boolean updateResult = nurtureCustomerFlowNodeService.updateById(node);
        if (!updateResult) {
            throw new BusinessException("更新节点状态失败，nodeId: " + node.getId());
        }
        
        log.info("节点状态更新成功，nodeId: {}, status: {}, replyType: {}", 
                 node.getId(), status.getDesc(), replyType.getDesc());
    }
}
