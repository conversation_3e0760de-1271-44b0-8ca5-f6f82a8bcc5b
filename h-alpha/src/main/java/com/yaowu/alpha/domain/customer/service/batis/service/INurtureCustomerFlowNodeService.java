package com.yaowu.alpha.domain.customer.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;

import java.util.List;

/**
 * <p>
 * 客户培育流程节点执行记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface INurtureCustomerFlowNodeService extends IService<NurtureCustomerFlowNode> {

    /**
     * 查询流程下可终止的节点（排除已过期、已完成的节点）
     * 
     * @param flowId 流程ID
     * @return 可终止的节点列表
     */
    List<NurtureCustomerFlowNode> listTerminableNodes(Long flowId);

    /**
     * 批量更新节点为终止状态
     * 
     * @param nodeIds 节点ID列表
     * @return 更新的节点数量
     */
    void batchUpdateNodesTermination(List<Long> nodeIds);

    /**
     * 查询待执行的节点列表
     * 
     * @param maxSize 最大查询数量
     * @return 待执行的节点列表
     */
    List<NurtureCustomerFlowNode> listPendingExecutionNodes(Long maxSize);

    /**
     * 查询待回复且已过期的节点列表
     * 
     * @param maxSize 最大查询数量
     * @return 待回复且已过期的节点列表
     */
    List<NurtureCustomerFlowNode> listExpiredPendingReplyNodes(Long maxSize);

    /**
     * 根据流程ID查询最新的待回复且未过期的节点
     * 
     * @param flowId 流程ID
     * @return 待回复节点，可能为null
     */
    NurtureCustomerFlowNode getActivePendingReplyNodeByFlowId(Long flowId);
}
