package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberChangeNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * 群人数变化
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportRoomMemberChangeProcessor implements INoticeActionProcessor<ReportRoomMemberChangeNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyGroupBizService proxyGroupBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportRoomMemberChangeNoticeRequestDTO;
    }


    @Override
    public NoticeBaseResponseVO process(ReportRoomMemberChangeNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }

        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-群人数变化-消息：{}", JacksonUtils.toJsonStr(request));
        // TODO 新建一个处理群信息任务
        // 提交查询群详细信息任务
        proxyGroupBizService.submitQueryGroupMembersDetailTask(Set.of(request.getRoom_wxid()));
        return NoticeBaseResponseVO.commonAck(request);
    }

}
