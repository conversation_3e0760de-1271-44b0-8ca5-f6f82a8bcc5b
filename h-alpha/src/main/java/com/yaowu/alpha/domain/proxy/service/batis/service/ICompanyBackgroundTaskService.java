package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.friend.CompanyBackgroundTaskPageDTO;
import com.yaowu.alpha.model.entity.proxy.CompanyBackgroundTask;

import java.util.List;

/**
 * 公司背调任务表服务接口
 * <AUTHOR>
 */
public interface ICompanyBackgroundTaskService extends IService<CompanyBackgroundTask> {

    CompanyBackgroundTask findLatestBackgroundTask(Long tenantId, String companyName,String industry);

    Page<CompanyBackgroundTask> pageByCondition(CompanyBackgroundTaskPageDTO dto);

    /**
     * 批量更新任务
     * @param tasks 需要更新的任务列表
     */
    void updateBatchTasks(List<CompanyBackgroundTask> tasks);
}