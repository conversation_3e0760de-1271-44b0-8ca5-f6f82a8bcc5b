package com.yaowu.alpha.domain.common.processor;

import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;

/**
 * 联系方式验证处理器接口
 * 参考Spring的supports机制设计
 * 
 * <AUTHOR>
 * 2025-01-27 15:30:00
 */
public interface IContactVerificationProcessor {

    /**
     * 判断是否支持指定的联系方式类型
     * 
     * @param contactType 联系方式类型
     * @return 是否支持
     */
    boolean supports(ContactVerificationContactTypeEnum contactType);

    /**
     * 执行验证处理
     * 
     * @param task 验证任务
     * @return 验证是否成功
     */
    void process(ContactVerificationTask task);
}