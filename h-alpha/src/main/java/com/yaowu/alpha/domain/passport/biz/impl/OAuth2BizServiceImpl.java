package com.yaowu.alpha.domain.passport.biz.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.passport.PassportClientDetailsConfig;
import com.yaowu.alpha.config.passport.PassportConfig;
import com.yaowu.alpha.domain.passport.biz.IOAuth2BizService;
import com.yaowu.alpha.model.bo.common.UserLoginInfoBO;
import com.yaowu.alpha.model.bo.passport.TokenBO;
import com.yaowu.alpha.model.vo.passport.TokenVO;
import com.yaowu.alpha.utils.contants.RedisConstants;
import com.yaowu.alpha.utils.passport.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Token服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
@Slf4j
@Service
public class OAuth2BizServiceImpl implements IOAuth2BizService {

    @Autowired
    private PassportClientDetailsConfig clientDetailsConfig;

    @Autowired
    private PassportConfig passportConfig;

    @Value("${server.port}")
    private String port;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 本地地址
     */
    public final static String LOCAL_HOST = "http://127.0.0.1:";

    /**
     * 认证地址
     */
    private final static String OAUTH_TOKEN_PATH = "/oauth2/token";

    private final static String TOKEN_SUCCESS_FLAG = "success";

    private static final String ERROR_KEY = "error";
    private static final String ERROR_DESCRIPTION_KEY = "error_description";

    @Override
    public TokenVO oauthTokenByPwd(Long userId, String username, String password) {
        HttpRequest post = HttpUtil.createPost(LOCAL_HOST + port + OAUTH_TOKEN_PATH);
        PassportClientDetailsConfig.ClientDetail clientDetail = getClientDetail();
        post.basicAuth(clientDetail.getClientId(), clientDetail.getClientSecret());
        post.form(new HashMap<>(7) {{
            put("grant_type", "custom");
            put("strategy", "password");
            put("username", userId.toString());
            put("password", password);
            put("scope", "openid");
            put("accessTokenValiditySeconds", clientDetail.getAccessTokenValiditySeconds());
            put("refreshTokenValiditySeconds", clientDetail.getRefreshTokenValiditySeconds());
        }});
        return getTokenVO(post, userId);
    }

    @Override
    public TokenVO oauthTokenBySms(Long userId, String phone, String smsCode) {
        HttpRequest post = HttpUtil.createPost(LOCAL_HOST + port + OAUTH_TOKEN_PATH);
        PassportClientDetailsConfig.ClientDetail clientDetail = getClientDetail();
        post.basicAuth(clientDetail.getClientId(), clientDetail.getClientSecret());
        post.form(new HashMap<>(8) {{
            put("grant_type", "custom");
            put("strategy", "sms_code");
            put("username", userId.toString());
            put("sms_code", smsCode);
            put("scope", "openid");
            put("accessTokenValiditySeconds", clientDetail.getAccessTokenValiditySeconds());
            put("refreshTokenValiditySeconds", clientDetail.getRefreshTokenValiditySeconds());
        }});
        return getTokenVO(post, userId);
    }

    @Override
    public TokenVO oauthTokenByEmail(Long userId, String email, String captcha) {
        HttpRequest post = HttpUtil.createPost(LOCAL_HOST + port + OAUTH_TOKEN_PATH);
        PassportClientDetailsConfig.ClientDetail clientDetail = getClientDetail();
        post.basicAuth(clientDetail.getClientId(), clientDetail.getClientSecret());
        post.form(new HashMap<>(7) {{
            put("grant_type", "custom");
            put("strategy", "email_code");
            put("username", userId.toString());
            put("email_code", captcha);
            put("scope", "openid");
            put("accessTokenValiditySeconds", clientDetail.getAccessTokenValiditySeconds());
            put("refreshTokenValiditySeconds", clientDetail.getRefreshTokenValiditySeconds());
        }});
        return getTokenVO(post, userId);
    }

    @Override
    public TokenVO refreshToken(String refreshToken) {
        log.info("刷新token，refreshToken：{}", refreshToken);
        TokenBO refreshTokenBO = TokenUtil.parseToken(refreshToken);
        HttpRequest post = HttpUtil.createPost(LOCAL_HOST + port + OAUTH_TOKEN_PATH);
        PassportClientDetailsConfig.ClientDetail clientDetail = getClientDetail();
        post.basicAuth(clientDetail.getClientId(), clientDetail.getClientSecret());
        Map<String, Object> form = new HashMap<>(6);
        form.put("grant_type", "custom");
        form.put("strategy", "refresh_token");
        form.put("refresh_token", refreshToken);
        form.put("username", refreshTokenBO.getUserId());
        form.put("accessTokenValiditySeconds", clientDetail.getAccessTokenValiditySeconds());
        form.put("refreshTokenValiditySeconds", clientDetail.getRefreshTokenValiditySeconds());
        form.put("scope", "openid");
        post.form(form);
        return getTokenVO(post, refreshTokenBO.getUserId());
    }

    private PassportClientDetailsConfig.ClientDetail getClientDetail() {
        return clientDetailsConfig.loadClientByClientId(passportConfig.getOauth2()
                .getClientId());
    }

    private TokenVO getTokenVO(HttpRequest post, Long userId) {
        HttpResponse httpResponse = post.execute();
        JSONObject json = parseResponse(httpResponse);
        if (!json.getBool(TOKEN_SUCCESS_FLAG, false)) {
            handleErrorResponse(json);
        }
        TokenVO tokenVO = JSONUtil.toBean(httpResponse.body(), TokenVO.class);
        TokenBO refreshTokenBO = TokenUtil.parseToken(tokenVO.getRefresh_token());
        // 设置token信息到redis中
        setUserLoginInfoToRedis(userId,
                tokenVO.getJti(), tokenVO.getExpires_in(),
                refreshTokenBO.getJti(), refreshTokenBO.getExp());
        return tokenVO;
    }

    /**
     * 设置token信息到redis中
     *
     * @param userId  用户id
     * @param jti     access token jti
     * @param jtiExp  access token jti过期时间（秒数）
     * @param rJti    refresh token jti
     * @param rJtiExp refresh token jti过期时间（秒数）
     */
    private void setUserLoginInfoToRedis(Long userId,
                                         String jti, Integer jtiExp,
                                         String rJti, Integer rJtiExp) {
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        // 把token jti 存在到redis中
        ops.set(RedisConstants.TOKEN_JTI_REDIS_PREFIX + jti, userId, jtiExp, TimeUnit.SECONDS);

        // 获取登录信息
        UserLoginInfoBO userLoginInfoBO = getUserLoginInfoBO(userId);
        List<UserLoginInfoBO.JtiStatus> jtiStatusList = userLoginInfoBO.getJtiStatusList();
        LocalDateTime now = LocalDateTime.now();
        // 新增jti状态
        addJtiStatus(jti, jtiExp, rJti, rJtiExp, now, jtiStatusList);

        // 再存入redis中
        ops.set(RedisConstants.LOGIN_INFO + userId, JSONUtil.toJsonStr(userLoginInfoBO));
    }


    /**
     * 添加jti状态
     */
    private void addJtiStatus(String jti, Integer jtiExp,
                              String rJti, Integer rJtiExp,
                              LocalDateTime now, List<UserLoginInfoBO.JtiStatus> jtiStatusList) {
        UserLoginInfoBO.JtiStatus jtiStatus = new UserLoginInfoBO.JtiStatus();
        jtiStatus.setLoginTime(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        jtiStatus.setJti(jti);
        jtiStatus.setOfflineTime("");
        jtiStatus.setOnline(true);
        jtiStatus.setExpTime(now.plusSeconds(jtiExp).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        jtiStatus.setRJti(rJti);
        jtiStatus.setRJtiExpTime(now.plusSeconds(rJtiExp).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")));
        jtiStatusList.add(jtiStatus);
    }

    /**
     * 获取用户登录信息
     *
     * @param userId
     * @return
     */
    private UserLoginInfoBO getUserLoginInfoBO(Long userId) {
        ValueOperations<String, Object> ops = redisTemplate.opsForValue();
        Object value = ops.get(RedisConstants.LOGIN_INFO + userId);
        UserLoginInfoBO userLoginInfoBO;
        if (value != null) {
            userLoginInfoBO = JSONUtil.toBean(value.toString(), UserLoginInfoBO.class);
        } else {
            userLoginInfoBO = new UserLoginInfoBO();
            userLoginInfoBO.setJtiStatusList(new ArrayList<>());
        }
        return userLoginInfoBO;
    }


    private JSONObject parseResponse(HttpResponse httpResponse) {
        String body = httpResponse.body();
        log.info("获取token结果：{}", body);
        return JSONUtil.parseObj(body);
    }

    private void handleErrorResponse(JSONObject json) {
        String error = json.getStr(ERROR_KEY);
        String errorDescription = json.getStr(ERROR_DESCRIPTION_KEY);
        log.error("获取token失败: error={}, error_description={}", error, errorDescription);
        throw new BusinessException(4003, StrUtil.isBlank(errorDescription) ? error : errorDescription);
    }

}
