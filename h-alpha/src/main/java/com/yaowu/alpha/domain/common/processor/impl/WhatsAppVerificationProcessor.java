package com.yaowu.alpha.domain.common.processor.impl;

import cn.hutool.core.collection.CollUtil;
import com.yaowu.alpha.config.nacos.WhatsAppVerificationConfig;
import com.yaowu.alpha.domain.common.processor.AbstractContactVerificationProcessor;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.enums.common.ContactVerificationContactTypeEnum;
import com.yaowu.alpha.enums.common.ContactVerificationStatusEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.ProxyAccountQueryDTO;
import com.yaowu.alpha.model.dto.proxy.control.SearchWhatsappContactTaskDTO;
import com.yaowu.alpha.model.entity.common.ContactVerificationTask;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * WhatsApp联系方式验证处理器
 * 继承抽象验证处理器，实现WhatsApp特定的验证逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-24 21:30:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WhatsAppVerificationProcessor extends AbstractContactVerificationProcessor {

    private final IProxyTaskBizService proxyTaskBizService;
    private final IProxyAccountConfigBizService proxyAccountConfigBizService;
    private final WhatsAppVerificationConfig whatsAppVerificationConfig;

    @Override
    public boolean supports(ContactVerificationContactTypeEnum contactType) {
        return ContactVerificationContactTypeEnum.WHATSAPP.equals(contactType);
    }

    /**
     * 执行WhatsApp验证逻辑
     * 创建异步验证任务，设置任务状态为验证中
     */
    @Override
    protected void doVerifyProcess(ContactVerificationTask task) {
        try {
            log.info("开始WhatsApp验证: contact={}", task.getContact());
            
            ProxyAccount proxyAccount = getAvailableWhatsAppAccount(whatsAppVerificationConfig.getProxyIds())
                    .orElseThrow(() -> new RuntimeException("没有可用的WhatsApp代理账号"));
            
            createWhatsAppVerificationTask(task, proxyAccount);
            updateTaskToVerifying(task);
        } catch (Exception e) {
            handleVerificationError(task, e);
        }
    }

    /**
     * 上报WhatsApp验证结果
     * 用于异步验证任务完成后更新验证状态
     *
     * @param contact 联系方式
     * @param success 验证是否成功
     * @param errorMsg 验证失败时的错误信息
     * @param isRegistered 是否注册
     * @param rawResult 原始验证结果数据
     */
    public void reportWhatsappVerificationResult(String contact, Boolean success, String errorMsg, Boolean isRegistered, String rawResult) {
        try {
            // 参数校验
            if (contact == null) {
                log.warn("WhatsApp验证结果上报失败: contact为空");
                return;
            }
            
            // 查询对应的验证任务
            List<ContactVerificationTask> tasks = contactVerificationTaskService
                    .listByContactsAndType(List.of(contact), ContactVerificationContactTypeEnum.WHATSAPP);

            if (CollUtil.isEmpty(tasks)) {
                log.warn("未找到WhatsApp验证任务: contact={}", contact);
                return;
            }

            // 更新最新的验证任务状态
            ContactVerificationTask task = tasks.get(0);
            updateTaskWithVerificationResult(task, success, errorMsg, isRegistered, rawResult);

            log.info("WhatsApp验证结果上报成功: contact={}, isRegistered={}", contact, isRegistered);
        } catch (Exception e) {
            log.error("WhatsApp验证结果上报失败: contact={}, error={}", contact, e.getMessage(), e);
            throw new RuntimeException("WhatsApp验证结果上报失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取可用的WhatsApp代理账号
     * 随机返回一个可用账号以实现负载均衡
     */
    private Optional<ProxyAccount> getAvailableWhatsAppAccount(List<String> accountProxyIds) {
        try {
            ProxyAccountQueryDTO queryDTO = ProxyAccountQueryDTO.builder()
                    .proxyIds(accountProxyIds)
                    .thirdType(ProxyThirdTypeEnum.WHAT_APP.getValue())
                    .onlineFlag(true)
                    .build();

            List<ProxyAccount> accounts = proxyAccountConfigBizService.listByCondition(queryDTO);

            List<ProxyAccount> availableAccounts = accounts.stream()
                    .filter(account -> !account.isForbidden())
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(availableAccounts)) {
                return Optional.empty();
            }

            // 随机选择一个可用账号
            int randomIndex = new Random().nextInt(availableAccounts.size());
            return Optional.of(availableAccounts.get(randomIndex));
        } catch (Exception e) {
            log.error("获取WhatsApp代理账号失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 根据验证结果更新任务状态
     */
    private void updateTaskWithVerificationResult(ContactVerificationTask task,
                                                  Boolean success,
                                                  String errorMsg,
                                                  Boolean isRegistered,
                                                  String rawResult) {
        ContactVerificationStatusEnum status = determineValidationStatus(success, isRegistered);
        
        task.setValidateTime(LocalDateTime.now())
                .setValidationRawResult(rawResult)
                .setErrorMessage(errorMsg)
                .setValidationStatus(status);

        contactVerificationTaskService.updateById(task);
        
        // 验证失败时发送企微通知
        if (ContactVerificationStatusEnum.FAILED.equals(status)) {
            sendFailureNotification(task, errorMsg);
        }
    }

    private static ContactVerificationStatusEnum determineValidationStatus(boolean success, boolean isRegistered) {
        return success ?
                (isRegistered ? ContactVerificationStatusEnum.VALID : ContactVerificationStatusEnum.INVALID) :
                ContactVerificationStatusEnum.FAILED;
    }

    /**
     * 创建WhatsApp验证任务
     */
    private void createWhatsAppVerificationTask(ContactVerificationTask task, ProxyAccount proxyAccount) {
        log.info("使用代理账号进行验证: accountId={}, proxyId={}, phone={}",
                proxyAccount.getId(), proxyAccount.getProxyId(), proxyAccount.getPhone());

        SearchWhatsappContactTaskDTO searchTaskDTO = buildSearchTaskDTO(task.getContact(), proxyAccount);
        proxyTaskBizService.createSearchWhatsappContactTask(searchTaskDTO);
    }

    /**
     * 构建搜索任务DTO
     */
    private SearchWhatsappContactTaskDTO buildSearchTaskDTO(String contact, ProxyAccount proxyAccount) {
        SearchWhatsappContactTaskDTO searchTaskDTO = new SearchWhatsappContactTaskDTO();
        searchTaskDTO.setTargetPhone(processPhoneNumber(contact));
        searchTaskDTO.setProxyAccountId(proxyAccount.getId());
        searchTaskDTO.setAgentPhone(proxyAccount.getPhone());
        return searchTaskDTO;
    }

    public String processPhoneNumber(String targetPhone) {
        if (targetPhone != null && targetPhone.startsWith("+")) {
            return targetPhone.substring(1);
        }
        return targetPhone;
    }

    /**
     * 更新任务状态为验证中
     */
    private void updateTaskToVerifying(ContactVerificationTask task) {
        task.setValidationStatus(ContactVerificationStatusEnum.VERIFYING);
        log.info("WhatsApp异步验证任务创建成功: contact={}", task.getContact());
    }

    /**
     * 处理验证异常
     */
    private void handleVerificationError(ContactVerificationTask task, Exception e) {
        log.error("WhatsApp验证异常: contact={}, error={}",
                task.getContact(), e.getMessage(), e);
        task.setValidationStatus(ContactVerificationStatusEnum.FAILED)
                .setErrorMessage(e.getMessage());
        
        // 验证异常时发送企微通知
        sendFailureNotification(task, e.getMessage());
    }

}