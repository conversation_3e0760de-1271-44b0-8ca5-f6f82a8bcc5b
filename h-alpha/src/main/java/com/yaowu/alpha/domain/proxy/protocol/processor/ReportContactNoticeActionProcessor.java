package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportContactNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 上报通讯录
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportContactNoticeActionProcessor implements INoticeActionProcessor<ReportContactNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyAccountFriendBizService friendBizService;

    private final IProxyGroupBizService groupBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportContactNoticeRequestDTO;
    }


    @Override
    public NoticeBaseResponseVO process(ReportContactNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return NoticeBaseResponseVO.commonAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-上报通讯录-消息：{}", JacksonUtils.toJsonStr(request));
        // 处理私聊好友
        friendBizService.updateContactsAsync(request, accountConfig);
        // 处理群信息
        groupBizService.updateContactsAsync(request, accountConfig);

        return NoticeBaseResponseVO.commonAck(request);
    }

}
