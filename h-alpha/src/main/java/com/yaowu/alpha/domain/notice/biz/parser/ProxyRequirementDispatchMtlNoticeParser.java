package com.yaowu.alpha.domain.notice.biz.parser;

import com.yaowu.alpha.domain.common.remote.INoticeRemoteBizService;
import com.yaowu.alpha.domain.notice.biz.event.ProxyRequirementDispatchMtlNoticeEvent;
import com.yaowu.alpha.domain.notice.support.parser.AbstractNoticeParser;
import com.yaowu.alpha.enums.common.NoticeTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.bo.common.NoticeParam;
import com.yaowu.alpha.model.bo.proxy.ProxyRentalClueCustomerRequirementData;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.utils.common.StringUtil;
import com.yaowu.alpha.utils.contants.SmsTemplateCodeConstant;
import com.yaowu.notice.constant.enums.ChannelEnum;
import com.yaowu.notice.model.vo.shortlink.RemoteShortLinkVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/26 16:30
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class ProxyRequirementDispatchMtlNoticeParser extends AbstractNoticeParser<ProxyRequirementDispatchMtlNoticeEvent> {

    /**
     * 小程序-我的需求页
     * 带有推广码的小程序短链
     */
    private static final String MY_REQUIREMENT_MINI_LINK = "https://wxaurl.cn/yzHmwd7iHVs";
    private static final Integer EXPIRE_DAYS = 7;


    private final INoticeRemoteBizService noticeRemoteBizService;

    @Override
    public NoticeParam parse(ProxyRequirementDispatchMtlNoticeEvent noticeEvent) {
        ProxyAccount proxyAccount = noticeEvent.getProxyAccount();
        ProxyRentalClueCustomerRequirementData requirementData = noticeEvent.getProxyRentalClueCustomerRequirementData();
        if (proxyAccount == null || !Objects.equals(proxyAccount.getThirdType(), ProxyThirdTypeEnum.PHONE.getValue())) {
            return null;
        }
        if (requirementData == null) {
            return null;
        }
        if (StringUtil.isBlank(requirementData.getPhone())) {
            log.info("ProxyRequirementDispatchMtlNoticeParser#parse 手机号为空，不发送短信");
            return null;
        }
        RemoteShortLinkVO shortLink = noticeRemoteBizService.generateShortLink(MY_REQUIREMENT_MINI_LINK, EXPIRE_DAYS);
        if (shortLink == null) {
            log.info("ProxyRequirementDispatchMtlNoticeParser#parse 生成短链失败，不发送短信");
            return null;
        }
        String templateCode = SmsTemplateCodeConstant.ALI_SMS_AGENT_MTL_DISPATCH_NOTICE_TEMPLATE;
        return NoticeParam.builder()
                .templateCode(templateCode)
                .templateParam("code", shortLink.getCode())
                .channel(NoticeTypeEnum.ALI_SMS, ChannelEnum.MACHINE_STAR_APPLETS)
                .phones(requirementData.getPhone())
                .build();
    }

}
