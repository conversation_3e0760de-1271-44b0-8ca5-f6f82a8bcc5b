package com.yaowu.alpha.domain.notice.support.event;

import org.springframework.context.ApplicationEvent;

import java.util.Objects;

/**
 * <AUTHOR>
 * Date: 2022/12/28
 * Time: 15:28
 */
public class NoticeEvent<T> extends ApplicationEvent {

    public NoticeEvent(T event) {
        super(event);
        Objects.requireNonNull(event);
    }

    @SuppressWarnings("unchecked")
    public T getData() {
        Object source = super.getSource();
        return (T) source;
    }

}
