package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.enums.user.UserBusinessAccountTypeEnum;
import com.yaowu.alpha.model.entity.user.UserBusinessAccount;

/**
 * <AUTHOR>
 * @date 2025/4/14-17:15
 */
public interface IUserBusinessAccountService extends IService<UserBusinessAccount> {

    /**
     * 根据业务id和账号类型获取用户账号
     *
     * @param bizId           业务id
     * @param userAccountType 账号类型
     * @return 用户账号
     */
    UserBusinessAccount getUserBusinessAccount(Long bizId, UserBusinessAccountTypeEnum userAccountType);
}
