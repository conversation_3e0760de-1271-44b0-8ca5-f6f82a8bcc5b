package com.yaowu.alpha.domain.payment.service.batis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.payment.PaymentOrderQueryDTO;
import com.yaowu.alpha.model.entity.payment.PaymentOrder;

/**
 * <AUTHOR>
 * @date 2025/4/7-19:54
 */
public interface IPaymentOrderService extends IService<PaymentOrder> {

    /**
     * 条件分页查询
     *
     * @param dto
     * @return
     */
    Page<PaymentOrder> pageByCondition(PaymentOrderQueryDTO dto);
}
