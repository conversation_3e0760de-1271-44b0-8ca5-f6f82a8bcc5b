package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.LogoutWhatsappAccountNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinLogoutWhatsappAccountRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolLogoutWhatsappAccountMapStruct;
import org.springframework.stereotype.Component;

/**
 * 登出WhatsApp账号适配器
 */
@Component
public class WetoolLogoutWhatsappAccountAdapter extends AbstractWetoolActionAdapter<WetoolWexinLogoutWhatsappAccountRequestDTO, LogoutWhatsappAccountNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.LOGOUT_WHATSAPP_ACCOUNT.equals(action);
    }

    @Override
    public LogoutWhatsappAccountNoticeRequestDTO transferRequest(WetoolWexinLogoutWhatsappAccountRequestDTO input) {
        return WetoolLogoutWhatsappAccountMapStruct.INSTANCE.toLogoutWhatsappAccountRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }
}
