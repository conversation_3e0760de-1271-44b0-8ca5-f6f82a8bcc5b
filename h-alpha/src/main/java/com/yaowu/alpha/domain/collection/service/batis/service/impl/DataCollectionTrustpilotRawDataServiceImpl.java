package com.yaowu.alpha.domain.collection.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.collection.service.batis.mapper.DataCollectionTrustpilotRawDataMapper;
import com.yaowu.alpha.domain.collection.service.batis.service.IDataCollectionTrustpilotRawDataService;
import com.yaowu.alpha.model.entity.collection.DataCollectionTrustpilotRawData;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Trustpilot数据采集原始数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Service
public class DataCollectionTrustpilotRawDataServiceImpl extends ServiceImpl<DataCollectionTrustpilotRawDataMapper, DataCollectionTrustpilotRawData> implements IDataCollectionTrustpilotRawDataService {

    @Override
    public Page<DataCollectionTrustpilotRawData> pageByCondition(Long taskId, String companyName, Integer aiAnalysisStatus, Page<DataCollectionTrustpilotRawData> page) {
        LambdaQueryWrapper<DataCollectionTrustpilotRawData> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        wrapper.eq(taskId != null, DataCollectionTrustpilotRawData::getTaskId, taskId)
               .like(StrUtil.isNotBlank(companyName), DataCollectionTrustpilotRawData::getCompanyName, companyName)
               .eq(aiAnalysisStatus != null, DataCollectionTrustpilotRawData::getAiAnalysisStatus, aiAnalysisStatus);
        
        // 排序：最新创建的在前
        wrapper.orderByDesc(DataCollectionTrustpilotRawData::getCreateTime);
        
        // 分页查询
        return page(page, wrapper);
    }

    @Override
    public List<DataCollectionTrustpilotRawData> listByTaskId(Long taskId) {
        LambdaQueryWrapper<DataCollectionTrustpilotRawData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionTrustpilotRawData::getTaskId, taskId)
               .orderByDesc(DataCollectionTrustpilotRawData::getCreateTime);
        return list(wrapper);
    }

    @Override
    public List<DataCollectionTrustpilotRawData> listByAiAnalysisStatus(Integer aiAnalysisStatus) {
        LambdaQueryWrapper<DataCollectionTrustpilotRawData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DataCollectionTrustpilotRawData::getAiAnalysisStatus, aiAnalysisStatus)
               .orderByDesc(DataCollectionTrustpilotRawData::getCreateTime);
        return list(wrapper);
    }

    @Override
    public boolean batchUpdateAiAnalysisStatus(List<Long> ids, Integer aiAnalysisStatus) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        LambdaUpdateWrapper<DataCollectionTrustpilotRawData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(DataCollectionTrustpilotRawData::getId, ids)
               .set(DataCollectionTrustpilotRawData::getAiAnalysisStatus, aiAnalysisStatus)
               .set(DataCollectionTrustpilotRawData::getAiAnalysisTime, LocalDateTime.now());
        return update(wrapper);
    }

    @Override
    public DataCollectionTrustpilotRawData getByTaskIdAndCompanyName(Long taskId, String companyName) {
        if (taskId == null || StrUtil.isBlank(companyName)) {
            return null;
        }
        List<DataCollectionTrustpilotRawData> list = this.lambdaQuery()
                .eq(DataCollectionTrustpilotRawData::getTaskId, taskId)
                .eq(DataCollectionTrustpilotRawData::getCompanyName, companyName)
                .list();
        return CollUtil.isNotEmpty(list) ? list.get(0) : null;
    }
}