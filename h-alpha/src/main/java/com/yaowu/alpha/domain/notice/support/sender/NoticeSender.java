package com.yaowu.alpha.domain.notice.support.sender;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.domain.common.remote.IJmcRemoteService;
import com.yaowu.alpha.model.bo.common.NoticeParam;
import com.yaowu.notice.model.dto.NotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/28
 */
@Component
@Slf4j
public class NoticeSender {
    @Autowired
    private IJmcRemoteService jmcRemoteService;

    public void send(NoticeParam param) {
        NotifyDto result;
        try {
            log.info("notice request: {}", JSONUtil.toJsonStr(param));
            result = jmcRemoteService.send(param);
            log.info("notice response:{}", JSONUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("notice error", e);
        }
    }
}
