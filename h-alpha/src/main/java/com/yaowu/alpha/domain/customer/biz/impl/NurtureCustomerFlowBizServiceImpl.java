package com.yaowu.alpha.domain.customer.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.freedom.web.exception.BusinessException;
import com.yaowu.alpha.config.nacos.NurtureCustomerFlowConfig;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowBizService;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowNodeBizService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowNodeService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerFlowService;
import com.yaowu.alpha.domain.customer.service.batis.service.INurtureCustomerService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.domain.proxy.control.biz.impl.agent.FriendInfoCompletionAgent;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.enums.customer.NurtureCustomerFlowStatusEnum;
import com.yaowu.alpha.enums.proxy.ProxyStatusEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.bo.customer.TenantContactTypePair;
import com.yaowu.alpha.model.dto.customer.FlowTerminateDTO;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerFlowBatchCreateDTO;
import com.yaowu.alpha.model.dto.proxy.control.ProxyAccountQueryDTO;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlow;
import com.yaowu.alpha.model.entity.customer.NurtureCustomerFlowNode;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;
import com.yaowu.alpha.utils.TransactionUtils;
import com.yaowu.alpha.utils.common.StreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 培育客户流程业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NurtureCustomerFlowBizServiceImpl implements INurtureCustomerFlowBizService {

    /**
     * Redis key前缀 - 发送人每日启动流程数量
     */
    private static final String REDIS_KEY_SENDER_DAILY_START_COUNT_FORMATTER = "nurture:flow:sender:daily:start:%s:%s";

    private final INurtureCustomerFlowService nurtureCustomerFlowService;
    private final INurtureCustomerService nurtureCustomerService;
    private final IProxyAccountConfigService proxyAccountConfigService;
    private final IProxyAccountFriendBizService proxyAccountFriendBizService;
    private final FriendInfoCompletionAgent friendInfoCompletionAgent;
    private final StringRedisTemplate redisTemplate;
    private final NurtureCustomerFlowConfig nurtureCustomerFlowConfig;
    private final INurtureCustomerFlowNodeBizService nurtureCustomerFlowNodeBizService;
    private final INurtureCustomerFlowNodeService nurtureCustomerFlowNodeService;
    private final TransactionUtils transactionUtils;

    /**
     * 批量创建培育流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateNurtureFlow(NurtureCustomerFlowBatchCreateDTO dto) {
        // 查询已存在的客户
        List<NurtureCustomer> existingCustomers = getExistingCustomers(dto.getCustomerIds());
        if (CollUtil.isEmpty(existingCustomers)) {
            throw new BusinessException("没有找到有效的培育客户");
        }
        
        // 检查未完成流程的客户
        Set<Long> customersWithUnfinishedFlow = nurtureCustomerFlowService.getCustomersWithUnfinishedFlow(dto.getCustomerIds());
        List<NurtureCustomer> validCustomers = filterValidCustomers(existingCustomers, customersWithUnfinishedFlow);
        // 获取预计开始时间，如果未提供则使用当前时间
        LocalDateTime expectedStartTime = dto.getExpectedStartTime() != null ? dto.getExpectedStartTime() : LocalDateTime.now();
        List<NurtureCustomerFlow> pendingSaveFlows = new ArrayList<>();
        for (NurtureCustomer customer : validCustomers) {
            NurtureCustomerFlow flow = createNurtureFlow(dto.getTenantId(), customer.getId(), customer.getContactType(), dto.getPriority(), expectedStartTime);
            pendingSaveFlows.add(flow);
        }
        // 保存流程
        nurtureCustomerFlowService.saveBatch(pendingSaveFlows);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateFlow(FlowTerminateDTO dto) {
        NurtureCustomerFlow flow = nurtureCustomerFlowService.getById(dto.getFlowId());
        if (flow == null) {
            throw new BusinessException("流程不存在");
        }
        if (flow.getFlowStatus() == NurtureCustomerFlowStatusEnum.TERMINATED) {
            throw new BusinessException("流程已终止");
        }
        if (flow.getFlowStatus() == NurtureCustomerFlowStatusEnum.FINISHED) {
            throw new BusinessException("流程已完成");
        }
        // 查询流程下可终止的节点
        List<NurtureCustomerFlowNode> terminableNodes = nurtureCustomerFlowNodeService.listTerminableNodes(dto.getFlowId());
        // 提取节点ID列表
        List<Long> nodeIds = StreamUtil.of(terminableNodes)
                .map(NurtureCustomerFlowNode::getId)
                .collect(java.util.stream.Collectors.toList());

        // 批量更新节点为终止状态
        nurtureCustomerFlowNodeService.batchUpdateNodesTermination(nodeIds);

        // 更新流程状态为终止
        flow.setFlowStatus(NurtureCustomerFlowStatusEnum.TERMINATED);
        flow.setFlowTerminationTime(LocalDateTime.now());
        nurtureCustomerFlowService.updateById(flow);
    }

    /**
     * 启动培育流程
     */
    @Override
    public void startFlow() {
        try {
            List<TenantContactTypePair> pendingPairs = getPendingTenantContactTypePairs();
            if (CollUtil.isEmpty(pendingPairs)) {
                log.info("没有未分配代理账号的待启动流程");
                return;
            }

            log.info("开始处理未分配代理账号的流程，租户联系方式组合数量: {}", pendingPairs.size());
            processTenantContactTypePairs(pendingPairs);
        } catch (Exception e) {
            log.error("执行培育流程启动任务失败", e);
        }
    }

    /**
     * 获取有待启动流程的租户和联系方式组合列表
     * 
     * @return 租户和联系方式组合列表
     */
    private List<TenantContactTypePair> getPendingTenantContactTypePairs() {
        return nurtureCustomerFlowService.listPendingTenantContactTypePairs();
    }

    /**
     * 处理所有租户和联系方式组合的流程
     * 
     * @param pairs 租户和联系方式组合列表
     */
    private void processTenantContactTypePairs(List<TenantContactTypePair> pairs) {
        for (TenantContactTypePair pair : pairs) {
            try {
                processSingleTenantContactType(pair.getTenantId(), pair.getContactType());
            } catch (Exception e) {
                log.error("处理租户[{}]联系方式类型[{}]的流程失败", pair.getTenantId(), pair.getContactType(), e);
            }
        }
    }

    /**
     * 处理单个租户指定联系方式类型的流程
     * 
     * @param tenantId 租户ID
     * @param contactType 联系方式类型
     */
    private void processSingleTenantContactType(Long tenantId, ProxyThirdTypeEnum contactType) {
        // 获取该租户指定联系方式的待启动流程
        List<NurtureCustomerFlow> pendingFlows = nurtureCustomerFlowService.listPendingFlowsByTenantAndContactType(tenantId, contactType, 100);
        if (CollUtil.isEmpty(pendingFlows)) {
            log.info("租户[{}]联系方式类型[{}]暂无待启动的培育流程", tenantId, contactType);
            return;
        }

        processContactTypeFlows(tenantId, contactType, pendingFlows);
    }

    /**
     * 处理指定联系方式类型的流程
     * 
     * @param tenantId 租户ID
     * @param contactType 联系方式类型
     * @param flows 流程列表
     */
    private void processContactTypeFlows(Long tenantId, ProxyThirdTypeEnum contactType, List<NurtureCustomerFlow> flows) {
        // 获取可用的代理账号
        List<ProxyAccount> availableAccounts = getAvailableProxyAccounts(tenantId, contactType.getVal());
        if (CollUtil.isEmpty(availableAccounts)) {
            log.warn("租户[{}]联系方式类型[{}]没有可用的代理账号", tenantId, contactType);
            return;
        }

        // 过滤出未达每日限额的代理账号
        List<ProxyAccount> usableAccounts = filterAccountsByDailyLimit(availableAccounts);
        if (CollUtil.isEmpty(usableAccounts)) {
            log.warn("租户[{}]联系方式类型[{}]的所有代理账号已达每日限额", tenantId, contactType);
            return;
        }

        // 分配代理账号并启动流程
        assignAccountsAndStartFlows(usableAccounts, flows);
    }

    /**
     * 过滤出未达每日限额的代理账号
     * 
     * @param accounts 代理账号列表
     * @return 未达限额的代理账号列表
     */
    private List<ProxyAccount> filterAccountsByDailyLimit(List<ProxyAccount> accounts) {
        return StreamUtil.of(accounts)
                .filter(account -> {
                    int startedCount = getSenderDailyStartedCount(account.getProxyId());
                    int remainingLimit = calculateRemainingLimit(account.getProxyId(), startedCount);
                    return remainingLimit > 0 && nurtureCustomerFlowConfig.isIncludeProxyId(account.getProxyId());
                })
                .collect(Collectors.toList());
    }

    /**
     * 分配代理账号并启动流程
     * 
     * @param accounts 可用代理账号列表
     * @param flows 待启动流程列表
     */
    private void assignAccountsAndStartFlows(List<ProxyAccount> accounts, List<NurtureCustomerFlow> flows) {
        AtomicInteger accountIndex = new AtomicInteger(0);
        
        for (NurtureCustomerFlow flow : flows) {
            try {
                // 轮询选择代理账号
                ProxyAccount selectedAccount = accounts.get(accountIndex.getAndIncrement() % accounts.size());
                
                // 检查该代理账号是否还有剩余限额
                int startedCount = getSenderDailyStartedCount(selectedAccount.getProxyId());
                int remainingLimit = calculateRemainingLimit(selectedAccount.getProxyId(), startedCount);
                
                if (remainingLimit <= 0) {
                    log.warn("代理账号[{}]已达每日限额，跳过流程[{}]", selectedAccount.getProxyId(), flow.getId());
                    continue;
                }
                
                // 分配代理账号并启动流程
                startSingleFlow(flow, selectedAccount);
                incrementSenderDailyStartCount(selectedAccount.getProxyId());
                
                log.info("成功分配代理账号并启动流程，flowId: {}, sender: {}", flow.getId(), selectedAccount.getProxyId());
            } catch (Exception e) {
                log.error("分配代理账号并启动流程失败，flowId: {}", flow.getId(), e);
                handleFlowError(flow, e);
            }
        }
    }

    /**
     * 获取发送人当日已启动的流程数量
     *
     * @param sender 发送人
     * @return 已启动的流程数量
     */
    private int getSenderDailyStartedCount(String sender) {
        String redisKey = buildSenderDailyStartCountKey(sender);
        String countStr = redisTemplate.opsForValue().get(redisKey);
        
        if (StrUtil.isBlank(countStr)) {
            return 0;
        }

        try {
            return Integer.parseInt(countStr);
        } catch (NumberFormatException e) {
            log.error("解析Redis中的启动计数失败，sender={}, value={}", sender, countStr, e);
            return 0;
        }
    }

    /**
     * 计算发送人剩余可启动的流程数量
     *
     * @param sender 发送人
     * @param startedCount 已启动数量
     * @return 剩余可启动数量
     */
    private int calculateRemainingLimit(String sender, int startedCount) {
        Integer dailyLimit = nurtureCustomerFlowConfig.getDailyLimit(sender);
        if (dailyLimit == null || dailyLimit <= 0) {
            log.warn("发送人[{}]的每日启动限制配置异常: {}", sender, dailyLimit);
            return 0;
        }
        
        int remaining = dailyLimit - startedCount;
        log.debug("发送人[{}]每日限制: {}, 已启动: {}, 剩余: {}", sender, dailyLimit, startedCount, remaining);
        return Math.max(0, remaining);
    }

    /**
     * 更新流程状态为执行中
     *
     * @param flow 培育流程
     */
    private void startSingleFlow(NurtureCustomerFlow flow, ProxyAccount selectedAccount) {
        // 获取客户信息
        NurtureCustomer customer = nurtureCustomerService.getById(flow.getCustomerId());
        if (customer == null) {
            throw new BusinessException("未找到客户信息，客户ID: " + flow.getCustomerId());
        }
        // 创建好友并补全好友信息
        Long friendId = createFriendIfNeeded(selectedAccount, customer);
        // 创建首个节点
        transactionUtils.execute(() -> {
            nurtureCustomerFlowNodeBizService.createFirstNode(flow);

            flow.setSender(selectedAccount.getProxyId());
            flow.setFriendId(friendId);
            flow.setFlowStatus(NurtureCustomerFlowStatusEnum.EXECUTING);
            flow.setFlowExecuteTime(LocalDateTime.now());
            nurtureCustomerFlowService.updateById(flow);
        });
    }
    
    /**
     * 创建好友记录并补全好友信息
     *
     * @param account 代理账号
     * @param customer 客户信息
     */
    private Long createFriendIfNeeded(ProxyAccount account, NurtureCustomer customer) {
        if (!needCreateFriend(customer.getContactType())) {
            log.debug("客户[{}]的联系方式类型不需要创建好友记录", customer.getId());
            return null;
        }

        try {
            // 获取或创建好友记录
            ProxyAccountFriend friend = proxyAccountFriendBizService.getOrCreate(account, customer.getCustomerContact(), customer.getCustomerName());
            // 补全好友信息
            friendInfoCompletionAgent.complete(account, friend, customer.getCustomerInfo());

            log.info("创建好友记录成功，sender：{}，receiver：{}, friendId: {}", 
                    account.getProxyId(), customer.getCustomerContact(), friend.getId());
            return friend.getId();
        } catch (Exception e) {
            log.error("创建好友记录失败，sender：{}，receiver：{}", account.getProxyId(), customer.getCustomerContact(), e);
            throw e;
        }
    }
    
    /**
     * 判断是否需要创建好友记录
     */
    private boolean needCreateFriend(ProxyThirdTypeEnum contactType) {
        if (contactType == null) {
            return false;
        }
        return !Objects.equals(contactType.getValue(), ProxyThirdTypeEnum.OTHER_EMAIL.getValue());
    }
    


    /**
     * 增加发送人每日启动计数
     *
     * @param sender 发送人
     */
    private void incrementSenderDailyStartCount(String sender) {
        String redisKey = buildSenderDailyStartCountKey(sender);
        
        redisTemplate.opsForValue().increment(redisKey);
        redisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
    }

    /**
     * 构建发送人每日启动计数的Redis键
     *
     * @param sender 发送人
     * @return Redis键
     */
    private String buildSenderDailyStartCountKey(String sender) {
        String today = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
        return String.format(REDIS_KEY_SENDER_DAILY_START_COUNT_FORMATTER, sender, today);
    }

    /**
     * 处理流程启动异常
     *
     * @param flow 培育流程
     * @param exception 异常信息
     */
    private void handleFlowError(NurtureCustomerFlow flow, Exception exception) {
        try {
            flow.setFlowStatus(NurtureCustomerFlowStatusEnum.ERROR);
            flow.setFlowErrorMsg(StrUtil.sub(exception.getMessage(), 0, 2000));
            
            nurtureCustomerFlowService.updateById(flow);
            log.info("已将培育流程状态更新为ERROR，flowId: {}", flow.getId());
        } catch (Exception e) {
            log.error("更新培育流程错误状态失败，flowId: {}", flow.getId(), e);
        }
    }

    /**
     * 查询已存在的客户
     */
    private List<NurtureCustomer> getExistingCustomers(List<Long> customerIds) {
        return nurtureCustomerService.listByIds(customerIds);
    }

    /**
     * 过滤有效的客户（排除已有未完成流程的客户）
     */
    private List<NurtureCustomer> filterValidCustomers(List<NurtureCustomer> existingCustomers, Set<Long> customersWithUnfinishedFlow) {
        return StreamUtil.of(existingCustomers)
                .filter(customer -> !customersWithUnfinishedFlow.contains(customer.getId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取可用的代理账号
     */
    private List<ProxyAccount> getAvailableProxyAccounts(Long tenantId, Integer contactType) {
        ProxyAccountQueryDTO queryDto = new ProxyAccountQueryDTO();
        queryDto.setTenantId(tenantId);
        queryDto.setThirdType(contactType);
        queryDto.setOnlineFlag(true);
        queryDto.setAccountStatuses(CollUtil.newHashSet(ProxyStatusEnum.AGENT.getValue(), ProxyStatusEnum.MANUAL.getValue()));
        
        return proxyAccountConfigService.listByCondition(queryDto)
                .stream()
                .filter(account -> Boolean.TRUE.equals(account.getOnlineFlag()))
                .collect(Collectors.toList());
    }

    /**
     * 创建培育流程实体
     */
    private NurtureCustomerFlow createNurtureFlow(Long tenantId,
                                                  Long customerId,
                                                  ProxyThirdTypeEnum contactType,
                                                  Integer priority,
                                                  LocalDateTime expectedStartTime) {
        NurtureCustomerFlow flow = new NurtureCustomerFlow();
        flow.setTenantId(tenantId);
        flow.setCustomerId(customerId);
        flow.setCustomerContactType(contactType);
        flow.setPriority(priority);
        flow.setFlowStartTime(expectedStartTime);
        // 待执行
        flow.setFlowStatus(NurtureCustomerFlowStatusEnum.PENDING);
        return flow;
    }
}
