package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.config.wetool.WetoolConfig;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.LoginNoticeRequestDTO;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.LoginAckVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 账号登录
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LoginNoticeActionProcessor implements INoticeActionProcessor<LoginNoticeRequestDTO, LoginAckVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final WetoolConfig wetoolConfig;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof LoginNoticeRequestDTO;
    }


    @Override
    public LoginAckVO process(LoginNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return LoginAckVO.notReport(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-账号登录-消息：{}", JacksonUtils.toJsonStr(request));
        // 登录成功返回需要任务的数据
        return LoginAckVO.success(wetoolConfig.getUploadUrl());
    }

}
