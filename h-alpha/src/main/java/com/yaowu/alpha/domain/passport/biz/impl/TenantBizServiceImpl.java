package com.yaowu.alpha.domain.passport.biz.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.yaowu.alpha.domain.passport.biz.ITenantBizService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/24-15:00
 */
@Service
public class TenantBizServiceImpl implements ITenantBizService {

    /**
     * 暂未实现，留着后面扩展，现在就是需要使用租户id生成的逻辑
     *
     * @param tenantName
     * @return
     */
    @Override
    public Long createTenant(String tenantName) {
        return IdWorker.getId();
    }
}
