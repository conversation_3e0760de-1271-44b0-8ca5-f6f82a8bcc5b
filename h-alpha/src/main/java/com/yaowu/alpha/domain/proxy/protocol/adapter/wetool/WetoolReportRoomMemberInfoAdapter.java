package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;
import com.yaowu.alpha.model.dto.proxy.control.ReportRoomMemberInfoNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinReportRoomMemberInfoDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolResponseData;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import org.springframework.stereotype.Component;

/**
 * 群成员信息详情
 * <AUTHOR>
 */
@Component
public class WetoolReportRoomMemberInfoAdapter extends AbstractWetoolActionAdapter<WetoolWexinReportRoomMemberInfoDTO, ReportRoomMemberInfoNoticeRequestDTO, NoticeBaseResponseVO, WetoolResponseData> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.REPORT_ROOM_MEMBER_INFO.equals(action);
    }

    @Override
    public ReportRoomMemberInfoNoticeRequestDTO transferRequest(WetoolWexinReportRoomMemberInfoDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toReportRoomMemberInfoNoticeRequestDTO(input);
    }

    @Override
    public WetoolResponseData transferResponse(NoticeBaseResponseVO output) {
        WetoolResponseData responseVO = new WetoolResponseData();
        return super.transferSuccessResponse(responseVO, null);
    }

}
