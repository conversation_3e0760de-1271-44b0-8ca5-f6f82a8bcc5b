package com.yaowu.alpha.domain.message.service.batis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.message.service.batis.mapper.MessageDeliveryRecordMapper;
import com.yaowu.alpha.domain.message.service.batis.service.IMessageDeliveryRecordService;
import com.yaowu.alpha.model.entity.message.MessageDeliveryRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息投递记录 Service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@Service
public class MessageDeliveryRecordServiceImpl
        extends ServiceImpl<MessageDeliveryRecordMapper, MessageDeliveryRecord>
        implements IMessageDeliveryRecordService {

} 