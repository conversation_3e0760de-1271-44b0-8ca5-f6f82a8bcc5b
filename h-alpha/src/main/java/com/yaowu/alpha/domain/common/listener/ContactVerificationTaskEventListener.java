package com.yaowu.alpha.domain.common.listener;

import com.yaowu.alpha.domain.common.biz.IContactVerificationTaskBizService;
import com.yaowu.alpha.model.event.common.ContactVerificationTaskCreatedEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 联系方式验证任务事件监听器
 * 监听任务创建事件，异步触发验证处理
 * 
 * <AUTHOR>
 * @since 2025-01-24 20:30:00
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ContactVerificationTaskEventListener {

    private final IContactVerificationTaskBizService contactVerificationTaskBizService;
    
    /**
     * 默认批次大小
     */
    private static final int DEFAULT_BATCH_SIZE = 50;

    /**
     * 监听联系方式验证任务创建事件
     * 异步触发验证任务处理
     *
     * @param event 任务创建事件
     */
    @Async("commonExecutor")
    @EventListener
    public void handleContactVerificationTaskCreated(ContactVerificationTaskCreatedEvent event) {
        try {
            log.info("接收到联系方式验证任务创建事件: 新任务={}, 更新任务={}, 总数={}, 批次ID={}",
                    event.getNewTaskCount(), event.getUpdatedTaskCount(),
                    event.getTotalContactCount(), event.getBatchId());

            // 检查是否有需要处理的任务
            if (!event.hasTasksToProcess()) {
                log.info("没有需要处理的验证任务，跳过处理");
                return;
            }
            // 异步触发验证任务处理
            int processedCount = contactVerificationTaskBizService.processVerificationTasks(DEFAULT_BATCH_SIZE);

            log.info("联系方式验证任务处理完成: 批次ID={}, 处理数量={}", event.getBatchId(), processedCount);

        } catch (Exception e) {
            log.error("处理联系方式验证任务创建事件异常: 批次ID={}, 错误信息={}",
                     event.getBatchId(), e.getMessage(), e);
        }
    }
}