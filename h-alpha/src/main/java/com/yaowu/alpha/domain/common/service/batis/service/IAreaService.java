package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.common.Area;

import java.util.List;

/**
 * 地区服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IAreaService extends IService<Area> {

    /**
     * 查询指定国家下的所有城市
     * 通过两级子查询：先查国家下的直接子区域（州/省），再查这些子区域下的城市
     *
     * @param countryAreaId 国家区域ID
     * @return 城市列表
     */
    List<Area> getCitiesByCountryAreaId(Long countryAreaId);

    /**
     * 根据国家查询州省列表
     *
     * @param countryAreaId 国家区域ID
     * @return 州省列表
     */
    List<Area> getStatesByCountryAreaId(Long countryAreaId);

    /**
     * 根据州省查询城市列表
     *
     * @param stateAreaId 州省区域ID
     * @return 城市列表
     */
    List<Area> getCitiesByStateAreaId(Long stateAreaId);

    /**
     * 根据父区域ID查询直接子区域列表（通用方法）
     *
     * @param parentAreaId 父区域ID
     * @return 子区域列表
     */
    List<Area> getChildrenByParentAreaId(Long parentAreaId);
}
