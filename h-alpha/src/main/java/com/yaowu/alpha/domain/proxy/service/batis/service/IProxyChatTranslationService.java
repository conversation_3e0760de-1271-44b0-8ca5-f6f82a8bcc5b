package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyChatMessageTranslation;

import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 智能体聊天消息翻译快照表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface IProxyChatTranslationService extends IService<ProxyChatMessageTranslation> {

    Map<Long,ProxyChatMessageTranslation> listByMessageIds(Set<Long> messageIds);


    ProxyChatMessageTranslation findMessageTranslations(Long messageId, Integer targetLanguageType);

    ProxyChatMessageTranslation findMessageTranslation(String md5Content, Integer targetLanguageType);
}
