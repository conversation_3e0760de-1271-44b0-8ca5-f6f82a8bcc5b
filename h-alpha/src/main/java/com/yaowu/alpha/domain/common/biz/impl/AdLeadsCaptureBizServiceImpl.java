package com.yaowu.alpha.domain.common.biz.impl;

import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.domain.common.biz.IAdLeadsCaptureBizService;
import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.alpha.model.dto.common.SubmitAdLeadsCaptureDTO;
import com.yaowu.alpha.utils.common.DateUtil;
import com.yaowu.alpha.utils.common.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.yaowu.alpha.enums.common.AdLeadsCaptureSourceEnum.GOOGLE_ADS;

/**
 * <AUTHOR>
 * @date 2023/8/9 16:31
 */
@Service
public class AdLeadsCaptureBizServiceImpl implements IAdLeadsCaptureBizService {

    @Autowired
    private IWarnNoticeBizService warnNoticeBizService;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public void submit(SubmitAdLeadsCaptureDTO dto) {
        String roobotMsg = buildWeChatRoobotMsg(dto);
        warnNoticeBizService.sendWechat(commonConfig.getNumberStaffAdLeadsCaptureBotKey(), roobotMsg);
        return;
    }

    /**
     * 构建微信机器人消息格式
     *
     * @param dto 留资信息DTO
     * @return 格式化的消息字符串
     */
    private String buildWeChatRoobotMsg(SubmitAdLeadsCaptureDTO dto) {
        if (dto == null) {
            return "[广告留资通知] 留资信息为空";
        }

        return new StringBuilder()
                .append(buildMessageHeader())
                .append(buildCustomerInfo(dto))
                .append(buildRequestDescription(dto))
                .append(buildSourceInfo(dto))
                .toString();
    }

    /**
     * 构建消息头部
     */
    private String buildMessageHeader() {
        return "[广告留资通知]\n\n" +
               "时间：" + DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss") + "\n\n";
    }

    /**
     * 构建客户信息部分
     */
    private String buildCustomerInfo(SubmitAdLeadsCaptureDTO dto) {
        StringBuilder info = new StringBuilder()
                .append("姓名：")
                .append(StringUtil.defaultIfBlank(dto.getName(), "未提供"))
                .append("\n");

        // 根据联系方式类型显示不同的标签
        String contactType = "联系方式";

        info.append(contactType)
            .append("：")
            .append(StringUtil.defaultIfBlank(dto.getContact(), "未提供"))
            .append("\n\n");

        return info.toString();
    }

    /**
     * 构建需求描述部分
     */
    private String buildRequestDescription(SubmitAdLeadsCaptureDTO dto) {
        String description = StringUtil.isNotBlank(dto.getRequestDescription()) ?
                dto.getRequestDescription() : "客户未提供需求描述";

        return "需求描述：\n" + description;
    }

    /**
     * 构建来源信息部分
     */
    private String buildSourceInfo(SubmitAdLeadsCaptureDTO dto) {
        String source = Objects.nonNull(dto.getSource()) ?
                dto.getSource().getDesc() : GOOGLE_ADS.getDesc();

        return "\n\n来源：" + source;
    }
}
