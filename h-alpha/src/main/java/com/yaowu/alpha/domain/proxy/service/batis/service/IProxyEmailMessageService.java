package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.ProxyEmailMessage;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代理邮箱消息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/27
 */
public interface IProxyEmailMessageService extends IService<ProxyEmailMessage> {

    ProxyEmailMessage getByMessageId(String messageId);
}
