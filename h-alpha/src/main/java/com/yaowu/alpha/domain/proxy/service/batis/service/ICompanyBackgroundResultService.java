package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.proxy.CompanyBackgroundResult;

/**
 * 公司背调结果表服务接口
 * <AUTHOR>
 */
public interface ICompanyBackgroundResultService extends IService<CompanyBackgroundResult> {

    CompanyBackgroundResult findBackgroundResult(Long tenantId, String companyName, String industry);


    CompanyBackgroundResult findBackResultByTaskId(Long id);
}