package com.yaowu.alpha.domain.customer.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.dto.customer.NurtureCustomerCreateDTO;
import com.yaowu.alpha.model.entity.customer.NurtureCustomer;

import java.util.List;

/**
 * <p>
 * 客户培育池 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface INurtureCustomerService extends IService<NurtureCustomer> {

    /**
     * 新增培育客户
     *
     * @param dto 新增培育客户请求参数
     * @return 新增成功的客户实体
     */
    NurtureCustomer createNurtureCustomer(NurtureCustomerCreateDTO dto);

    /**
     * 检查联系方式是否已存在
     *
     * @param customerContact 客户联系方式
     * @return true-存在，false-不存在
     */
    boolean existsByCustomerContact(String customerContact);

    /**
     * 批量查询客户
     * 
     * @param customerIds 客户ID列表
     * @return 客户列表
     */
    List<NurtureCustomer> listByIds(List<Long> customerIds);
}
