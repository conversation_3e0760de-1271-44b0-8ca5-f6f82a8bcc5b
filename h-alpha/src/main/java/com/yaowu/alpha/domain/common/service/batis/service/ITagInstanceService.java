package com.yaowu.alpha.domain.common.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.model.entity.common.Tag;
import com.yaowu.alpha.model.entity.common.TagInstance;

import java.util.List;

/**
 * <p>
 * 标签实例 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface ITagInstanceService extends IService<TagInstance> {

    /**
     * 根据标签ID和实例ID查询标签实例
     * @param tagId 标签ID
     * @param instanceId 实例ID
     * @return 标签实例
     */
    TagInstance findByTagIdAndInstanceId(Long tagId, String instanceId);

    /**
     * 根据标签ID查询实例ID列表
     * @param tagId 标签ID
     * @return 实例ID列表
     */
    List<String> listInstanceIdsByTagId(Long tagId);

    /**
     * 批量获取多个标签的所有实例ID
     * @param tagIds 标签ID列表
     * @return 所有实例ID列表，不去重
     */
    List<String> listAllInstanceIdsByTagIds(List<Long> tagIds);
    
    /**
     * 根据标签ID列表批量查询标签实例
     * @param tagIds 标签ID列表
     * @return 标签实例列表
     */
    List<TagInstance> listByTagIds(List<Long> tagIds);
    
    /**
     * 根据实例ID查询标签列表
     * @param instanceId 实例ID
     * @param businessCode 业务编码
     * @return 标签列表
     */
    List<Tag> listTagsByInstanceId(String instanceId, String businessCode);
}
