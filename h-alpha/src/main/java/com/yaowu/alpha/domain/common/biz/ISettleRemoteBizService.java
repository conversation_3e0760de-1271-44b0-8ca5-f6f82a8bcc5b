package com.yaowu.alpha.domain.common.biz;

import com.yaowu.settle.api.model.dto.settle.RemoteWithdrawDTO;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteSubmitThirdPartyPaymentDTO;
import com.yaowu.settle.api.model.mtl.dto.aggregation.RemoteThirdPartyPaymentRecordQueryDTO;
import com.yaowu.settle.api.model.mtl.dto.withdrawal.RemoteWithdrawalRecordQueryDTO;
import com.yaowu.settle.api.model.mtl.vo.aggregation.RemoteSubmitThirdPartyPaymentVO;
import com.yaowu.settle.api.model.mtl.vo.aggregation.RemoteThirdPartyPaymentRecordVO;
import com.yaowu.settle.api.model.mtl.vo.withdrawal.RemoteWithdrawalRecordVO;
import com.yaowu.settle.api.model.vo.settle.RemoteValidateWithdrawalVO;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/9 16:31
 */
@Validated
public interface ISettleRemoteBizService {

    /**
     * 提交三方支付
     * @param dto
     * @return
     */
    RemoteSubmitThirdPartyPaymentVO submitPayment(RemoteSubmitThirdPartyPaymentDTO dto);

    RemoteThirdPartyPaymentRecordVO getPreSubmitPaymentRecordById(Long id);

    RemoteThirdPartyPaymentRecordVO getPreSubmitPaymentRecord(RemoteThirdPartyPaymentRecordQueryDTO dto);
    List<RemoteWithdrawalRecordVO> listWithdrawalRecords(RemoteWithdrawalRecordQueryDTO dto);

}
