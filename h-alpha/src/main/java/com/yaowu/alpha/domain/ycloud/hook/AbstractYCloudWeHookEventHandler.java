package com.yaowu.alpha.domain.ycloud.hook;

import cn.hutool.json.JSONUtil;
import com.yaowu.alpha.utils.reflection.TypeCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Type;

/**
 * YCloud Webhook事件处理器抽象类
 *
 * @param <T> 事件类型
 */
@Slf4j
public abstract class AbstractYCloudWeHookEventHandler<T> {

    /**
     * 判断是否支持处理该类型的事件
     *
     * @param eventType 事件类型
     * @return 是否支持
     */
    public abstract boolean support(String eventType);

    /**
     * 处理webhook事件
     *
     * @param payload 事件载荷JSON字符串
     */
    public void process(String payload) {
        if (!StringUtils.hasText(payload)) {
            log.warn("Payload is empty, skip processing");
            return;
        }
        try {
            Type[] clzType = TypeCache.getActualTypeArguments(this.getClass());
            // 解析事件载荷为具体事件类型
            T event = JSONUtil.toBean(payload, clzType[0], true);
            // 处理具体事件
            handleEvent(event);
        } catch (Exception e) {
            log.error("Failed to process webhook event: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理具体事件的方法，由子类实现
     *
     * @param event 事件对象
     */
    protected abstract void handleEvent(T event);
}
