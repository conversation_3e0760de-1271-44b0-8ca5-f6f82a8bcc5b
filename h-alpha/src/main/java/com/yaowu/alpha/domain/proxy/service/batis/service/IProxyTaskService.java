package com.yaowu.alpha.domain.proxy.service.batis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yaowu.alpha.enums.proxy.TaskTypeEnum;
import com.yaowu.alpha.model.entity.proxy.ProxyTask;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代理下发任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IProxyTaskService extends IService<ProxyTask> {

    /**
     * 查询一个待处理的任务，根据处理优先级及时间顺序取最早一条
     */
    ProxyTask selectOnePendingTask(Long accountId, Set<Integer> unSupportTaskType);

    /**
     * 查询多个待处理的任务，根据处理优先级及时间顺序取最早一条
     * @param accountIds
     * @param unSupportTaskTypes
     * @return
     */
    List<ProxyTask> listPendingTasks(Set<Long> accountIds, Set<Integer> unSupportTaskTypes);

    /**
     * 查询任务
     */
    List<ProxyTask> listByTypeAndStatus(Long accountId, TaskTypeEnum taskType, Integer taskStatus, Integer limit);

    /**
     *
     * @param taskIds
     * @return
     */
    Long countWaitingTasksByIds(List<Long> taskIds);

    List<ProxyTask> listExpiredTasks(Long limit);

    /**
     * 查询一个whatsApp待处理的任务，根据处理优先级及时间顺序取最早一条
     */
    ProxyTask selectCreateWhatsAppAccountPendingTask();

    /**
     * 根据任务类型查询一个待执行的任务
     * 
     * @param taskType 任务类型
     * @return 代理任务，如果没有则返回null
     */
    ProxyTask selectOnePendingTaskByType(TaskTypeEnum taskType);

    /**
     * 查询一个待执行的采集任务控制指令任务
     * @param collectionTaskId 采集任务ID
     * @return 代理任务，如果没有则返回null
     */
    ProxyTask selectOnePendingDataCollectionControlInstruction(Long collectionTaskId);
}
