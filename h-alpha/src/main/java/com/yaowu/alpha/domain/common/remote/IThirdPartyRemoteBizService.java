package com.yaowu.alpha.domain.common.remote;

import com.genlian.thirdparty.api.model.vo.dict.ThirdPartyDictListVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteAreaAddressModelVO;
import com.genlian.thirdparty.api.model.vo.lbs.RemoteLbsAddressVO;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/16 13:49
 */
public interface IThirdPartyRemoteBizService {

    RemoteLbsAddressVO searchAddress(String lng, String lat, Integer radius);

    RemoteAreaAddressModelVO searchGeoAddressByAddressText(String address);


    /**
     * 查询字典 (字典code-字典名称)
     */
    Map<String,String> getSourceCodeToNameDictName(String dictType);

    List<ThirdPartyDictListVO> getDictList(@NotNull(message = "字典类型标志不可为空") String dictType);
}
