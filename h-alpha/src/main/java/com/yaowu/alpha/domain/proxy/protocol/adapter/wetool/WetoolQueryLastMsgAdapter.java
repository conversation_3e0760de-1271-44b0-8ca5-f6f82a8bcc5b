package com.yaowu.alpha.domain.proxy.protocol.adapter.wetool;

import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.QueryLastMsgRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.BaseWetoolRequest;
import com.yaowu.alpha.model.dto.proxy.protocol.wetool.WetoolWexinQueryLastMsgRequestDTO;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.model.vo.proxy.QueryLastMsgVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinPullTaskAckResponseVO;
import com.yaowu.alpha.model.vo.proxy.wetool.WetoolWexinQueryLastMsgResponseVO;
import com.yaowu.alpha.utils.contants.proxy.WetoolAckConstants;
import com.yaowu.alpha.utils.contants.proxy.WetoolActionConstants;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolWexinPullTaskMapStruct;
import org.springframework.stereotype.Component;

/**
 * 查询最新一条消息转换处理器
 */
@Component
public class WetoolQueryLastMsgAdapter extends AbstractWetoolActionAdapter<WetoolWexinQueryLastMsgRequestDTO, QueryLastMsgRequestDTO, QueryLastMsgVO, WetoolWexinQueryLastMsgResponseVO> {

    @Override
    protected Boolean supportAction(String action) {
        return WetoolActionConstants.QUERY_LAST_MSG.equals(action);
    }

    @Override
    public QueryLastMsgRequestDTO transferRequest(WetoolWexinQueryLastMsgRequestDTO input) {
        return WetoolCommonMapStruct.INSTANCE.toQueryLastMsgRequestDTO(input);
    }

    @Override
    public WetoolWexinQueryLastMsgResponseVO transferResponse(QueryLastMsgVO output) {
        WetoolWexinQueryLastMsgResponseVO responseVO = WetoolCommonMapStruct.INSTANCE.toWetoolWexinQueryLastMsgResponseVO(output);
        responseVO.setAck_type(WetoolAckConstants.COMMON_ACK);
        responseVO.setError_code(0);
        responseVO.setError_reason("");
        return responseVO;
    }
}
