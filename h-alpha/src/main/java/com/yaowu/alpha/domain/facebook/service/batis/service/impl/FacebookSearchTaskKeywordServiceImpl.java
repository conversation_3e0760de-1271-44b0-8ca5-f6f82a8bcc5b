package com.yaowu.alpha.domain.facebook.service.batis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yaowu.alpha.domain.facebook.service.batis.mapper.FacebookSearchTaskKeywordMapper;
import com.yaowu.alpha.domain.facebook.service.batis.service.FacebookSearchTaskKeywordService;
import com.yaowu.alpha.model.entity.facebook.FacebookSearchTaskKeyword;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Facebook搜索任务关键字 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class FacebookSearchTaskKeywordServiceImpl extends ServiceImpl<FacebookSearchTaskKeywordMapper, FacebookSearchTaskKeyword> implements FacebookSearchTaskKeywordService {

    @Override
    public List<FacebookSearchTaskKeyword> likeByKeyword(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().like(FacebookSearchTaskKeyword::getKeyword, keyword).list();
    }

    @Override
    public List<Long> listIdsByLikeByKeyword(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new ArrayList<>();
        }
        List<FacebookSearchTaskKeyword> list = this.lambdaQuery().like(FacebookSearchTaskKeyword::getKeyword, keyword).list();
        return list.stream().map(FacebookSearchTaskKeyword::getId).toList();
    }

    @Override
    public Map<Long, List<String>> getKeywordsMap(List<Long> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }
        List<FacebookSearchTaskKeyword> list = this.lambdaQuery().in(FacebookSearchTaskKeyword::getTaskId, taskIds).list();
        Map<Long, List<FacebookSearchTaskKeyword>> map = list.stream().collect(Collectors.groupingBy(FacebookSearchTaskKeyword::getTaskId));
        Map<Long, List<String>> resultMap = new HashMap<>();
        map.forEach((k, v) -> {
            resultMap.put(k, v.stream().map(FacebookSearchTaskKeyword::getKeyword).toList());
        });
        return resultMap;
    }

    @Override
    public Map<Long, FacebookSearchTaskKeyword> getKeywordMap(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        List<FacebookSearchTaskKeyword> keywordList = this.listByIds(ids);
        return keywordList.stream().collect(Collectors.toMap(FacebookSearchTaskKeyword::getId, Function.identity()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean delByTaskId(Long taskId) {
        List<FacebookSearchTaskKeyword> list = this.lambdaQuery().eq(FacebookSearchTaskKeyword::getTaskId, taskId).list();
        if (CollUtil.isEmpty(list)) {
            return true;
        }
        return this.removeByIds(list);
    }
}