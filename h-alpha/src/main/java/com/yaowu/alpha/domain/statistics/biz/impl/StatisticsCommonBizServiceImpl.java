package com.yaowu.alpha.domain.statistics.biz.impl;

import cn.hutool.core.util.StrUtil;
import com.yaowu.alpha.config.tenant.TenantIdValueServiceImpl;
import com.yaowu.alpha.domain.statistics.biz.IStatisticsCommonBizService;
import com.yaowu.alpha.domain.statistics.service.IStatisticsCommonService;
import com.yaowu.alpha.enums.common.OrgTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.statistics.StatisticsCommonDTO;
import com.yaowu.alpha.model.vo.statistics.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class StatisticsCommonBizServiceImpl implements IStatisticsCommonBizService {

    @Autowired
    private IStatisticsCommonService statisticsCommonService;

    @Autowired
    private TenantIdValueServiceImpl tenantIdValueService;

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public List<StatisticDataItemVO> getEmailTotalStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        EmailStatisticsVO emailStatisticsVO = statisticsCommonService.getEmailStatistics(dto);
        List<StatisticDataItemVO> dataList = new ArrayList<>();
        emailStatisticsVO = emailStatisticsVO == null ? new EmailStatisticsVO() : emailStatisticsVO;
        dataList.add(StatisticDataItemVO.builder().itemName("总数").itemCount(
                emailStatisticsVO.getPendingCount()
                        + emailStatisticsVO.getSentCount()
                        + emailStatisticsVO.getDeliveredCount()
                        + emailStatisticsVO.getOpenedCount()
                        + emailStatisticsVO.getClickedCount()
                        + emailStatisticsVO.getRepliedCount()
                        + emailStatisticsVO.getComplainedCount()
                        + emailStatisticsVO.getBouncedCount()
        ).build());
        dataList.add(StatisticDataItemVO.builder().itemName("待发送").itemCount(emailStatisticsVO.getPendingCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已发送").itemCount(emailStatisticsVO.getSentCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已送达").itemCount(emailStatisticsVO.getDeliveredCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已打开").itemCount(emailStatisticsVO.getOpenedCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已点击").itemCount(emailStatisticsVO.getClickedCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已回复").itemCount(emailStatisticsVO.getRepliedCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("被投诉").itemCount(emailStatisticsVO.getComplainedCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已退回").itemCount(emailStatisticsVO.getBouncedCount()).build());
        return dataList;
    }

    @Override
    public List<StatisticsDailyVO> getEmailDailyStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<LocalDate> dates = getDateRanges(dto);
        List<DailyEmailStatisticsVO> dailyEmailStatistics = statisticsCommonService.getDailyEmailStatistics(dto);
        List<StatisticsDailyVO> result = new ArrayList<>();
        Map<LocalDate, DailyEmailStatisticsVO> dailyMap = dailyEmailStatistics.stream().collect(Collectors.toMap(DailyEmailStatisticsVO::getEventDate, Function.identity()));
        for (LocalDate date : dates) {
            StatisticsDailyVO vo = new StatisticsDailyVO();
            vo.setDate(date.format(DATE_FORMATTER));
            List<StatisticDataItemVO> dataList = new ArrayList<>();
            vo.setDataList(dataList);
            DailyEmailStatisticsVO emailStatisticsVO = dailyMap.get(date);
            emailStatisticsVO = emailStatisticsVO == null ? new DailyEmailStatisticsVO() : emailStatisticsVO;
            dataList.add(StatisticDataItemVO.builder().itemName("已发送").itemCount(emailStatisticsVO.getSentCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("已送达").itemCount(emailStatisticsVO.getDeliveredCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("已点击").itemCount(emailStatisticsVO.getClickedCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("已打开").itemCount(emailStatisticsVO.getOpenedCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("已回复").itemCount(emailStatisticsVO.getRepliedCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("被投诉").itemCount(emailStatisticsVO.getComplainedCount()).build());
            dataList.add(StatisticDataItemVO.builder().itemName("已退回").itemCount(emailStatisticsVO.getBouncedCount()).build());
            result.add(vo);
        }
        return result;
    }


    @Override
    public List<StatisticDataGroupVO> getChatTotalStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<StatisticDataGroupVO> result = new ArrayList<>();
        List<ChatStatisticsVO> chatStatistics = statisticsCommonService.getChatStatistics(dto);
        chatStatistics = chatStatistics.stream().filter(d -> d.getThirdType() != null).toList();
        Map<Integer, ChatStatisticsVO> thirdTypeChatMap = chatStatistics.stream().collect(Collectors.toMap(ChatStatisticsVO::getThirdType, Function.identity()));
        List<ChatStatisticsVO> chatReplyStatistics = statisticsCommonService.getChatReplyStatistics(dto);
        Map<Integer, ChatStatisticsVO> thirdTypeChatReplyMap = chatReplyStatistics.stream().collect(Collectors.toMap(ChatStatisticsVO::getThirdType, Function.identity()));

        ChatStatisticsVO whatsApp = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.WHAT_APP.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO whatsAppReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.WHAT_APP.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> whatsAppDataList = new ArrayList<>();
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(whatsApp.getConversationCount()).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(whatsAppReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("WhatsApp")
                .dataList(whatsAppDataList)
                .build());

        ChatStatisticsVO wechat = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.WECHAT.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO wechatReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.WECHAT.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> wechatDataList = new ArrayList<>();
        wechatDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(wechat.getConversationCount()).build());
        wechatDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(wechatReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("微信")
                .dataList(wechatDataList)
                .build());

        ChatStatisticsVO entWechat = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.ENTERPRISE_WECHAT.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO entWechatReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.ENTERPRISE_WECHAT.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> entWechatDataList = new ArrayList<>();
        entWechatDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(entWechat.getConversationCount()).build());
        entWechatDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(entWechatReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("企微")
                .dataList(entWechatDataList)
                .build());

        ChatStatisticsVO tiktokShop = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.TIKTOK_SHOP.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO tiktokShopReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.TIKTOK_SHOP.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> tiktokShopDataList = new ArrayList<>();
        tiktokShopDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(tiktokShop.getConversationCount()).build());
        tiktokShopDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(tiktokShopReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("TikTok Shop")
                .dataList(tiktokShopDataList)
                .build());

        ChatStatisticsVO tiktok = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.TIKTOK.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO tiktokReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.TIKTOK.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> tiktokDataList = new ArrayList<>();
        tiktokDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(tiktok.getConversationCount()).build());
        tiktokDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(tiktokReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("TikTok")
                .dataList(tiktokDataList)
                .build());

        ChatStatisticsVO instagram = thirdTypeChatMap.getOrDefault(ProxyThirdTypeEnum.INSTAGRAM.getValue(), new ChatStatisticsVO());
        ChatStatisticsVO instagramReply = thirdTypeChatReplyMap.getOrDefault(ProxyThirdTypeEnum.INSTAGRAM.getValue(), new ChatStatisticsVO());
        List<StatisticDataItemVO> instagramDataList = new ArrayList<>();
        instagramDataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(instagram.getConversationCount()).build());
        instagramDataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(instagramReply.getConversationCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName("Instagram")
                .dataList(instagramDataList)
                .build());
        return result;
    }

    @Override
    public List<StatisticsDailyVO> getChatDailyStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<LocalDate> dates = getDateRanges(dto);
        List<StatisticsDailyVO> result = new ArrayList<>();
        List<DailyChatStatisticsVO> dailyChatStatistics = statisticsCommonService.getDailyChatStatistics(dto);
        dailyChatStatistics = dailyChatStatistics.stream().filter(d -> d.getThirdType() != null).toList();
        Map<LocalDate, List<DailyChatStatisticsVO>> dailyChatMap = dailyChatStatistics.stream().collect(Collectors.groupingBy(DailyChatStatisticsVO::getChatDate));
        List<DailyChatStatisticsVO> dailyChatReplyStatistics = statisticsCommonService.getDailyChatReplyStatistics(dto);
        Map<LocalDate, List<DailyChatStatisticsVO>> dailyChatReplyMap = dailyChatReplyStatistics.stream().collect(Collectors.groupingBy(DailyChatStatisticsVO::getChatDate));
        Integer thirdType = dto.getThirdType();
        thirdType = thirdType == null ? ProxyThirdTypeEnum.WHAT_APP.getValue() : thirdType;
        for (LocalDate date : dates) {
            StatisticsDailyVO vo = new StatisticsDailyVO();
            vo.setDate(date.format(DATE_FORMATTER));
            List<StatisticDataItemVO> dataList = new ArrayList<>();
            vo.setDataList(dataList);
            List<DailyChatStatisticsVO> dailyChatList = dailyChatMap.getOrDefault(date, new ArrayList<>());
            List<DailyChatStatisticsVO> dailyChatReplyList = dailyChatReplyMap.getOrDefault(date, new ArrayList<>());

            Map<Integer, DailyChatStatisticsVO> thirdTypeChatMap = dailyChatList.stream().collect(Collectors.toMap(DailyChatStatisticsVO::getThirdType, Function.identity()));
            Map<Integer, DailyChatStatisticsVO> thirdTypeChatReplyMap = dailyChatReplyList.stream().collect(Collectors.toMap(DailyChatStatisticsVO::getThirdType, Function.identity()));

            DailyChatStatisticsVO chat = thirdTypeChatMap.getOrDefault(thirdType, new DailyChatStatisticsVO());
            DailyChatStatisticsVO chatReply = thirdTypeChatReplyMap.getOrDefault(thirdType, new DailyChatStatisticsVO());
            dataList.add(StatisticDataItemVO.builder().itemName("对话数").itemCount(chat != null ? chat.getCustomerCount() : 0L).build());
            dataList.add(StatisticDataItemVO.builder().itemName("回复数").itemCount(chatReply != null ? chatReply.getCustomerCount() : 0L).build());

            result.add(vo);
        }
        return result;
    }

    @Override
    public List<StatisticDataGroupVO> getGreetingTotalStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<StatisticDataGroupVO> result = new ArrayList<>();
        List<GreetingTaskStatisticsVO> greetingTaskStatistics = statisticsCommonService.getGreetingTaskStatistics(dto);
        Map<Integer, GreetingTaskStatisticsVO> thirdTypeMap = greetingTaskStatistics.stream().collect(Collectors.toMap(GreetingTaskStatisticsVO::getThirdType, Function.identity()));
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.WHAT_APP, result, "WhatsApp");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.WECHAT, result, "微信");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.ENTERPRISE_WECHAT, result, "企微");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.OTHER_EMAIL, result, "邮箱");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.TIKTOK_SHOP, result, "TikTok Shop");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.TIKTOK, result, "TikTok");
        buildThirdTypeGreetingGroup(thirdTypeMap, ProxyThirdTypeEnum.INSTAGRAM, result, "Instagram");
        return result;
    }

    private static void buildThirdTypeGreetingGroup(Map<Integer, GreetingTaskStatisticsVO> thirdTypeMap,
                                                    ProxyThirdTypeEnum thirdType,
                                                    List<StatisticDataGroupVO> result,
                                                    String groupName) {
        GreetingTaskStatisticsVO vo = thirdTypeMap.getOrDefault(thirdType.getValue(), new GreetingTaskStatisticsVO());
        List<StatisticDataItemVO> dataList = new ArrayList<>();
        dataList.add(StatisticDataItemVO.builder().itemName("总数").itemCount(vo.getTotalCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("待执行").itemCount(vo.getPendingCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("已执行").itemCount(vo.getExecutedCount()).build());
        dataList.add(StatisticDataItemVO.builder().itemName("执行失败").itemCount(vo.getFailedCount()).build());
        result.add(StatisticDataGroupVO.builder()
                .groupName(groupName)
                .dataList(dataList)
                .build());
    }

    @Override
    public List<StatisticsDailyVO> getGreetingDailyStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<LocalDate> dateRanges = getDateRanges(dto);
        List<StatisticsDailyVO> result = new ArrayList<>();
        List<DailyGreetingTaskStatisticsVO> statistics = statisticsCommonService.getDailyGreetingTaskStatistics(dto);
        Map<LocalDate, List<DailyGreetingTaskStatisticsVO>> dateMap = statistics.stream().collect(Collectors.groupingBy(DailyGreetingTaskStatisticsVO::getTaskDate));
        Integer thirdType = dto.getThirdType();
        thirdType = thirdType == null ? ProxyThirdTypeEnum.WHAT_APP.getValue() : thirdType;
        for (LocalDate date : dateRanges) {
            StatisticsDailyVO vo = new StatisticsDailyVO();
            vo.setDate(date.format(DATE_FORMATTER));
            List<StatisticDataItemVO> dataList = new ArrayList<>();
            vo.setDataList(dataList);
            List<DailyGreetingTaskStatisticsVO> list = dateMap.getOrDefault(date, new ArrayList<>());
            Map<Integer, DailyGreetingTaskStatisticsVO> thirdTypeMap = list.stream().collect(Collectors.toMap(DailyGreetingTaskStatisticsVO::getThirdType, Function.identity()));

            DailyGreetingTaskStatisticsVO statisticsVO = thirdTypeMap.getOrDefault(thirdType, new DailyGreetingTaskStatisticsVO());
            dataList.add(StatisticDataItemVO.builder().itemName("执行数").itemCount(statisticsVO != null ? statisticsVO.getExecutedCount() : 0L).build());

            result.add(vo);
        }
        return result;
    }

    @Override
    public List<StatisticDataGroupVO> getNurtureCustomerFlowTotalStatistics(StatisticsCommonDTO dto) {
        handleQueryDTO(dto);
        List<StatisticDataGroupVO> result = new ArrayList<>();
        List<CustomerNurtureFlowStatisticsVO> statistics = statisticsCommonService.getCustomerNurtureFlowStatistics(dto);
        Map<Integer, CustomerNurtureFlowStatisticsVO> thirdTypeMap = statistics.stream().collect(Collectors.toMap(CustomerNurtureFlowStatisticsVO::getCustomerContactType, Function.identity()));

        CustomerNurtureFlowStatisticsVO whatsApp = thirdTypeMap.getOrDefault(ProxyThirdTypeEnum.WHAT_APP.getValue(), new CustomerNurtureFlowStatisticsVO());
        List<StatisticDataItemVO> whatsAppDataList = new ArrayList<>();
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("总数").itemCount(whatsApp != null ? whatsApp.getTotalCount() : 0L).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("待执行").itemCount(whatsApp != null ? whatsApp.getPendingCount() : 0L).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("执行中").itemCount(whatsApp != null ? whatsApp.getExecutingCount() : 0L).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("已结束").itemCount(whatsApp != null ? whatsApp.getCompletedCount() : 0L).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("已终止").itemCount(whatsApp != null ? whatsApp.getTerminatedCount() : 0L).build());
        whatsAppDataList.add(StatisticDataItemVO.builder().itemName("异常").itemCount(whatsApp != null ? whatsApp.getAbnormalCount() : 0L).build());

        result.add(StatisticDataGroupVO.builder()
                .groupName("WhatsApp")
                .dataList(whatsAppDataList)
                .build());

        return result;
    }

    private static List<LocalDate> getDateRanges(StatisticsCommonDTO dto) {
        if (StrUtil.isBlank(dto.getStartTime()) || StrUtil.isBlank(dto.getEndTime())) {
            return new ArrayList<>();
        }
        LocalDate startDate = LocalDate.parse(dto.getStartTime(), DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(dto.getEndTime(), DATE_FORMATTER);
        List<LocalDate> dates = new ArrayList<>();
        while (startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
            dates.add(startDate);
            startDate = startDate.plusDays(1);
        }
        return dates;
    }

    private void handleQueryDTO(StatisticsCommonDTO dto) {
        OrgTypeEnum curUserOrgType = tenantIdValueService.getCurUserOrgType();
        if (curUserOrgType != OrgTypeEnum.INNER) {
            Long tenantId = tenantIdValueService.getTenantId();
            dto.setTenantIds(List.of(Objects.requireNonNullElse(tenantId, -1L)));
        }
    }
}
