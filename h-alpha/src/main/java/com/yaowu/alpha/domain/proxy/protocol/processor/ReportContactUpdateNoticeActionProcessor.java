package com.yaowu.alpha.domain.proxy.protocol.processor;

import cn.hutool.core.collection.CollUtil;
import com.freedom.toolscommon.utils.JacksonUtils;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountConfigBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.ReportContactUpdateNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.protocol.GroupInfo;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import com.yaowu.alpha.model.vo.proxy.PullTaskAckVO;
import com.yaowu.alpha.utils.convertor.proxy.wetool.WetoolCommonMapStruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 好友信息变更
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReportContactUpdateNoticeActionProcessor implements INoticeActionProcessor<ReportContactUpdateNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyAccountConfigBizService proxyAccountConfigBizService;

    private final IProxyAccountFriendBizService friendBizService;

    private final IProxyGroupBizService groupBizService;


    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof ReportContactUpdateNoticeRequestDTO;
    }


    @Override
    public NoticeBaseResponseVO process(ReportContactUpdateNoticeRequestDTO request) {
        final ProxyAccount accountConfig = proxyAccountConfigBizService.getOneByProxyId(request);
        if (Objects.isNull(accountConfig)) {
            log.warn("没有配置该代理账户，proxyId：{}", request.getProxyId());
            return PullTaskAckVO.emptyAck(request);
        }
        ProxyThirdTypeEnum proxyThirdTypeEnum = ProxyThirdTypeEnum.ofCode(accountConfig.getThirdType());
        String proxyThirdTypeDesc = proxyThirdTypeEnum != null ? proxyThirdTypeEnum.getDesc() : null;
        log.info(proxyThirdTypeDesc +"代理-好友信息变更-消息：{}", JacksonUtils.toJsonStr(request));
        // 更新私聊好友
        friendBizService.updateFriendAsync(request, accountConfig);

        // 更新群
        final List<GroupInfo> groupInfos = request.getUpdate_list().stream()
                // 过滤群聊
                .filter(updateInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(updateInfo.getRoom_wxid())
                        && updateInfo.getWxid().endsWith("@chatroom"))
                .map(WetoolCommonMapStruct.INSTANCE::toGroupInfo)
                .toList();
        if (CollUtil.isNotEmpty(groupInfos)) {
            groupBizService.updateGroupAsync(groupInfos, accountConfig);
        }

        return NoticeBaseResponseVO.commonAck(request);
    }

}
