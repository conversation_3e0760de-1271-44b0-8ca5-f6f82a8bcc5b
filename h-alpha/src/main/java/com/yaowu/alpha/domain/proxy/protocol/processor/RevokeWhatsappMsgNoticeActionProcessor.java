package com.yaowu.alpha.domain.proxy.protocol.processor;

import com.yaowu.alpha.domain.proxy.control.biz.IProxyNoticeControlBizService;
import com.yaowu.alpha.domain.proxy.protocol.INoticeActionProcessor;
import com.yaowu.alpha.model.dto.proxy.control.BaseNoticeRequestDTO;
import com.yaowu.alpha.model.dto.proxy.control.RevokeWhatsappMsgNoticeRequestDTO;
import com.yaowu.alpha.model.vo.proxy.NoticeBaseResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 撤回WhatsApp消息处理器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RevokeWhatsappMsgNoticeActionProcessor implements INoticeActionProcessor<RevokeWhatsappMsgNoticeRequestDTO, NoticeBaseResponseVO> {

    private final IProxyNoticeControlBizService proxyNoticeControlBizService;

    @Override
    public boolean supports(BaseNoticeRequestDTO request) {
        return request instanceof RevokeWhatsappMsgNoticeRequestDTO;
    }

    @Override
    public NoticeBaseResponseVO process(RevokeWhatsappMsgNoticeRequestDTO request) {
        log.info("开始处理撤回WhatsApp消息通知, proxyId: {}, taskId: {}, msgId: {}, revokeResult: {}", 
                request.getProxyId(), request.getTask_id(), request.getMsg_id(), request.getRevoke_result());
        try {
            // 调用业务服务处理
            proxyNoticeControlBizService.revokeWhatsappMsg(request);
        } catch (Exception e) {
            log.error("处理撤回WhatsApp消息异常, proxyId: {}, taskId: {}, 错误: {}", request.getProxyId(), request.getTask_id(), e.getMessage(), e);
        }
        return NoticeBaseResponseVO.commonAck(request);
    }
}
