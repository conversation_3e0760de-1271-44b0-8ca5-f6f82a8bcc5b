package com.yaowu.alpha;

import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.freedom.mq.annotation.EnableRabbitMQ;
import com.freedom.objectstorage.aspect.CommonFileUrlSignHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableScheduling
@SpringBootApplication
@EnableRabbitMQ
@EnableDiscoveryClient //启用服务发现
@EnableNacosConfig // 启用nacos配置中心
@EnableFeignClients(basePackages = {"com.**.**.feign"}) // 启用feigh，basePackages指定到具体的@FeignClient包名
@Import(value = {CommonFileUrlSignHandler.class})
public class AlphaApplication {

    public static void main(String[] args) {
        SpringApplication.run(AlphaApplication.class, args);
    }

}
