package com.yaowu.alpha.constant;

import com.yaowu.alpha.enums.common.ErrorCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/22-19:43
 */
public class LangDictConstant {

    public static final String LANGUAGE_HEADER = "X-Custom-Language";

    @AllArgsConstructor
    public enum LangTypeEnum {
        ZH_CN("zh_CN", "简体中文"), EN_US("en_US", "美国英文");
        @Getter
        private final String code;
        private final String desc;
    }

    /**
     * 账户流水
     * {@link com.yaowu.alpha.enums.user.AccountFlowTypeEnum}
     */
    public static final String ACCOUNT_FLOW_CATEGORY_CODE = "ACCOUNT_FLOW";

    /**
     * 支付订单状态
     * {@link com.yaowu.alpha.enums.payment.PaymentOrderStatusEnum}
     */
    public static final String PAY_ORDER_STATUS_CATEGORY_CODE = "PAY_ORDER_STATUS";

    /**
     * 三方类型
     * {@link com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum}
     */
    public static final String THIRD_TYPE_CATEGORY_CODE = "THIRD_TYPE";

    /**
     * 代理账号类型
     * {@link com.yaowu.alpha.enums.proxy.ProxyTypeEnum}
     */
    public static final String PROXY_TYPE_CATEGORY_CODE = "PROXY_TYPE";

    /**
     * 错误码
     * {@link ErrorCodeEnum}
     */
    public static final String ERROR_CODE_CATEGORY_CODE = "ERROR_CODE";

    /**
     * 知识库类型
     * 知识库类型，1：文档型，2：结构化数据，3：FAQ
     */
    public static final String KNOWLEDGE_TYPE_CATEGORY_CODE = "KNOWLEDGE_TYPE";

    /**
     * 占位符
     */
    public static final String PLACE_HOLDER_CATEGORY_CODE = "PLACE_HOLDER";

    /**
     * 常量编码
     */
    public static final String CONSTANCE_CODE_CATEGORY_CODE = "CONSTANCE_CODE";
}
