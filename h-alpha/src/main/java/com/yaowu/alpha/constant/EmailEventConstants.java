package com.yaowu.alpha.constant;

import lombok.experimental.UtilityClass;

/**
 * 邮件事件常量
 * 统一管理Resend邮件服务的事件类型
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@UtilityClass
public class EmailEventConstants {

    /**
     * 邮件发送成功事件
     */
    public static final String EMAIL_SENT = "email.sent";

    /**
     * 邮件投递成功事件
     */
    public static final String EMAIL_DELIVERED = "email.delivered";

    /**
     * 邮件打开事件
     */
    public static final String EMAIL_OPENED = "email.opened";

    /**
     * 邮件点击事件
     */
    public static final String EMAIL_CLICKED = "email.clicked";

    /**
     * 邮件退回事件
     */
    public static final String EMAIL_BOUNCED = "email.bounced";

    /**
     * 邮件发送失败事件
     */
    public static final String EMAIL_FAILED = "email.failed";

    /**
     * 邮件投诉事件
     */
    public static final String EMAIL_COMPLAINED = "email.complained";

    /**
     * 邮件投递延迟事件
     */
    public static final String EMAIL_DELIVERY_DELAYED = "email.delivery_delayed";

    /**
     * 邮件回复事件， 非Resend所有
     */
    public static final String EMAIL_REPLIED = "email.replied";
} 