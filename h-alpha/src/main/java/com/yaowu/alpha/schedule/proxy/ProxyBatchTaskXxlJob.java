package com.yaowu.alpha.schedule.proxy;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyBatchTaskBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/26 18:01
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyBatchTaskXxlJob {

    private final IProxyBatchTaskBizService proxyBatchTaskBizService;

    @XxlJob("executeProxyBatchTask")
    @Trace
    public void executeProxyBatchTask() {
        XxlJobHelper.log("定时- 执行代理批量任务-开始，traceId={}", TraceUtil.traceId());
        proxyBatchTaskBizService.executeBatchTask();
        XxlJobHelper.log("定时- 执行代理批量任务-完毕");

    }

}
