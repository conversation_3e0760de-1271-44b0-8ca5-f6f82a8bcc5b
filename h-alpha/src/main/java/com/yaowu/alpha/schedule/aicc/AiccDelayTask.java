package com.yaowu.alpha.schedule.aicc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.biz.AiccSseService;
import com.yaowu.alpha.utils.contants.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 天润智能机器人提取
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AiccDelayTask {

    @Autowired
    private AiccSseService aiccSseService;

    @Autowired
    protected RedisTemplate<String, Object> redisTemplate;

    /**
     * 天润智能体定时提取线索
     */
    @XxlJob("aiccDelayExtractClue")
    @Trace
    public void delayExtractClue() {
        XxlJobHelper.log("delayExtractClue_start:" + TraceContext.traceId());
        String param = XxlJobHelper.getJobParam();
        List<Object> mainUniquestList = new ArrayList<>();
        if (StringUtils.hasText(param)) {
            mainUniquestList = JSONUtil.toList(param, Object.class);
        } else {
            // 从队列中取出所有的通话记录ID
            mainUniquestList = redisTemplate.opsForList()
                    .range(RedisConstants.AICC_MAIN_UNI_QUEID, 0, -1);
        }
        if (CollUtil.isEmpty(mainUniquestList)) {
            XxlJobHelper.log("delayExtractClue_is_empty_end:" + TraceContext.traceId());
            return;
        }
        log.info("当前待提取的通话记录ID，：{}", JSONUtil.toJsonStr(mainUniquestList));
        for (Object userId : mainUniquestList) {
            try {
                boolean extractResult = aiccSseService.delayExtractClue((String) userId);
                if (extractResult) {
                    redisTemplate.opsForList().remove(RedisConstants.AICC_MAIN_UNI_QUEID, 1, userId);
                }
            } catch (Exception e) {
                log.warn("天润智能体定时提取线索异常 ", e);
            }
        }
        XxlJobHelper.log("delayExtractClue_end:" + TraceContext.traceId());
    }
}
