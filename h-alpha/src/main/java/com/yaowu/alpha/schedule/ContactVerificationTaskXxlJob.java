package com.yaowu.alpha.schedule;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.common.biz.IContactVerificationTaskBizService;
import com.yaowu.alpha.utils.TraceUtil;
import com.yaowu.alpha.utils.XxlJobParamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * 联系方式验证任务定时任务
 * <AUTHOR>
 * 2024-12-19 15:30:00
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ContactVerificationTaskXxlJob {
    
    private final IContactVerificationTaskBizService contactVerificationTaskBizService;
    
    /**
     * 处理联系方式验证任务
     * 支持通过参数配置批量执行数量
     * 
     * 参数格式: {"batchSize": 50} 或直接传数字 "50"
     * 不传参数时使用默认批量大小
     */
    @XxlJob("processContactVerificationTasks")
    @Trace
    public void processVerificationTasks() {
        XxlJobHelper.log("定时-处理联系方式验证任务-开始，traceId={}", TraceUtil.traceId());
        
        try {
            // 解析批次大小参数
            Integer batchSize = XxlJobParamUtil.parseBatchSize();
            int actualBatchSize = batchSize != null ? batchSize : 50;
            // 执行验证任务处理
            int processedCount = contactVerificationTaskBizService.processVerificationTasks(actualBatchSize);
            XxlJobHelper.log("定时-处理联系方式验证任务-完成，处理任务数: {}", processedCount);
        } catch (Exception e) {
            log.error("定时-处理联系方式验证任务-异常", e);
            XxlJobHelper.log("定时-处理联系方式验证任务-异常：{}", e.getMessage());
            throw e;
        }
    }
    

}
