package com.yaowu.alpha.schedule.proxy;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.biz.IProxyInteractBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/10 20:12
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyInteractXxlJob {

    private final IProxyInteractBizService proxyInteractBizService;

    @XxlJob("interactWithFriend")
    @Trace
    public void interactWithFriend() {
        XxlJobHelper.log("定时- 执行好友定期互动任务-开始，traceId={}", TraceUtil.traceId());
        proxyInteractBizService.interactWithFriend();
        XxlJobHelper.log("定时- 执行好友定期互动任务-结束");
    }
}
