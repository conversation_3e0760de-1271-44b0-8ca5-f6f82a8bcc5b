package com.yaowu.alpha.schedule.proxy.background;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.biz.ICompanyBackgroundTaskBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * 公司背调任务定时任务
 * 用于处理过期的背调任务，将过期任务状态更新为已过期或已失败
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CompanyBackgroundTaskXxlJob {

    private final ICompanyBackgroundTaskBizService companyBackgroundTaskBizService;

    /**
     * 扫描并处理过期的背调任务
     * 将未开始且过期的任务标记为已过期(5)
     * 将进行中且过期的任务标记为已失败(4)
     */
    @XxlJob("scanAndExpireBackgroundTasks")
    @Trace
    public void scanAndExpireBackgroundTasks() {
        XxlJobHelper.log("定时- 执行背调任务过期处理-开始，traceId={}", TraceUtil.traceId());
        
        try {
            String result = companyBackgroundTaskBizService.processExpiredBackgroundTasks();
            XxlJobHelper.log("定时- 执行背调任务过期处理-完毕，处理结果: {}", result);
        } catch (Exception e) {
            log.error("背调任务过期处理失败", e);
            XxlJobHelper.log("定时- 执行背调任务过期处理-失败，错误信息: {}", e.getMessage());
        }
    }
}
