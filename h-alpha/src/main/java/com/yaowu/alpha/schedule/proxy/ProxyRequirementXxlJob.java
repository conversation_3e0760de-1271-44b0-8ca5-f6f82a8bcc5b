package com.yaowu.alpha.schedule.proxy;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyCustomerRequirementBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/27 18:51
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyRequirementXxlJob {

    private final IProxyCustomerRequirementBizService proxyCustomerRequirementBizService;

    @XxlJob("closeRequirement")
    @Trace
    public void closeRequirement() {
        XxlJobHelper.log("定时- 执行代理需求单关闭任务-开始，traceId={}", TraceUtil.traceId());
        proxyCustomerRequirementBizService.queryAndCloseRequirement();
        XxlJobHelper.log("定时- 执行代理需求单关闭任务-完毕");
    }

    @XxlJob("dispatchRequirement")
    @Trace
    public void dispatchRequirement() {
        XxlJobHelper.log("定时- 执行代理需求单分发任务-开始，traceId={}", TraceUtil.traceId());
        proxyCustomerRequirementBizService.queryAndDispatchRequirement();
        XxlJobHelper.log("定时- 执行代理需求单分发任务-完毕");
    }

    @XxlJob("overtimeRequirement")
    @Trace
    public void overtimeRequirement() {
        XxlJobHelper.log("定时- 执行代理需求单超时任务-开始，traceId={}", TraceUtil.traceId());
        proxyCustomerRequirementBizService.queryAndOvertimeRequirement();
        XxlJobHelper.log("定时- 执行代理需求单超时任务-完毕");
    }
}
