package com.yaowu.alpha.schedule;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.freedom.redis.annotation.DistributedLock;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowBizService;
import com.yaowu.alpha.domain.customer.biz.INurtureCustomerFlowNodeBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class NurtureCustomerFlowXxlJob {

    private final INurtureCustomerFlowBizService nurtureCustomerFlowBizService;
    private final INurtureCustomerFlowNodeBizService nurtureCustomerFlowNodeBizService;

    @DistributedLock(key = "NurtureCustomerFlowXxlJob:startFlow")
    @XxlJob("startNurtureCustomerFlow")
    @Trace
    public void startNurtureCustomerFlow() {
        XxlJobHelper.log("定时-培育流程启动任务-开始，traceId={}", TraceUtil.traceId());
        nurtureCustomerFlowBizService.startFlow();
        XxlJobHelper.log("定时-培育流程启动任务-结束");
    }

    @DistributedLock(key = "NurtureCustomerFlowXxlJob:executeFlowNode")
    @XxlJob("executeNurtureCustomerFlowNode")
    @Trace
    public void executeNurtureCustomerFlowNode() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时-执行培育流程节点-开始，param:{}, traceId={}", param, TraceUtil.traceId());
        JSONObject jsonObject = JSONUtil.parseObj(param);
        Long maxSize = jsonObject.getLong("num", 20L);
        nurtureCustomerFlowNodeBizService.executeNode(maxSize);
        XxlJobHelper.log("定时-执行培育流程节点-结束");
    }

    @DistributedLock(key = "NurtureCustomerFlowXxlJob:processExpiredNodes")
    @XxlJob("processNurtureCustomerFlowExpiredNodes")
    @Trace
    public void processNurtureCustomerFlowExpiredNodes() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时-处理过期节点任务-开始，param:{}, traceId={}", param, TraceUtil.traceId());
        JSONObject jsonObject = JSONUtil.parseObj(param);
        Long maxSize = jsonObject.getLong("num", 20L);
        nurtureCustomerFlowNodeBizService.processExpiredNodes(maxSize);
        XxlJobHelper.log("定时-处理过期节点任务-结束");
    }
}
