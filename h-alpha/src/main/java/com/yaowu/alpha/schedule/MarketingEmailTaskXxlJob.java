package com.yaowu.alpha.schedule;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.email.biz.IMarketingEmailBizService;
import com.yaowu.alpha.model.vo.email.MarketingEmailTaskExecutionResultVO;
import com.yaowu.alpha.utils.TraceUtil;
import com.yaowu.alpha.utils.XxlJobParamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * 营销邮件任务定时执行器
 * 负责定时执行待发送的营销邮件任务
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MarketingEmailTaskXxlJob {

    private final IMarketingEmailBizService marketingEmailBizService;

    /**
     * 执行营销邮件任务
     * 支持通过参数配置批量执行数量
     * 
     * 参数格式: {"batchSize": 50}
     * 不传参数时使用默认批量大小
     */
    @XxlJob("executeMarketingEmailTasks")
    @Trace
    public void executeMarketingEmailTasks() {
        XxlJobHelper.log("定时-执行营销邮件任务-开始，traceId={}", TraceUtil.traceId());
        
        try {
            // 解析任务参数
            Integer batchSize = XxlJobParamUtil.parseBatchSize();
            
            // 执行营销邮件任务
            MarketingEmailTaskExecutionResultVO result = marketingEmailBizService.executeMarketingEmailTasks(batchSize);
            
            // 记录执行结果
            logExecutionResult(result);
            
            XxlJobHelper.log("定时-执行营销邮件任务-完成，处理数量：{}，成功：{}，失败：{}", 
                            result.getTotalProcessed(), result.getSuccessCount(), result.getFailedCount());
            
        } catch (Exception e) {
            log.error("定时-执行营销邮件任务-异常", e);
            XxlJobHelper.log("定时-执行营销邮件任务-异常：{}", e.getMessage());
            throw e;
        }
    }



    /**
     * 记录执行结果
     */
    private void logExecutionResult(MarketingEmailTaskExecutionResultVO result) {
        XxlJobHelper.log("执行结果统计：");
        XxlJobHelper.log("- 总处理数量：{}", result.getTotalProcessed());
        XxlJobHelper.log("- 成功数量：{}", result.getSuccessCount());
        XxlJobHelper.log("- 失败数量：{}", result.getFailedCount());
        XxlJobHelper.log("- 执行耗时：{}ms", result.getExecutionTime());
        XxlJobHelper.log("- 执行状态：{}", result.getMessage());
    }
}
