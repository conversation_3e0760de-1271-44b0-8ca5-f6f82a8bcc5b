package com.yaowu.alpha.schedule.proxy;

import cn.hutool.core.collection.CollUtil;
import com.freedom.toolscommon.utils.StreamTools;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.config.nacos.CommonConfig;
import com.yaowu.alpha.config.nacos.LLMAgentConfig;
import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyGroupBizService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.enums.proxy.ProxyThirdTypeEnum;
import com.yaowu.alpha.enums.proxy.ProxyTypeEnum;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.utils.TraceUtil;
import com.yaowu.alpha.utils.common.EnumUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 微信代理账号-定时任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyAccountConfigXxlJob {

    private final IProxyAccountConfigService accountConfigService;

    private final IWarnNoticeBizService warnNoticeBizService;

    private final LLMAgentConfig llmAgentConfig;

    private final CommonConfig commonConfig;

    private final IProxyGroupBizService proxyGroupBizService;


    /**
     * 掉线监控-超时时间，单位分钟
     */
    private static final long OFFLINE_MINUTES = 10;


    /**
     * 微信代理账号-掉线监控-定时任务
     */
    @XxlJob("wxAccountConfigOfflineMonitorXxlJob")
    @Trace
    public void wxAccountConfigOfflineMonitorXxlJob() {
        // 最多通知3次
        LocalDateTime gtUpdateTime = LocalDateTime.now().plusMinutes(-4 * OFFLINE_MINUTES);
        LocalDateTime ltUpdateTime = LocalDateTime.now().plusMinutes(-OFFLINE_MINUTES);

        final List<ProxyAccount> accountConfigList = accountConfigService.listOfflineAccountConfig(gtUpdateTime,
                ltUpdateTime,
                CollUtil.newArrayList(ProxyTypeEnum.WE_TOOL),
                CollUtil.newArrayList(ProxyThirdTypeEnum.WECHAT, ProxyThirdTypeEnum.WHAT_APP)
        );
        if (CollUtil.isEmpty(accountConfigList)) {
            return;
        }
        StringBuilder builder = new StringBuilder();
        builder.append("以下代理账号已掉线，请尽快处理：").append("\n");
        for (ProxyAccount proxyAccount : accountConfigList) {
            ProxyThirdTypeEnum proxyThirdTypeEnum = EnumUtil.fromValue(proxyAccount.getThirdType(), ProxyThirdTypeEnum.class);
            builder.append(" - ")
                    .append(proxyThirdTypeEnum == null ? "未知" : proxyThirdTypeEnum.getDesc())
                    .append("  ")
                    .append(proxyAccount.getAccountName())
                    .append("（").append(proxyAccount.getProxyId()).append("）")
                    .append("\n");
        }
        Set<Long> accountIds = StreamTools.toSet(accountConfigList, ProxyAccount::getId);
        accountConfigService.updateOffline(accountIds);
        warnNoticeBizService.sendNoticeToWechat(builder.toString(), commonConfig.getSystemWarnNoticeBotKey(), CollUtil.newArrayList("@all"));
    }

    /**
     * 获取群成员信息-定时任务
     */
    @XxlJob("batchSubmitGroupMembersTaskXxlJob")
    @Trace
    public void batchSubmitGroupMembersTaskXxlJob() {
        XxlJobHelper.log("定时- 获取群成员信息-开始，traceId={}", TraceUtil.traceId());
        proxyGroupBizService.batchSubmitQueryGroupMembersDetailTask();
        XxlJobHelper.log("定时- 获取群成员信息-结束");
    }
}


