package com.yaowu.alpha.schedule;

import com.freedom.redis.annotation.DistributedLock;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.email.biz.IEmailSyncBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class EmailSyncXxlJob {

    private final IEmailSyncBizService emailSyncBizService;

    /**
     * 同步所有邮件账户
     */
    @XxlJob("syncAllEmailAccount")
    @Trace
    @DistributedLock(key = "syncAllEmailAccount")
    public void syncAllEmailAccount() {
        XxlJobHelper.log("定时-同步所有邮件账户-开始，traceId={}", TraceUtil.traceId());
        emailSyncBizService.syncAllEmailAccount();
        XxlJobHelper.log("定时-同步所有邮件账户-完成");
    }
}
