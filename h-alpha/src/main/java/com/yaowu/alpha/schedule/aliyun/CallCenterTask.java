package com.yaowu.alpha.schedule.aliyun;

import cn.hutool.json.JSONUtil;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.chatbot20220408.AsyncClient;
import com.aliyun.sdk.service.voicenavigator20180612.models.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.config.ali.AliyunCallCenter;
import com.yaowu.alpha.domain.proxy.biz.AliyunCallCenterService;
import com.yaowu.alpha.utils.common.LongUtils;
import com.yaowu.alpha.utils.common.StreamUtil;
import com.yaowu.alpha.utils.contants.RedisConstants;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阿里云呼叫中心定时拉去会话信息任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CallCenterTask {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private com.aliyun.sdk.service.chatbot20220408.AsyncClient client;

    private com.aliyun.sdk.service.voicenavigator20180612.AsyncClient voiceClient;

    @Autowired
    private AliyunCallCenter aliyunCallCenter;

    @Autowired
    private AliyunCallCenterService aliyunCallCenterService;


    /**
     * 阿里云呼叫中心会话ID对应的接听电话
     */
    public static final String ALI_YUN_CALL_CENTER_BEGIN_TIME_LEFTRANGE = "ALI_YUN_CALL_CENTER_BEGIN_TIME_LEFTRANGE";

    @PostConstruct
    public void init() {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(aliyunCallCenter.getSecretId()).accessKeySecret(aliyunCallCenter.getSecretKey()).build());
        client = AsyncClient.builder().region("cn-shanghai") // Region ID
                .credentialsProvider(provider).overrideConfiguration(ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Chatbot
                                .setEndpointOverride("chatbot.cn-shanghai.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                ).build();
        voiceClient = com.aliyun.sdk.service.voicenavigator20180612.AsyncClient.builder()
                .region("cn-shanghai") // Region ID
                .credentialsProvider(provider).overrideConfiguration(ClientOverrideConfiguration.create()
                        // Endpoint 请参考 https://api.aliyun.com/product/VoiceNavigator
                        .setEndpointOverride("voicenavigator.cn-shanghai.aliyuncs.com")).build();
    }

    /**
     * 阿里云呼叫中心根据会话ID读取对话内容，然后提取线索
     * https://help.aliyun.com/zh/voice-navigator/developer-reference/api-listconversationdetails-42658
     */
    @XxlJob("aliyunDelayExtractClue")
    @Trace
    public void delayExtractClue() {
        XxlJobHelper.log("delayExtractClue_start:" + TraceContext.traceId());
        List<Object> mainUniquestList = redisTemplate.opsForList()
                .range(RedisConstants.ALI_YUN_CALL_CENTER_MAIN_UNI_QUEID, 0, -1);
        if (mainUniquestList == null || mainUniquestList.isEmpty()) {
            return;
        }
        for (Object sessionId : mainUniquestList) {
            ListConversationDetailsRequest listConversationDetailsRequest =
                    ListConversationDetailsRequest.builder().instanceId(aliyunCallCenter.getVoiceNavigatorInstanceId())
                            .conversationId(sessionId.toString()).build();
            log.info("aliyunDelayExtractClue_request:{}", JSONUtil.toJsonStr(listConversationDetailsRequest));
            CompletableFuture<ListConversationDetailsResponse> response = voiceClient.listConversationDetails(listConversationDetailsRequest);
            try {
                ListConversationDetailsResponse resp = response.get();
                log.info("aliyunDelayExtractClue_response:{}", JSONUtil.toJsonStr(resp));
                List<ListConversationDetailsResponseBody.ConversationDetails> datas = resp.getBody()
                        .getConversationDetails();
                boolean save = aliyunCallCenterService.saveHistoryChat(datas);
                if (save) {
                    boolean extractResult = aliyunCallCenterService.delayExtractClue(sessionId.toString());
                    if (extractResult) {
                        redisTemplate.opsForList()
                                .remove(RedisConstants.ALI_YUN_CALL_CENTER_MAIN_UNI_QUEID, 1, sessionId.toString());
                        redisTemplate.delete(String.format(RedisConstants.ALI_YUN_CALL_CENTER_USER_PHONE, sessionId.toString()));
                    }
                }
            } catch (Exception e) {
                log.error("listTongyiChatHistorys error", e);
            }
        }

        XxlJobHelper.log("delayExtractClue_end:" + TraceContext.traceId());
    }

    /**
     * 阿里云呼叫中心获取最近20条会话ID，定时拉去即可，每次都是拉去最新的20条
     * https://help.aliyun.com/zh/voice-navigator/developer-reference/api-listconversations-68153
     */
    @XxlJob("aliyunDelayExtractSessionId")
    @Trace
    public void delayExtractSessionId() {
        XxlJobHelper.log("delayExtractSessionId_start:" + TraceContext.traceId());
        Long beginTimeLeftRange = LocalDateTime.now().minusMinutes(5).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        if (redisTemplate.hasKey(ALI_YUN_CALL_CENTER_BEGIN_TIME_LEFTRANGE)) {
            beginTimeLeftRange = (Long) redisTemplate.opsForValue().get(ALI_YUN_CALL_CENTER_BEGIN_TIME_LEFTRANGE);
        }
        //往前推30分钟
        beginTimeLeftRange = beginTimeLeftRange - 30 * 60 * 1000;
        ListConversationsRequest listConversationsRequest = ListConversationsRequest.builder()
                .instanceId(aliyunCallCenter.getVoiceNavigatorInstanceId()).pageNumber(1).pageSize(20)
                .beginTimeLeftRange(beginTimeLeftRange).build();
        log.info("listConversationsRequest_request:{}", JSONUtil.toJsonStr(listConversationsRequest));
        CompletableFuture<ListConversationsResponse> response = voiceClient.listConversations(listConversationsRequest);
        try {
            ListConversationsResponse resp = response.get();
            log.info("ListConversationsResponse_response:{}", JSONUtil.toJsonStr(resp));
            Map<String, ListConversationsResponseBody.Conversations> conversationMap = StreamUtil.of(resp.getBody().getConversations())
                    .filter(Objects::nonNull)
                    .filter(conversation -> LongUtils.isValid(conversation.getEndTime()) && conversation.getEndTime() > 1737167504000L)
                    .collect(Collectors.toMap(
                            ListConversationsResponseBody.Conversations::getConversationId,
                            Function.identity()
                    ));
            setSession2CacheList(conversationMap);
        } catch (Exception e) {
            log.info("listConversations error,start time {}", beginTimeLeftRange);
        } finally {
            Long beginTimeRightRange = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
            redisTemplate.opsForValue()
                    .set(ALI_YUN_CALL_CENTER_BEGIN_TIME_LEFTRANGE, beginTimeRightRange, 24, TimeUnit.HOURS);
        }
        XxlJobHelper.log("delayExtractSessionId_end:" + TraceContext.traceId());
    }

    private void setSession2CacheList(Map<String, ListConversationsResponseBody.Conversations> conversationMap) {
        List<Object> mainUniquestList = redisTemplate.opsForList()
                .range(RedisConstants.ALI_YUN_CALL_CENTER_MAIN_UNI_QUEID, 0, -1);
        conversationMap.forEach((sessionId, conversation) -> {
            if (mainUniquestList != null && mainUniquestList.stream()
                    .anyMatch(item -> sessionId.equals(item.toString()))) {
                return;
            } else {
                //不存在则加入
                if (!aliyunCallCenterService.exist(sessionId)) {
                    redisTemplate.opsForList().leftPush(RedisConstants.ALI_YUN_CALL_CENTER_MAIN_UNI_QUEID, sessionId);
                    redisTemplate.expire(RedisConstants.ALI_YUN_CALL_CENTER_MAIN_UNI_QUEID, 2, TimeUnit.HOURS);
                    redisTemplate.opsForValue()
                            .set(String.format(RedisConstants.ALI_YUN_CALL_CENTER_USER_PHONE, sessionId), conversation.getCallingNumber(), 2, TimeUnit.HOURS);
                    redisTemplate.opsForValue()
                            .set(String.format(RedisConstants.ALI_YUN_CALL_CENTER_USER_CALLED_PHONE, sessionId), conversation.getCalledNumber(), 2, TimeUnit.HOURS);
                }
            }
        });
    }
}
