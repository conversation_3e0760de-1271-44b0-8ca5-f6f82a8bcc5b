package com.yaowu.alpha.schedule.proxy;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/8 16:36
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyTaskXxlJob {

    private final IProxyTaskBizService proxyTaskBizService;

    @XxlJob("scanAndExpireTasks")
    @Trace
    public void scanAndExpireTasks() {
        XxlJobHelper.log("定时- 执行代理任务关闭任务-开始，traceId={}", TraceUtil.traceId());
        proxyTaskBizService.scanAndExpireTasks();
        XxlJobHelper.log("定时- 执行代理任务关闭任务-完毕");
    }
}
