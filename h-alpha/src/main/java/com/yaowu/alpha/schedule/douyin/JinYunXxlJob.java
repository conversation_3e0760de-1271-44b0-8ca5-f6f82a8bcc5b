package com.yaowu.alpha.schedule.douyin;

import com.freedom.redis.annotation.DistributedLock;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.biz.IJinYunFacadeBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/2 15:46
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JinYunXxlJob {

    private final IJinYunFacadeBizService jinYunAdaptorBizService;

    @XxlJob("refreshJinYunProxyAccount")
    @Trace
    @DistributedLock
    public void refreshJinYunProxyAccount() {
        XxlJobHelper.log("定时-执行井云代理账户数据刷新任务-开始，traceId={}", TraceUtil.traceId());
        jinYunAdaptorBizService.refreshProxyAccount();
        XxlJobHelper.log("定时-执行井云代理账户数据刷新任务-结束");
    }


    @XxlJob("pullJinYunMessages")
    @Trace
    @DistributedLock
    public void pullJinYunMessages() {
        XxlJobHelper.log("定时-执行井云消息拉取任务-开始，traceId={}", TraceUtil.traceId());
        jinYunAdaptorBizService.pullMessages();
        XxlJobHelper.log("定时-执行井云消息拉取任务-结束");
    }

    @XxlJob("pullJinYunTaskAndExecute")
    @Trace
    @DistributedLock
    public void pullJinYunTaskAndExecute() {
        XxlJobHelper.log("定时-执行井云任务拉取并执行任务-开始，traceId={}", TraceUtil.traceId());
        jinYunAdaptorBizService.pullJinYunTaskAndExecute();
        XxlJobHelper.log("定时-执行井云任务拉取并执行任务-结束");
    }

}
