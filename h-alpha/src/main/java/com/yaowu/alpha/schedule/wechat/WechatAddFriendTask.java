package com.yaowu.alpha.schedule.wechat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.config.nacos.WeixinAgentControlConfig;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountAddFriendBizService;
import com.yaowu.alpha.domain.proxy.control.biz.NewFriendPhoneBizService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Component
@Slf4j
public class WechatAddFriendTask {

    @Autowired
    private IProxyAccountAddFriendBizService iProxyAccountAddFriendBizService;

    @Autowired
    private WeixinAgentControlConfig weixinAgentControlConfig;

    @Autowired
    private NewFriendPhoneBizService newFriendPhoneBizService;

    /**
     * 添加好友
     */
    @XxlJob("addFriendFromGroupTask")
    @Trace
    void addFriendFromGroup() {
        XxlJobHelper.log("addFriendFromGroupTask_start:" + TraceContext.traceId());
        String param = XxlJobHelper.getJobParam();
        List<AddGroupParam> addGroupParams = new ArrayList<>();
        if (StringUtils.hasText(param)) {
            addGroupParams = JSONUtil.toList(JSONUtil.parseArray(param), AddGroupParam.class);
        }
        for (AddGroupParam addGroupParam : addGroupParams) {
            if (null == addGroupParam.getProxyAccountId()) {
                continue;
            }
            for (Long groupId : addGroupParam.getGroupIds()) {
                iProxyAccountAddFriendBizService.addFriendFromGroup(addGroupParam.getProxyAccountId(), groupId);
            }
        }
        List<Long> proxyIds = weixinAgentControlConfig.getAddFriendByPhoneAccountIds();
        if (CollUtil.isEmpty(proxyIds)) {
            return;
        }
        for (Long proxyId : proxyIds) {
            List<String> phones = newFriendPhoneBizService.queryWaitAddPhoneList(weixinAgentControlConfig.getAddFriendByPhoneLimit());
            iProxyAccountAddFriendBizService.addFriendByPhones(proxyId, new HashSet<>(phones));
        }
    }


    /**
     * 开始从群添加好友到任务队列中
     */
    @XxlJob("startGroupAddFriendToTask")
    @Trace
    void startGroupAddFriendToTask() {
        XxlJobHelper.log("startGroupAddFriendToTask_start:" + TraceContext.traceId());
        String param = XxlJobHelper.getJobParam();
        Long proxyAccountId = null;
        if (StringUtils.hasText(param)) {
            proxyAccountId = Long.parseLong(param);
        }
        // proxyAccountId 代理账户配置项id,为空的话，就查询所有的代理账号
        iProxyAccountAddFriendBizService.startAddFriendToQueueTask(proxyAccountId);
    }

    /**
     * 执行群添加好友任务
     */
    @XxlJob("executeGroupAddFriendTask")
    @Trace
    void executeGroupAddFriendTask() {
        XxlJobHelper.log("executeGroupAddFriendTask_start:" + TraceContext.traceId());
        String param = XxlJobHelper.getJobParam();
        Long proxyAccountId = null;
        if (StringUtils.hasText(param)) {
            proxyAccountId = Long.parseLong(param);
        }
        iProxyAccountAddFriendBizService.executeAddFriendTask(proxyAccountId);
    }

    @Data
    static class AddGroupParam {
        @Schema(description = "代理账户配置项id")
        private Long proxyAccountId;
        @Schema(description = "代理账号所在群Id")
        private List<Long> groupIds;
        @Schema(description = "开始时间")
        private LocalDateTime startTime;
        @Schema(description = "结束时间")
        private LocalDateTime endTime;
    }
}
