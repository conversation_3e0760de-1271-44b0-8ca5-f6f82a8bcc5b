package com.yaowu.alpha.schedule.proxy;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.freedom.redis.annotation.DistributedLock;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.biz.IProxyGreetingBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * 代理平台问候任务处理
 * <AUTHOR>
 * @date 2024/12/10 20:12
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyGreetingXxlJob {

    private final IProxyGreetingBizService proxyGreetingBizService;

    @XxlJob("doGreetingJob")
    @Trace
    public void doGreetingJob() {
        XxlJobHelper.log("定时-执行打招呼任务-开始，traceId={}", TraceUtil.traceId());
        proxyGreetingBizService.processGreetingTasks();
        XxlJobHelper.log("定时-执行打招呼任务-结束");
    }

    @XxlJob("generateGreetingContentJob")
    @Trace
    @DistributedLock(key = "generateGreetingContentJob")
    public void generateGreetingContentJob() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时-生成打招呼内容-开始，param:{}, traceId={}", param, TraceUtil.traceId());
        JSONObject jsonObject = JSONUtil.parseObj(param);
        Long maxSize = jsonObject.getLong("num", 20L);
        proxyGreetingBizService.generateGreetingContents(maxSize);
        XxlJobHelper.log("定时-生成打招呼内容-结束");
    }
}
