package com.yaowu.alpha.schedule.proxy;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyAccountFriendBizService;
import com.yaowu.alpha.utils.TraceUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * 代理平台好友互动时间刷新定时任务
 * <AUTHOR>
 * @date 2024/12/10 20:12
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ProxyFriendInteractionTimeXxlJob {

    private final IProxyAccountFriendBizService proxyAccountFriendBizService;

    /**
     * 刷新好友互动时间任务
     * 根据聊天消息表的最新消息时间更新好友的互动时间字段
     */
    @XxlJob("refreshFriendInteractionTimeJob")
    @Trace
    public void refreshFriendInteractionTimeJob() {
        XxlJobHelper.log("定时-刷新好友互动时间任务-开始，traceId={}", TraceUtil.traceId());
        try {
            // 调用业务服务刷新好友互动时间
            proxyAccountFriendBizService.refreshFriendInteractionTime();
            XxlJobHelper.log("定时-刷新好友互动时间任务-执行成功");
        } catch (Exception e) {
            log.error("定时-刷新好友互动时间任务-执行失败", e);
            XxlJobHelper.log("定时-刷新好友互动时间任务-执行失败: {}", e.getMessage());
        }
        XxlJobHelper.log("定时-刷新好友互动时间任务-结束");
    }
} 