package com.yaowu.alpha.schedule.wechat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yaowu.alpha.domain.common.biz.IWarnNoticeBizService;
import com.yaowu.alpha.domain.proxy.biz.IWorkWxBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTagBizService;
import com.yaowu.alpha.domain.proxy.control.biz.IProxyTaskBizService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountConfigService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyAccountFriendService;
import com.yaowu.alpha.domain.proxy.service.batis.service.IProxyCustomerRequirementService;
import com.yaowu.alpha.model.entity.proxy.ProxyAccount;
import com.yaowu.alpha.model.entity.proxy.ProxyAccountFriend;
import com.yaowu.alpha.model.entity.proxy.ProxyCustomerRequirement;
import com.yaowu.alpha.utils.common.StreamUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 微信好友标签-定时任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WxTagXxlJob {

    private final IProxyAccountFriendService friendService;

    private final IProxyAccountConfigService accountConfigService;

    private final IProxyTaskBizService taskBizService;

    private final IProxyTagBizService tagBizService;

    private final IWarnNoticeBizService warnNoticeService;

    private final IProxyCustomerRequirementService proxyCustomerRequirementService;

    private final IWorkWxBizService iWorkWxBizService;


    /**
     * 微信代理账号标签定时任务
     */
    @XxlJob("wxAccountConfigTagXxlJob")
    @Trace
    public void wxAccountConfigTagXxlJob() {
        final long startTime = System.currentTimeMillis();
        log.info("微信代理账号标签定时任务-开始");

        final List<ProxyAccount> accountConfigList = accountConfigService.list();
        // 判断是否还有记录
        if (CollUtil.isEmpty(accountConfigList)) {
            return;
        }

        // 处理好友数据
        accountConfigList.forEach(accountConfig -> taskBizService.createReportTagInfoTask(accountConfig.getId()));

        log.info("微信代理账号标签定时任务-结束-总耗时={}", System.currentTimeMillis() - startTime);
    }

    /**
     * 微信好友标签定时任务
     */
    @XxlJob("wxFriendTagXxlJob")
    @Trace
    public void wxFriendTagXxlJob() {
        final long startTime = System.currentTimeMillis();
        log.info("微信好友标签定时任务-开始");

        long page = 1;
        long size = 200;

        while (true) {
            final Page<ProxyAccountFriend> friendPage = friendService.page(new Page<>(page, size));
            final List<ProxyAccountFriend> records = friendPage.getRecords();
            // 判断是否还有记录
            if (CollUtil.isEmpty(records)) {
                break;
            }

            // 处理好友数据
            records.forEach(friend -> taskBizService.createReportUserInfoTask(friend.getProxyAccountId(), friend.getFriendProxyId()));

            // 下一页
            page++;
        }

        log.info("微信好友标签定时任务-结束-总耗时={}", System.currentTimeMillis() - startTime);
    }

    /**
     * 微信好友类型标签清洗定时任务
     */
    @XxlJob("wxFriendTypeTagCleanXxlJob")
    @Trace
    public void wxFriendTypeTagCleanXxlJob() {
        final long startTime = System.currentTimeMillis();
        log.info("微信好友类型标签清洗定时任务-开始");
        tagBizService.cleanWxFriendTypeTag();
        log.info("微信好友类型标签清洗定时任务-结束-总耗时={}", System.currentTimeMillis() - startTime);
    }

    /**
     * 企微绑定uid初始化
     */
    @XxlJob("workWxFriendBindUidInitXxlJob")
    @Trace
    public void workWxFriendBindUidInitXxlJob() {
        XxlJobHelper.log("workWxFriendBindUidInitXxlJob:" + TraceContext.traceId());
        final long startTime = System.currentTimeMillis();
        log.info("企微好友初始化绑定uid-开始");
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(param)) {
            iWorkWxBizService.initWorkWxAllFriendAndBindUid(param);
        }else{
            iWorkWxBizService.syncIncrementalWorkWeChatFriends(null);
        }
        log.info("企微好友初始化绑定uid-结束-总耗时={}", System.currentTimeMillis() - startTime);
    }

    /**
     * // TODO: zhaozhiwei 2024/12/10 后续版本删除
     */
    @Deprecated
    @XxlJob("initFriendTag")
    @Trace
    public void initFriendTag() {
        List<ProxyAccountFriend> friends = friendService.list();
        List<ProxyCustomerRequirement> requirements = proxyCustomerRequirementService.list();

        for (ProxyAccountFriend friend : friends) {
            try {
                LocalDate friendDate = friend.getCreateTime().toLocalDate();
                tagBizService.markNewFriendTag(friend, friendDate);

                StreamUtil.of(requirements)
                        .filter(r -> Objects.equals(r.getUserProxyId(), friend.getFriendProxyId()))
                        .findAny()
                        .ifPresent(req -> tagBizService.markCustomerRequirementTag(req, friend.getProxyAccountId(), req.getCreateTime()
                                .toLocalDate()));
            } catch (Exception e) {
                log.warn("好友标签初始化异常，friend={}", JSONUtil.toJsonStr(friend), e);
            }
        }
        warnNoticeService.sendWechat("35a1b45d-bf8d-4989-8786-6266c70638b7", "初始化好友标签完成");

    }

}


