package com.yaowu.alpha.model.dto.proxy;

import com.freedom.web.model.param.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 代理会话查询DTO
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(title = "ProxyConversationQueryDTO", description = "代理会话查询请求对象")
public class ProxyConversationQueryDTO extends BasePageRequest {

    @Schema(description = "会话ID列表")
    private Set<Long> ids;

    @Schema(description = "账号ID")
    private Long accountId;

    @Schema(description = "账号ID列表")
    private List<Long> accountIds;

    @Schema(description = "关键字查询，收发件人")
    private String keyword;

    @Schema(title = "外部会话唯一标识列表")
    private Set<String> externalConversationIds;

    @Schema(description = "最新消息时间开始")
    private LocalDateTime latestMessageTimeStart;

    @Schema(description = "最新消息时间结束")
    private LocalDateTime latestMessageTimeEnd;

    @Schema(description = "是否按最新消息时间倒序排序")
    private Boolean isOrderByLatestMessageTimeDesc;
}