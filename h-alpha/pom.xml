<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yaowu</groupId>
        <artifactId>h-alpha-parent</artifactId>
        <version>1.0.6</version>
    </parent>

    <groupId>com.yaowu</groupId>
    <artifactId>h-alpha</artifactId>

    <version>${project.parent.version}</version>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>
    <description>数字员工业务</description>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <flying-saucer.version>9.8.0</flying-saucer.version>
        <!--二方库-->
        <g-third-party-api.version>1.2.16</g-third-party-api.version>
        <h-passport-api.version>1.4.13</h-passport-api.version>
        <h-hera-api.version>2.1.60</h-hera-api.version>
        <g-notice-api.version>1.0.27</g-notice-api.version>
        <h-riskcontrol-api.version>2.0.1</h-riskcontrol-api.version>
        <h-melina-api.version>1.1.34</h-melina-api.version>
        <g-tag-system-api.version>1.0.13</g-tag-system-api.version>
        <h-customer-service-api.version>1.2.48</h-customer-service-api.version>
        <g-settle-api.version>1.1.12</g-settle-api.version>
    </properties>

    <dependencies>
        <!-- 子模块 -->
        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>h-alpha-api</artifactId>
        </dependency>

        <!-- 基础依赖 -->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--spring-cloud-starter-kubernetes-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-kubernetes-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-kubernetes-client-config</artifactId>
        </dependency>
        <!--openfeign-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--loadbalancer-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-kubernetes-client-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.4.0</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.2.0-jre</version> <!-- replace with the version you want -->
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.mvel/mvel2 -->
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>2.5.2.Final</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.50</version>
        </dependency>
        <!--nacos-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.3.0-RC</version>
        </dependency>
        <dependency>
            <groupId>com.github.jsonzou</groupId>
            <artifactId>jmockdata</artifactId>
            <version>4.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <!-- testing -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>
        <!-- PowerMock -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-object-storage-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <!-- HTML & PDF: FreeMaker, Flying-Saucer -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>${flying-saucer.version}</version>
        </dependency>
        <!-- Jsoup HTML parser -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.4</version>
        </dependency>


        <!-- 基础依赖 -->
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-web-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-web-base</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-biz-common</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-feign-config-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-tools-common</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-mybatisplus-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-redisson-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-redis-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-security-auth-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-security-resource-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-biz-common</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-invoke-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-biz-log-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-xxl-job-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-sms-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <!-- websocket -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- 二方库 -->
        <dependency>
            <groupId>com.genlian</groupId>
            <artifactId>g-third-party-api</artifactId>
            <version>${g-third-party-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>h-riskcontrol-api</artifactId>
            <version>${h-riskcontrol-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>h-melina-api</artifactId>
            <version>${h-melina-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>g-tag-system-api</artifactId>
            <version>${g-tag-system-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>h-customer-service-api</artifactId>
            <version>${h-customer-service-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>h-hera-api</artifactId>
            <version>${h-hera-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>springfox-swagger2</artifactId>
                    <groupId>io.springfox</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>springfox-boot-starter</artifactId>
                    <groupId>io.springfox</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join-boot-starter</artifactId>
            <version>1.4.3.1</version>
        </dependency>

        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-xxl-job-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>

        <!-- testing -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>
        <!-- PowerMock -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-object-storage-spring-boot-starter</artifactId>
            <version>${freedom.version}</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <!-- HTML & PDF: FreeMaker, Flying-Saucer -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>${flying-saucer.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yaowu.notice</groupId>
            <artifactId>g-notice-api</artifactId>
            <version>${g-notice-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-chatbot20220408</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-voicenavigator20180612</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.github.plexpt</groupId>
            <artifactId>chatgpt</artifactId>
            <version>4.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <!-- 字符串相似度 -->
        <dependency>
            <groupId>me.xdrop</groupId>
            <artifactId>fuzzywuzzy</artifactId>
            <version>1.4.0</version>
        </dependency>
        <!-- 智谱SDK -->
        <dependency>
            <groupId>cn.bigmodel.openapi</groupId>
            <artifactId>oapi-java-sdk</artifactId>
            <version>release-V4-2.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.yaowu</groupId>
            <artifactId>g-settle-api</artifactId>
            <version>${g-settle-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>springfox-swagger2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-mq-spring-boot-starter</artifactId>
        </dependency>

        <!-- gmail SDK-->
        <dependency>
            <groupId>com.google.api-client</groupId>
            <artifactId>google-api-client</artifactId>
            <version>2.0.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.oauth-client/google-oauth-client -->
        <dependency>
            <groupId>com.google.oauth-client</groupId>
            <artifactId>google-oauth-client</artifactId>
            <version>1.39.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.oauth-client/google-oauth-client-jetty -->
        <dependency>
            <groupId>com.google.oauth-client</groupId>
            <artifactId>google-oauth-client-jetty</artifactId>
            <version>1.39.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.apis</groupId>
            <artifactId>google-api-services-gmail</artifactId>
            <version>v1-rev20220404-2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-pubsub</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.cloud</groupId>
            <artifactId>google-cloud-storage-control</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/jakarta.mail/jakarta.mail-api -->
        <dependency>
            <groupId>jakarta.mail</groupId>
            <artifactId>jakarta.mail-api</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.ycloud</groupId>
            <artifactId>ycloud-sdk-java</artifactId>
            <version>1.15.3</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.resend</groupId>
            <artifactId>resend-java</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.freedom</groupId>
            <artifactId>freedom-mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.26</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <mainClass>com.yaowu.alpha.AlphaApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <annotationProcessorPaths>
                        <!--加入mapstruct后出现 找不到符号 符号: 方法 setXX 的解决方法-->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <!--本module不deploy到私库-->
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.codehaus.mojo</groupId>-->
            <!--                <artifactId>exec-maven-plugin</artifactId>-->
            <!--                <version>1.6.0</version>-->
            <!--                <executions>-->
            <!--                    &lt;!&ndash; 执行配置 &ndash;&gt;-->
            <!--                    <execution>-->
            <!--                        <id>write-properties</id> &lt;!&ndash; 执行标识 &ndash;&gt;-->
            <!--                        <phase>compile</phase> &lt;!&ndash; 编译阶段 &ndash;&gt;-->
            <!--                        <goals>-->
            <!--                            <goal>java</goal> &lt;!&ndash; 执行目标 &ndash;&gt;-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            &lt;!&ndash; 主类配置 &ndash;&gt;-->
            <!--                            <mainClass>com.yaowu.alpha.config.WritePropertiesFile</mainClass>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>

    <!-- 私库 -->
    <repositories>
        <!-- 使用阿里私库 -->
        <!--        <repository>-->
        <!--            <id>aliyun-central</id>-->
        <!--            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>-->
        <!--            <name>aliyun</name>-->
        <!--        </repository>-->
        <!-- nexus -->
        <repository>
            <id>nexus-central</id>
            <url>https://nexus3.yaowutech.cn/repository/maven-public/</url>
            <name>nexus</name>
            <!--            <releases>-->
            <!--                <enabled>true</enabled>-->
            <!--                <updatePolicy>always</updatePolicy>-->
            <!--            </releases>-->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>maven_central</id>
            <name>Maven Central</name>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>
    </repositories>
    <!-- 设置插件私库 -->
    <pluginRepositories>
        <!--        <pluginRepository>-->
        <!--            <id>aliyun-central</id>-->
        <!--            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>-->
        <!--            <name>aliyun</name>-->
        <!--        </pluginRepository>-->
        <pluginRepository>
            <id>nexus-central</id>
            <url>https://nexus3.yaowutech.cn/repository/maven-public/</url>
            <name>nexus</name>
        </pluginRepository>
    </pluginRepositories>


</project>
