# YCloud WhatsApp 工具类使用文档

## 概述

YCloud WhatsApp 工具类提供了发送 WhatsApp 消息的功能，包括模板消息和文本消息。该工具类基于 YCloud SDK 实现，支持 WhatsApp 业务平台的消息发送功能。

## 配置

在 `application.yml` 中添加以下配置：

```yaml
# YCloud配置
ycloud:
  api-key: YOUR_API_KEY  # YCloud API密钥
  base-path: https://api.ycloud.com/v2  # YCloud API基础路径
  default-whatsapp-number: YOUR_WHATSAPP_NUMBER  # 默认的WhatsApp发送号码
```

## 发送模板消息

模板消息是 WhatsApp 业务平台推荐的消息发送方式，需要先在 WhatsApp 业务平台创建并审核通过模板，然后才能发送。

### 示例代码

```java
@Autowired
private YCloudWhatsAppUtil yCloudWhatsAppUtil;

public void sendTemplateMessageExample() {
    // 创建模板消息DTO
    WhatsAppTemplateMessageDTO messageDTO = WhatsAppTemplateMessageDTO.builder()
        .to("8613812345678")  // 接收方WhatsApp号码
        .templateName("order_confirmation")  // 模板名称
        .languageCode("en_US")  // 语言代码
        .bodyParameters(Arrays.asList("ORDER-5555", "99 USD", "February 25, 2023"))  // 正文参数
        .build();
    
    // 发送模板消息
    String messageId = yCloudWhatsAppUtil.sendTemplateMessage(messageDTO);
    System.out.println("消息ID: " + messageId);
}
```

### 带图片的模板消息

```java
WhatsAppTemplateMessageDTO messageDTO = WhatsAppTemplateMessageDTO.builder()
    .to("8613812345678")
    .templateName("marketing_friday")
    .languageCode("en_US")
    .headerParameter("https://example.com/image.jpg")  // 图片URL
    .headerParameterType("image")  // 头部参数类型为图片
    .bodyParameters(Collections.singletonList("Joe"))  // 正文参数
    .build();

String messageId = yCloudWhatsAppUtil.sendTemplateMessage(messageDTO);
```

### 带按钮的模板消息

```java
WhatsAppTemplateMessageDTO messageDTO = WhatsAppTemplateMessageDTO.builder()
    .to("8613812345678")
    .templateName("marketing_friday")
    .languageCode("en_US")
    .bodyParameters(Collections.singletonList("Joe"))
    .buttonParameters(Arrays.asList(
        new WhatsAppTemplateMessageDTO.ButtonParameter(0, "quick_reply", "more_about_marketing_friday"),
        new WhatsAppTemplateMessageDTO.ButtonParameter(1, "quick_reply", "unsubscribe_marketing_notifications")
    ))
    .build();

String messageId = yCloudWhatsAppUtil.sendTemplateMessage(messageDTO);
```

## 发送文本消息

文本消息只能在用户主动联系企业后的24小时内发送，或者在用户回复企业模板消息后的24小时内发送。

### 示例代码

```java
@Autowired
private YCloudWhatsAppUtil yCloudWhatsAppUtil;

public void sendTextMessageExample() {
    // 创建文本消息DTO
    WhatsAppTextMessageDTO messageDTO = WhatsAppTextMessageDTO.builder()
        .to("8613812345678")  // 接收方WhatsApp号码
        .body("Hello, this is a text message!")  // 消息内容
        .previewUrl(true)  // 预览URL
        .build();
    
    // 发送文本消息
    String messageId = yCloudWhatsAppUtil.sendTextMessage(messageDTO);
    System.out.println("消息ID: " + messageId);
}
```

### 回复消息

```java
WhatsAppTextMessageDTO messageDTO = WhatsAppTextMessageDTO.builder()
    .to("8613812345678")
    .body("This is a reply to your message.")
    .replyToMessageId("wamid.BgNODYxN...")  // 回复的消息ID
    .build();

String messageId = yCloudWhatsAppUtil.sendTextMessage(messageDTO);
```

## API接口

工具类同时提供了REST API接口，可以通过HTTP请求发送WhatsApp消息。

### 发送模板消息

```
POST /api/v1/ycloud/whatsapp/template
Content-Type: application/json

{
  "to": "8613812345678",
  "templateName": "order_confirmation",
  "languageCode": "en_US",
  "bodyParameters": ["ORDER-5555", "99 USD", "February 25, 2023"]
}
```

### 发送文本消息

```
POST /api/v1/ycloud/whatsapp/text
Content-Type: application/json

{
  "to": "8613812345678",
  "body": "Hello, this is a text message!",
  "previewUrl": true
}
```

## 注意事项

1. 发送模板消息前，需要先在WhatsApp业务平台创建并审核通过模板。
2. 文本消息只能在用户主动联系企业后的24小时内发送，或者在用户回复企业模板消息后的24小时内发送。
3. 发送消息时需要确保接收方号码格式正确，包含国家代码，例如：8613812345678。
4. 配置文件中的API密钥和WhatsApp号码需要替换为实际的值。 