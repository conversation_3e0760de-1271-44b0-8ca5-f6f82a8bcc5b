# 通用智能体提取器设计文档

## 1. 概述

通用智能体提取器（GenericAgentExtractor）是一个基于大语言模型的数据提取框架，它能够从自然语言对话中提取结构化数据。与传统的提取器不同，通用提取器不依赖于特定的业务逻辑或数据结构，而是完全基于配置驱动，能够灵活适应各种业务场景。

### 1.1 设计目标

- **通用性**：能够处理各种类型的提取需求，无需为每种业务场景编写特定代码
- **可配置性**：通过配置定义提取字段、默认值和必填规则
- **易扩展性**：便于集成到现有系统，易于扩展新功能
- **高性能**：利用缓存机制提高提取效率
- **容错性**：处理异常情况，提供优雅的降级方案

### 1.2 主要特性

- 基于配置的字段提取
- 支持字段默认值设置
- 支持必填字段验证
- 支持批量消息提取
- 会话上下文管理
- 提取结果缓存
- 异常处理与日志记录

## 2. 系统架构

### 2.1 核心组件

通用提取器由以下核心组件组成：

1. **GenericAgentExtractor**：提取器核心类，负责协调各组件完成提取工作
2. **GenericAgentExtractorService**：业务服务层，提供对外接口，封装提取逻辑
3. **配置管理**：管理提取器配置信息，包括字段定义、默认值等
4. **会话管理**：管理LLM会话上下文，优化提取效果
5. **数据处理**：处理提取到的原始数据，进行验证、转换和存储

### 2.2 工作流程

![通用提取器工作流程](https://mermaid.ink/img/pako:eNqNksFqwzAMhl_F-NRCXyDd0WMPPfbUQSmOFyVWaluJHZJSQsm7T06aZmm3DQyS9P_6JVHZIXE0yLYUvJNFYZUYMkj40L6R3cXs9j4BNi0Kw3pwFLg6Yx2kpCfOeDUkO6wSJThWGnvUFATXaEJvlzEJIcuWuSFNQsw1l6VxL6LJCslmIXWr16SbOfnA-5_kMTk9IXnzYcNz3afqE_YuJHmZHNkzFZcHHp9lMXNXSHHrMdZ5HsqaNshaVFoZumDbLNpBRTGMJPyLuUIJdSNrN1F5gVPe0KZ0ZDcmLQ0o5PPrYrmoPO7RuknNMF9Gxw9TnIEF2nI1B_1h_nz1fbGhL7_qC8JjNKAXaRuP58zEwn9YXYZlDQeFNaafZmMPj3xhZ99sU8uG)

提取过程的主要步骤：

1. 接收聊天消息或消息列表
2. 检索提取器配置信息
3. 创建或获取LLM会话
4. 构建提取提示词
5. 请求LLM进行提取
6. 解析提取结果
7. 应用默认值
8. 验证提取结果完整性
9. 返回结构化数据

## 3. 核心组件详解

### 3.1 GenericAgentExtractor

GenericAgentExtractor 是提取器的核心实现类，负责主要的提取逻辑：

```java
public class GenericAgentExtractor {
    // 主要方法
    public Map<String, Object> extractData(ProxyChatMessage message, ProxyAccount proxyAccount);
    public Map<String, Object> extractDataFromMessages(List<AgentChatMessage> messages, ProxyAccount proxyAccount);
    public Map<String, Object> applyDefaultValues(Map<String, Object> extractedData, String extractorAppKey);
    public boolean isExtractionComplete(Map<String, Object> extractedData, String extractorAppKey);
    
    // 辅助方法
    private AgentSession createOrGetSession(String sessionKey);
    private List<AgentExtractorField> getExtractorFields(String extractorAppKey);
    private String buildExtractionPrompt(List<AgentExtractorField> fields, String messageContent);
    // ...
}
```

### 3.2 GenericAgentExtractorService

GenericAgentExtractorService 是面向业务的服务层，封装了提取逻辑，并处理提取结果：

```java
@Service
public class GenericAgentExtractorService {
    public BaseResult<String> processMessageExtraction(ProxyChatMessage message, ProxyAccount proxyAccount);
    public BaseResult<String> processMessagesExtraction(List<AgentChatMessage> messages, ProxyAccount proxyAccount, String userProxyId);
    private void processExtractedData(Map<String, Object> extractedData, ProxyAccount proxyAccount, String userProxyId, boolean isComplete);
    // ...
}
```

### 3.3 提取器配置

提取器配置定义了需要提取的字段、默认值和验证规则：

```json
{
  "fields": [
    {
      "name": "客户姓名",
      "code": "customerName",
      "description": "客户的姓名",
      "required": true,
      "defaultValue": null
    },
    {
      "name": "电话号码",
      "code": "phoneNumber",
      "description": "客户的联系电话",
      "required": true,
      "defaultValue": null
    },
    {
      "name": "意向区域",
      "code": "interestedArea",
      "description": "客户感兴趣的区域",
      "required": false,
      "defaultValue": "不限"
    }
  ]
}
```

## 4. 使用指南

### 4.1 提取器配置

提取器配置是通用提取器的核心，正确配置提取器字段对于提取效果至关重要：

1. **字段命名**：使用清晰、具体的名称，避免歧义
2. **字段代码**：使用驼峰命名法，与系统中的变量名保持一致
3. **字段描述**：详细描述字段的含义和提取要求
4. **必填设置**：明确哪些字段是必须提取的
5. **默认值**：为可选字段提供合理的默认值

### 4.2 提取器提示模板

提示模板决定了提取器的提取效果，应包含以下要素：

1. **角色定义**：明确LLM的角色（如数据提取专家）
2. **任务描述**：清晰描述提取任务
3. **字段说明**：列出所有需要提取的字段及其描述
4. **输出格式**：指定JSON格式输出
5. **约束条件**：明确提取规则和限制

示例提示模板：

```
你是一个专业的数据提取专家。请从以下对话内容中提取客户信息，包括：
{{#each fields}}
- {{name}}：{{description}}
{{/each}}

请按照以下规则进行提取：
1. 只提取对话中明确提及的信息
2. 不确定的字段请留空，不要猜测
3. 提取结果必须是有效的JSON格式
4. 对于没有提及的字段，你可以使用默认值（如果有）

对话内容：
{{message}}

请输出JSON格式：
```

### 4.3 集成到现有系统

#### 4.3.1 添加提取器配置

首先需要创建提取器配置，包括字段定义和提示模板。

#### 4.3.2 配置代理账号

将提取器配置绑定到代理账号：

```java
ProxyAccount proxyAccount = proxyAccountService.getById(accountId);
proxyAccount.setExtractorAgentAppKey("generic_extractor_key");
proxyAccountService.updateById(proxyAccount);
```

#### 4.3.3 调用提取服务

在消息处理流程中调用提取服务：

```java
@Autowired
private GenericAgentExtractorService extractorService;

// 处理单条消息
BaseResult<String> result = extractorService.processMessageExtraction(message, proxyAccount);

// 处理多条消息
BaseResult<String> result = extractorService.processMessagesExtraction(messageList, proxyAccount, userProxyId);
```

## 5. 最佳实践

### 5.1 提高提取准确率

1. **优化提示模板**：根据业务场景定制提示模板
2. **增加示例**：在提示模板中提供示例，帮助LLM理解任务
3. **分段提取**：对于复杂会话，可以分段提取，然后合并结果
4. **结果验证**：实现业务层面的结果验证逻辑
5. **人工干预**：对于低置信度的提取结果，设置人工审核机制

### 5.2 性能优化

1. **会话复用**：对同一用户的连续提取复用会话
2. **配置缓存**：缓存提取器配置，减少数据库查询
3. **批量提取**：优先使用批量提取接口处理多条消息
4. **异步处理**：对非实时场景使用异步提取
5. **结果缓存**：缓存已提取的结果，避免重复提取

### 5.3 错误处理

1. **日志详细记录**：记录提取过程的关键信息
2. **优雅降级**：提取失败时有备选处理方案
3. **重试机制**：对于可恢复的错误实现重试逻辑
4. **监控告警**：设置提取成功率监控和告警

## 6. 扩展与优化方向

### 6.1 当前限制

1. 依赖大语言模型的提取能力
2. 提取复杂结构化数据时可能不够精确
3. 无法处理多模态内容（如图片、语音）

### 6.2 未来扩展方向

1. **多模态支持**：集成图像识别、语音转文字等能力
2. **提取策略优化**：支持多轮提取和交互式提取
3. **自适应学习**：根据人工反馈优化提取逻辑
4. **领域特化模型**：针对特定领域训练专用模型
5. **多语言支持**：扩展支持多语言提取能力

## 7. 总结

通用智能体提取器是一个灵活、强大的数据提取框架，通过配置驱动的方式实现了业务逻辑与提取逻辑的解耦。它能够适应各种业务场景，显著降低开发成本和维护难度。

合理使用通用提取器，可以实现高效、准确的数据提取，为智能客服、销售线索收集、需求分析等业务场景提供强有力的支持。 