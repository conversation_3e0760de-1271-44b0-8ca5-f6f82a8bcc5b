实现回复邮件接口，注意代码规范
别的系统代码文件参考（仅供参考，需要结合当前系统的设计进行必要改造），其中调用邮件基础能力的代码可先空置

@Data
@Schema(description = "邮件回复DTO")
public class MailReplyDTO {

    @NotNull(message = "邮件ID不能为空")
    @Schema(description = "邮件ID", example = "123456", required = true)
    private Long mailId;

    @NotNull(message = "会话ID不能为空")
    @Schema(description = "会话ID", example = "789012", required = true)
    private Long conversationId;

    @NotBlank(message = "回复内容不能为空")
    @Schema(description = "回复内容", example = "<p>这是回复内容</p>", required = true)
    private String content;

    @Schema(description = "内联附件列表")
    private List<MailAttachment> inlineAttachments;

    @Schema(description = "附件列表")
    private List<MailAttachment> attachments;

    @Schema(description = "是否包含原始邮件内容（引用原文）", example = "true")
    private Boolean includeOriginal = true;

    @Schema(description = "聊天消息ID", example = "1")
    private Long chatMessageId;
}

@Override
    public Boolean replyMail(MailReplyDTO replyDTO) {
        // 校验参数，确保至少提供mailId或conversationId中的一个
        if (replyDTO.getMailId() == null && replyDTO.getConversationId() == null) {
            throw new BusinessException("邮件ID和会话ID不能同时为空，请至少提供一个");
        }

        ProxyChatMessage referenceMessage = null;
        // 1. 获取原始邮件信息作为参考
        if (replyDTO.getMailId() != null) {
            referenceMessage = Optional.ofNullable(proxyChatMessageService.getById(replyDTO.getMailId()))
                    .orElseThrow(() -> new BusinessException("找不到原始邮件"));
        }

        // 2. 获取会话信息
        ProxyConversation conversation;
        if (replyDTO.getConversationId() == null && referenceMessage != null) {
            replyDTO.setConversationId(Long.valueOf(referenceMessage.getSessionProxyId()));
        }

        conversation = Optional.ofNullable(proxyConversationService.getById(replyDTO.getConversationId()))
                .orElseThrow(() -> new BusinessException("找不到会话信息"));

        // 3. 获取邮箱账号配置
        ProxyAccountConfig accountConfig = Optional.ofNullable(proxyAccountConfigService.getById(conversation.getAccountId()))
                .orElseThrow(() -> new BusinessException("找不到邮箱账号配置"));

        // 4. 获取会话中最新的对方发送的邮件作为回复目标
        ProxyChatMessage originalMessage = findLatestOpponentMessage(conversation.getId().toString(), accountConfig.getEmail());
        if (originalMessage == null) {
            // 如果找不到对方发送的邮件，则使用参考邮件
            if (referenceMessage != null) {
                originalMessage = referenceMessage;
            } else {
                // 如果既没有参考邮件又找不到对方发送的邮件，则无法回复
                throw new BusinessException("无法确定回复目标，请提供有效的邮件ID");
            }
        }

        // 5. 创建邮件协议上下文
        EmailProtocolContext emailContext = createEmailProtocolContext(originalMessage, conversation, replyDTO);

        // 6. 创建消息请求DTO
        MessageRequestDTO requestDTO = createMessageRequestDTO(conversation, accountConfig, originalMessage, replyDTO, emailContext);

        // 7. 获取消息发送能力并发送
        IMessageSendCapability messageSendCapability = capabilityFactory.getCapability(IMessageSendCapability.class, requestDTO);
        MessageSendResponseVO response = messageSendCapability.sendMessage(requestDTO);

        // 8. 处理响应结果
        if (response == null || !response.getSuccess()) {
            log.error("回复邮件失败，响应结果: {}", response);
            throw new BusinessException("邮件发送失败");
        }

        // 9. 记录回复邮件
        saveReplyMessage(replyDTO, originalMessage, conversation, accountConfig, response.getMessageId());
        return true;
    }


    /**
     * 查找会话中最新的对方发送的邮件
     *
     * @param sessionProxyId   会话ID
     * @param currentUserEmail 当前用户邮箱
     * @return 最新的对方发送的邮件
     */
    @Override
    public ProxyChatMessage findLatestOpponentMessage(String sessionProxyId, String currentUserEmail) {
        // 构建查询条件
        ProxyChatMsgQuery query = new ProxyChatMsgQuery();
        query.setSessionProxyIds(Set.of(sessionProxyId));
        query.setIsOrderByMessageTimestampDesc(true); // 按时间戳降序，获取最新的消息

        // 获取会话中的所有消息
        List<ProxyChatMessage> messages = proxyChatMessageService.listByCondition(query);

        // 过滤出对方发送的邮件（发送者不是当前用户邮箱）
        return messages.stream()
                .filter(msg -> !currentUserEmail.equalsIgnoreCase(msg.getChatUserProxyId()))
                .findFirst()
                .orElse(null);
    }

        /**
     * 创建邮件协议上下文
     */
    public EmailProtocolContext createEmailProtocolContext(ProxyChatMessage originalMessage,
                                                           ProxyConversation conversation, MailReplyDTO replyDTO) {
        // 获取原始邮件内容
        String originalContent = getOriginalContent(originalMessage);

        // 创建邮件协议上下文
        EmailProtocolContext emailContext = new EmailProtocolContext();
        // 主题
        emailContext.setSubject(conversation.getConversationSubject());
        // 设置是否为HTML内容
        emailContext.setHtmlContent(true);
        // 设置回复的消息ID
        emailContext.setInReplyToMessageId(originalMessage.getExternalMessageId());
        // 设置是否引用原始内容
        emailContext.setQuoteOriginal(replyDTO.getIncludeOriginal() != null && replyDTO.getIncludeOriginal());
        // 设置原始内容
        emailContext.setOriginalContent(originalContent);
        // 自动添加Re:前缀
        emailContext.setAutoAddRePrefix(true);

        // 处理附件
        if (replyDTO.getAttachments() != null && !replyDTO.getAttachments().isEmpty()) {
            List<EmailAttachment> attachments = convertToCapabilityAttachments(replyDTO.getAttachments(), false);
            emailContext.setAttachments(attachments);
        }

        // 处理内联附件
        if (replyDTO.getInlineAttachments() != null && !replyDTO.getInlineAttachments().isEmpty()) {
            for (MailAttachment inlineAttachment : replyDTO.getInlineAttachments()) {
                // 从URL下载附件内容
                byte[] content = downloadAttachment(inlineAttachment.getId());

                // 创建EmailAttachment对象
                EmailAttachment emailAttachment = EmailAttachment.fromByteArray(
                                content,
                                inlineAttachment.getName())
                        .withContentType(inlineAttachment.getContentType() != null ? inlineAttachment.getContentType()
                                : "application/octet-stream")
                        .withContentId(inlineAttachment.getId())
                        .withDisposition("inline");

                // 添加到内联附件映射中，使用contentId作为key
                emailContext.getInlineAttachments().put(inlineAttachment.getId(), emailAttachment);
            }
        }

        return emailContext;
    }

        /**
     * 创建消息请求DTO
     */
    public MessageRequestDTO createMessageRequestDTO(
            ProxyConversation conversation,
            ProxyAccountConfig accountConfig,
            ProxyChatMessage originalMessage,
            MailReplyDTO replyDTO,
            EmailProtocolContext emailContext) {

        MessageRequestDTO requestDTO = new MessageRequestDTO();
        // 设置渠道类型
        requestDTO.setChatChannelType(conversation.getChannelType());
        // 设置发送者账号ID
        requestDTO.setSenderAccountId(accountConfig.getId());
        // 设置接收者账号ID列表
        // 判断原始邮件的发送者是否是当前账号，如果是则回复给接收者，否则回复给发送者
        String recipientEmail;
        if (accountConfig.getEmail().equalsIgnoreCase(originalMessage.getChatUserProxyId())) {
            // 如果原始邮件的发送者是当前账号，则回复给接收者
            recipientEmail = originalMessage.getToUserProxyId();
        } else {
            // 如果原始邮件的发送者不是当前账号，则回复给发送者
            recipientEmail = originalMessage.getChatUserProxyId();
        }
        emailContext.setToAddresses(Collections.singletonList(recipientEmail));
        // 设置内容
        requestDTO.setContent(replyDTO.getContent());
        // 设置协议上下文
        requestDTO.setProtocolContext(emailContext);

        return requestDTO;
    }


        /**
     * 保存回复邮件记录
     */
    public void saveReplyMessage(
            MailReplyDTO replyDTO,
            ProxyChatMessage originalMessage,
            ProxyConversation conversation,
            ProxyAccountConfig accountConfig,
            String externalMessageId) {

        ProxyChatMessage replyMessage = new ProxyChatMessage();

        // 设置基本信息
        replyMessage.setAccountId(accountConfig.getId());
        replyMessage.setAgentSessionId(originalMessage.getAgentSessionId());
        replyMessage.setSessionProxyId(conversation.getId().toString());
        replyMessage.setTenantId(originalMessage.getTenantId());
        replyMessage.setUserId(originalMessage.getUserId());

        // 设置发送方和接收方
        replyMessage.setChatUserProxyId(accountConfig.getEmail());
        replyMessage.setChatUserName(originalMessage.getToUserName());
        replyMessage.setToUserProxyId(originalMessage.getChatUserProxyId());
        replyMessage.setToUserName(originalMessage.getChatUserName());

        // 处理内容和附件信息
        // 优先设置普通文本内容
        replyMessage.setContent(replyDTO.getContent());
        replyMessage.setMessageType(1); // 1-文本
        replyMessage.setReceiveFlag(false); // 0=发送
        replyMessage.setReplyType(1); // 1=代理
        replyMessage.setGroupFlag(false); // 0-否
        replyMessage.setMessageTimestamp(TimezoneUtils.getCurrentUTCTimestamp());
        replyMessage.setExternalMessageId(externalMessageId);

        // 如果有附件或者HTML内容，需要创建ProxyEmailChatMessageContent处理
        boolean hasAttachments = replyDTO.getAttachments() != null && !replyDTO.getAttachments().isEmpty();
        boolean hasInlineAttachments = replyDTO.getInlineAttachments() != null
                && !replyDTO.getInlineAttachments().isEmpty();
        ProxyEmailChatMessageContent emailContent = new ProxyEmailChatMessageContent();
        emailContent.setAgentChatMessageId(replyDTO.getChatMessageId());
        // 设置HTML内容
        emailContent.setHtmlContent(replyDTO.getContent());
        if (hasAttachments || hasInlineAttachments) {
            // 只有当有普通附件时才设置
            if (hasAttachments) {
                emailContent.setAttachments(replyDTO.getAttachments());
            }
            // 只有当有内联附件时才设置
            if (hasInlineAttachments) {
                emailContent.setInlineAttachments(replyDTO.getInlineAttachments());
            }
        }

        String rawContent = JSONUtil.toJsonStr(emailContent);
        replyMessage.setContent(rawContent);
        replyMessage.setRawContent(rawContent);
        replyMessage.setRawProxyData(rawContent);

        // 保存消息记录
        proxyChatMessageService.save(replyMessage);
    }