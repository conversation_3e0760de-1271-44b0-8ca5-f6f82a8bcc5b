# Resend Webhook 验证指南

本指南介绍如何在H-Alpha项目中使用Resend webhook的签名验证功能。

## 概述

Resend使用Svix来管理webhook，确保webhook的安全性和完整性。我们实现了完整的签名验证机制来验证来自Resend的webhook请求。

## 功能特性

- ✅ 完整的webhook签名验证
- ✅ 时间戳验证防止重放攻击  
- ✅ 常数时间比较防止时序攻击
- ✅ 支持多版本签名格式
- ✅ 详细的错误处理和日志记录
- ✅ 可复用的工具类
- ✅ 完整的单元测试

## 配置

在应用配置文件中设置webhook secret：

```yaml
resend:
  webhook:
    secret: whsec_your_webhook_secret_here
```

webhook secret可以从Resend Dashboard获取。

## 使用方法

### 1. 接收Webhook

系统已经配置了webhook接收端点：

```
POST /v1/marketing-email/webhook/resend/hook
```

### 2. 验证Headers

Resend发送的webhook包含以下验证headers：

- `svix-id`: 唯一消息标识符
- `svix-timestamp`: Unix时间戳
- `svix-signature`: Base64编码的签名列表

### 3. 签名验证流程

1. **提取Headers**: 从HTTP请求中获取验证所需的headers
2. **构造签名内容**: 格式为 `${svix_id}.${svix_timestamp}.${payload}`
3. **计算期望签名**: 使用HMAC-SHA256和webhook secret
4. **验证签名**: 常数时间比较防止时序攻击
5. **验证时间戳**: 检查时间戳是否在5分钟容差范围内

### 4. 使用工具类

```java
// 创建验证器
ResendWebhookVerifier verifier = new ResendWebhookVerifier(webhookSecret);

// 简单验证
boolean isValid = verifier.verifySignature(svixId, svixTimestamp, payload, svixSignature);

// 详细验证（推荐）
ResendWebhookVerifier.VerificationResult result = verifier.verifyWithDetails(
    svixId, svixTimestamp, payload, svixSignature
);

if (!result.isValid()) {
    log.warn("验证失败: {}", result.getMessage());
}
```

## 错误处理

系统会返回相应的HTTP状态码：

- `200 OK`: 验证成功
- `400 Bad Request`: 缺少必要的headers
- `401 Unauthorized`: 签名无效或时间戳过期
- `500 Internal Server Error`: 服务器内部错误

## 安全考虑

### 时间戳验证
- 默认容差：5分钟
- 防止重放攻击
- 要求服务器时钟同步

### 签名验证
- 使用HMAC-SHA256算法
- 常数时间比较防止时序攻击
- 支持多版本签名格式

### 敏感信息处理
- webhook secret安全存储
- 不在日志中记录敏感信息
- 使用原始请求体进行验证

## 测试

### 运行单元测试

```bash
mvn test -Dtest=MarketingEmailWebhookControllerTest
```

### 测试覆盖场景

- ✅ 有效签名验证
- ✅ 缺少headers处理
- ✅ 无效签名处理
- ✅ 时间戳过期处理
- ✅ 多版本签名支持

### 手动测试

可以使用工具类生成测试签名：

```java
ResendWebhookVerifier verifier = new ResendWebhookVerifier("whsec_test_secret");
String signature = verifier.generateSignature("msg_id", "1234567890", "{\"test\":\"data\"}");
```

## 调试

### 启用调试日志

```yaml
logging:
  level:
    com.yaowu.alpha.controller.v1.email: DEBUG
    com.yaowu.alpha.utils.resend: DEBUG
```

### 常见问题

1. **签名验证失败**
   - 检查webhook secret是否正确
   - 确保使用原始请求体
   - 验证headers是否完整

2. **时间戳过期**
   - 检查服务器时钟是否同步
   - 确认时间戳格式正确（Unix秒）

3. **Headers缺失**
   - 确认Resend配置正确
   - 检查请求是否来自Resend

## 扩展使用

### 自定义时间戳容差

```java
// 设置10分钟容差
ResendWebhookVerifier verifier = new ResendWebhookVerifier(webhookSecret, 600);
```

### 集成到其他控制器

```java
@Autowired
private ResendWebhookVerifier webhookVerifier;

public void handleWebhook(String payload, HttpServletRequest request) {
    String svixId = request.getHeader("svix-id");
    String svixTimestamp = request.getHeader("svix-timestamp");
    String svixSignature = request.getHeader("svix-signature");
    
    if (webhookVerifier.verifySignature(svixId, svixTimestamp, payload, svixSignature)) {
        // 处理webhook
    }
}
```

## 参考资料

- [Resend Webhook 文档](https://resend.com/docs/dashboard/webhooks/verify-webhooks-requests)
- [Svix 验证文档](https://docs.svix.com/receiving/verifying-payloads/how-manual)
- [HMAC-SHA256 规范](https://tools.ietf.org/html/rfc2104)

## 更新日志

- **2025-01-19**: 初始实现
  - 完整的签名验证机制
  - 可复用的工具类
  - 完整的单元测试覆盖 